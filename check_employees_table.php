<?php
/**
 * Check Employees Table
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

// Start HTML output
echo "<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>فحص جدول الموظفين</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #0066cc; margin-top: 30px; }
        p { margin: 10px 0; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .back-link { display: inline-block; margin-top: 20px; padding: 10px 15px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>فحص جدول الموظفين</h1>";

// Check employees table structure
echo "<h2>هيكل جدول الموظفين</h2>";
try {
    $columnsStmt = $pdo->query("DESCRIBE employees");
    $columns = $columnsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>أعمدة جدول الموظفين:</p>";
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
    // Check if status column exists
    $statusExists = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'status') {
            $statusExists = true;
            break;
        }
    }
    
    if ($statusExists) {
        echo "<p class='success'>✓ عمود status موجود في جدول الموظفين</p>";
    } else {
        echo "<p class='warning'>⚠️ عمود status غير موجود في جدول الموظفين</p>";
        
        // Add status column
        try {
            $pdo->exec("ALTER TABLE employees ADD COLUMN status ENUM('active', 'inactive') NOT NULL DEFAULT 'active' AFTER current_grade");
            echo "<p class='success'>✓ تم إضافة عمود status إلى جدول الموظفين بنجاح</p>";
        } catch (PDOException $e) {
            echo "<p class='error'>خطأ في إضافة عمود status: " . $e->getMessage() . "</p>";
        }
    }
} catch (PDOException $e) {
    echo "<p class='error'>خطأ في جلب هيكل جدول الموظفين: " . $e->getMessage() . "</p>";
}

// Get employees data
echo "<h2>بيانات الموظفين</h2>";
try {
    $employeesStmt = $pdo->query("SELECT id, employee_number, full_name, current_grade FROM employees LIMIT 10");
    $employees = $employeesStmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($employees) > 0) {
        echo "<p>عدد الموظفين (عرض أول 10 فقط): " . count($employees) . "</p>";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>الرقم الوظيفي</th><th>الاسم الكامل</th><th>الدرجة الحالية</th></tr>";
        
        foreach ($employees as $employee) {
            echo "<tr>";
            echo "<td>" . $employee['id'] . "</td>";
            echo "<td>" . $employee['employee_number'] . "</td>";
            echo "<td>" . $employee['full_name'] . "</td>";
            echo "<td>" . $employee['current_grade'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ لا يوجد موظفين في قاعدة البيانات</p>";
    }
} catch (PDOException $e) {
    echo "<p class='error'>خطأ في جلب بيانات الموظفين: " . $e->getMessage() . "</p>";
}

// Add links to navigate
echo "
    <div style='margin-top: 30px;'>
        <a href='generate_notifications.php' class='back-link'>العودة إلى صفحة توليد التنبيهات</a>
    </div>
</body>
</html>";
?>
