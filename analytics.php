<?php
/**
 * Advanced Analytics Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();

// Get filter parameters
$year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');
$department = isset($_GET['department']) ? (int)$_GET['department'] : 0;

// Initialize variables with default values
$departments = [];
$totalEmployees = 0;
$employeesByGrade = [];
$employeesByEducation = [];
$allowancesByMonth = [];
$promotionsByMonth = [];
$appreciationByType = [];
$departmentStats = [];
$upcomingAllowances = [
    'current' => 0,
    'within30Days' => 0,
    'within60Days' => 0,
    'within90Days' => 0,
    'beyond90Days' => 0
];
$upcomingPromotions = [
    'current' => 0,
    'within30Days' => 0,
    'within60Days' => 0,
    'within90Days' => 0,
    'beyond90Days' => 0
];

// Flag to check if we're using dummy data
$usingDummyData = false;

try {
    // Check if tables exist
    $tablesExist = true;

    // Check departments table
    $deptTableCheck = $pdo->query("SHOW TABLES LIKE 'departments'");
    if ($deptTableCheck->rowCount() == 0) {
        $tablesExist = false;
        $usingDummyData = true;
        // Create dummy departments data
        $departments = [
            ['id' => 1, 'name' => 'قسم الموارد البشرية'],
            ['id' => 2, 'name' => 'قسم تكنولوجيا المعلومات'],
            ['id' => 3, 'name' => 'قسم المالية'],
            ['id' => 4, 'name' => 'قسم الإدارة']
        ];
    } else {
        // Get departments for filter
        $deptStmt = $pdo->query("SELECT id, name FROM departments ORDER BY name ASC");
        $departments = $deptStmt->fetchAll();
    }

    // Check employees table
    $empTableCheck = $pdo->query("SHOW TABLES LIKE 'employees'");
    if ($empTableCheck->rowCount() == 0) {
        $tablesExist = false;
        $usingDummyData = true;
        // Create dummy data
        $totalEmployees = 100;

        // Dummy employees by grade
        $employeesByGrade = [
            ['current_grade' => 1, 'count' => 5],
            ['current_grade' => 2, 'count' => 8],
            ['current_grade' => 3, 'count' => 12],
            ['current_grade' => 4, 'count' => 15],
            ['current_grade' => 5, 'count' => 20],
            ['current_grade' => 6, 'count' => 15],
            ['current_grade' => 7, 'count' => 10],
            ['current_grade' => 8, 'count' => 8],
            ['current_grade' => 9, 'count' => 5],
            ['current_grade' => 10, 'count' => 2]
        ];

        // Dummy employees by education
        $employeesByEducation = [
            ['name' => 'بكالوريوس', 'count' => 45],
            ['name' => 'دبلوم', 'count' => 30],
            ['name' => 'ماجستير', 'count' => 15],
            ['name' => 'دكتوراه', 'count' => 10]
        ];

        // Dummy department stats
        $departmentStats = [
            [
                'id' => 1,
                'name' => 'قسم الموارد البشرية',
                'total_employees' => 25,
                'eligible_allowances' => 5,
                'eligible_promotions' => 3,
                'avg_years_service' => 8,
                'total_appreciation' => 15
            ],
            [
                'id' => 2,
                'name' => 'قسم تكنولوجيا المعلومات',
                'total_employees' => 30,
                'eligible_allowances' => 7,
                'eligible_promotions' => 4,
                'avg_years_service' => 6,
                'total_appreciation' => 20
            ],
            [
                'id' => 3,
                'name' => 'قسم المالية',
                'total_employees' => 20,
                'eligible_allowances' => 4,
                'eligible_promotions' => 2,
                'avg_years_service' => 10,
                'total_appreciation' => 12
            ],
            [
                'id' => 4,
                'name' => 'قسم الإدارة',
                'total_employees' => 25,
                'eligible_allowances' => 6,
                'eligible_promotions' => 3,
                'avg_years_service' => 12,
                'total_appreciation' => 18
            ]
        ];

        // Dummy upcoming data
        $upcomingAllowances = [
            'current' => 8,
            'within30Days' => 12,
            'within60Days' => 15,
            'within90Days' => 10,
            'beyond90Days' => 25
        ];

        $upcomingPromotions = [
            'current' => 5,
            'within30Days' => 8,
            'within60Days' => 10,
            'within90Days' => 7,
            'beyond90Days' => 15
        ];
    } else {
        // Get real data
        // Total employees
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM employees");
        $totalEmployees = $stmt->fetch()['count'];

        // Employees by grade
        $gradeStmt = $pdo->query("
            SELECT current_grade, COUNT(*) as count
            FROM employees
            GROUP BY current_grade
            ORDER BY current_grade ASC
        ");
        $employeesByGrade = $gradeStmt->fetchAll();

        // Check education_levels table
        $eduTableCheck = $pdo->query("SHOW TABLES LIKE 'education_levels'");
        if ($eduTableCheck->rowCount() > 0) {
            // Employees by education level
            $eduStmt = $pdo->query("
                SELECT el.name, COUNT(e.id) as count
                FROM employees e
                JOIN education_levels el ON e.education_level_id = el.id
                GROUP BY el.id, el.name
                ORDER BY COUNT(e.id) DESC
            ");
            $employeesByEducation = $eduStmt->fetchAll();
        } else {
            // Dummy education data
            $employeesByEducation = [
                ['name' => 'بكالوريوس', 'count' => 45],
                ['name' => 'دبلوم', 'count' => 30],
                ['name' => 'ماجستير', 'count' => 15],
                ['name' => 'دكتوراه', 'count' => 10]
            ];
        }

        // Department statistics
        if ($deptTableCheck->rowCount() > 0) {
            $deptStatsStmt = $pdo->query("
                SELECT
                    d.id,
                    d.name,
                    COUNT(e.id) as total_employees,
                    SUM(CASE WHEN e.allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . " AND e.next_allowance_date <= CURDATE() THEN 1 ELSE 0 END) as eligible_allowances,
                    SUM(CASE WHEN e.next_promotion_date <= CURDATE() THEN 1 ELSE 0 END) as eligible_promotions,
                    AVG(e.years_of_service) as avg_years_service,
                    0 as total_appreciation
                FROM departments d
                LEFT JOIN employees e ON d.id = e.department_id
                GROUP BY d.id, d.name
                ORDER BY d.name ASC
            ");
            $departmentStats = $deptStatsStmt->fetchAll();

            // Check if appreciation_letters table exists
            $appTableCheck = $pdo->query("SHOW TABLES LIKE 'appreciation_letters'");
            if ($appTableCheck->rowCount() > 0) {
                // Update total_appreciation for each department
                foreach ($departmentStats as &$dept) {
                    $appStmt = $pdo->prepare("
                        SELECT COUNT(*) as count
                        FROM appreciation_letters a
                        JOIN employees e ON a.employee_id = e.id
                        WHERE e.department_id = :dept_id
                    ");
                    $appStmt->execute([':dept_id' => $dept['id']]);
                    $appCount = $appStmt->fetch();
                    $dept['total_appreciation'] = $appCount ? $appCount['count'] : 0;
                }
            }
        } else {
            // Dummy department stats
            foreach ($departments as $dept) {
                $deptEmp = rand(10, 30);
                $departmentStats[] = [
                    'id' => $dept['id'],
                    'name' => $dept['name'],
                    'total_employees' => $deptEmp,
                    'eligible_allowances' => rand(1, 5),
                    'eligible_promotions' => rand(1, 3),
                    'avg_years_service' => rand(3, 15),
                    'total_appreciation' => rand(5, 20)
                ];
            }
        }

        // Upcoming allowances and promotions
        $upcomingAllowancesStmt = $pdo->query("
            SELECT
                SUM(CASE WHEN next_allowance_date <= CURDATE() THEN 1 ELSE 0 END) as current,
                SUM(CASE WHEN next_allowance_date > CURDATE() AND next_allowance_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as within30Days,
                SUM(CASE WHEN next_allowance_date > DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND next_allowance_date <= DATE_ADD(CURDATE(), INTERVAL 60 DAY) THEN 1 ELSE 0 END) as within60Days,
                SUM(CASE WHEN next_allowance_date > DATE_ADD(CURDATE(), INTERVAL 60 DAY) AND next_allowance_date <= DATE_ADD(CURDATE(), INTERVAL 90 DAY) THEN 1 ELSE 0 END) as within90Days,
                SUM(CASE WHEN next_allowance_date > DATE_ADD(CURDATE(), INTERVAL 90 DAY) THEN 1 ELSE 0 END) as beyond90Days
            FROM employees
            WHERE allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
        ");
        $upcomingAllowances = $upcomingAllowancesStmt->fetch();

        $upcomingPromotionsStmt = $pdo->query("
            SELECT
                SUM(CASE WHEN next_promotion_date <= CURDATE() THEN 1 ELSE 0 END) as current,
                SUM(CASE WHEN next_promotion_date > CURDATE() AND next_promotion_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as within30Days,
                SUM(CASE WHEN next_promotion_date > DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND next_promotion_date <= DATE_ADD(CURDATE(), INTERVAL 60 DAY) THEN 1 ELSE 0 END) as within60Days,
                SUM(CASE WHEN next_promotion_date > DATE_ADD(CURDATE(), INTERVAL 60 DAY) AND next_promotion_date <= DATE_ADD(CURDATE(), INTERVAL 90 DAY) THEN 1 ELSE 0 END) as within90Days,
                SUM(CASE WHEN next_promotion_date > DATE_ADD(CURDATE(), INTERVAL 90 DAY) THEN 1 ELSE 0 END) as beyond90Days
            FROM employees
        ");
        $upcomingPromotions = $upcomingPromotionsStmt->fetch();
    }

    // Check allowances table
    $allowancesTableCheck = $pdo->query("SHOW TABLES LIKE 'allowances'");
    if ($allowancesTableCheck->rowCount() > 0) {
        // Allowances by month
        $allowancesStmt = $pdo->prepare("
            SELECT MONTH(allowance_date) as month, COUNT(*) as count
            FROM allowances
            WHERE YEAR(allowance_date) = :year
            GROUP BY MONTH(allowance_date)
            ORDER BY MONTH(allowance_date) ASC
        ");
        $allowancesStmt->execute([':year' => $year]);
        $allowancesByMonth = $allowancesStmt->fetchAll();
    } else {
        $usingDummyData = true;
        // Dummy allowances data
        $allowancesByMonth = [
            ['month' => 1, 'count' => 5],
            ['month' => 2, 'count' => 7],
            ['month' => 3, 'count' => 10],
            ['month' => 4, 'count' => 8],
            ['month' => 5, 'count' => 12],
            ['month' => 6, 'count' => 15],
            ['month' => 7, 'count' => 9],
            ['month' => 8, 'count' => 6],
            ['month' => 9, 'count' => 8],
            ['month' => 10, 'count' => 11],
            ['month' => 11, 'count' => 7],
            ['month' => 12, 'count' => 5]
        ];
    }

    // Check promotions table
    $promotionsTableCheck = $pdo->query("SHOW TABLES LIKE 'promotions'");
    if ($promotionsTableCheck->rowCount() > 0) {
        // Promotions by month
        $promotionsStmt = $pdo->prepare("
            SELECT MONTH(promotion_date) as month, COUNT(*) as count
            FROM promotions
            WHERE YEAR(promotion_date) = :year
            GROUP BY MONTH(promotion_date)
            ORDER BY MONTH(promotion_date) ASC
        ");
        $promotionsStmt->execute([':year' => $year]);
        $promotionsByMonth = $promotionsStmt->fetchAll();
    } else {
        $usingDummyData = true;
        // Dummy promotions data
        $promotionsByMonth = [
            ['month' => 1, 'count' => 2],
            ['month' => 2, 'count' => 3],
            ['month' => 3, 'count' => 5],
            ['month' => 4, 'count' => 4],
            ['month' => 5, 'count' => 6],
            ['month' => 6, 'count' => 8],
            ['month' => 7, 'count' => 5],
            ['month' => 8, 'count' => 3],
            ['month' => 9, 'count' => 4],
            ['month' => 10, 'count' => 6],
            ['month' => 11, 'count' => 3],
            ['month' => 12, 'count' => 2]
        ];
    }

    // Check appreciation_letters table
    $appreciationTableCheck = $pdo->query("SHOW TABLES LIKE 'appreciation_letters'");
    if ($appreciationTableCheck->rowCount() > 0) {
        // Appreciation letters by type
        $appreciationStmt = $pdo->prepare("
            SELECT issuer, COUNT(*) as count
            FROM appreciation_letters
            WHERE YEAR(letter_date) = :year
            GROUP BY issuer
        ");
        $appreciationStmt->execute([':year' => $year]);
        $appreciationByType = $appreciationStmt->fetchAll();
    } else {
        $usingDummyData = true;
        // Dummy appreciation data
        $appreciationByType = [
            ['issuer' => 'regular', 'count' => 35],
            ['issuer' => 'prime_minister', 'count' => 10]
        ];
    }

} catch (PDOException $e) {
    error_log("Get Analytics Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات التحليلات', 'alert alert-danger');
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-chart-line me-2"></i> التحليلات المتقدمة
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <button class="btn btn-outline-secondary btn-print">
            <i class="fas fa-print me-1"></i> طباعة
        </button>
    </div>
</div>

<?php if ($usingDummyData): ?>
<div class="alert alert-warning mb-4">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>تنبيه:</strong> البيانات المعروضة هي بيانات وهمية لأغراض العرض فقط. قم بإضافة بيانات حقيقية إلى قاعدة البيانات للحصول على إحصائيات دقيقة.
</div>
<?php endif; ?>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="get" class="row g-3">
            <div class="col-md-4">
                <label for="year" class="form-label">السنة</label>
                <select class="form-select" id="year" name="year">
                    <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                        <option value="<?php echo $y; ?>" <?php echo ($year == $y) ? 'selected' : ''; ?>>
                            <?php echo $y; ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
            <div class="col-md-4">
                <label for="department" class="form-label">القسم</label>
                <select class="form-select" id="department" name="department">
                    <option value="0">جميع الأقسام</option>
                    <?php foreach ($departments as $dept): ?>
                        <option value="<?php echo $dept['id']; ?>" <?php echo ($department == $dept['id']) ? 'selected' : ''; ?>>
                            <?php echo $dept['name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">تطبيق</button>
            </div>
        </form>
    </div>
</div>

<!-- Main Analytics Dashboard -->
<div class="row">
    <!-- Employee Distribution -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i> توزيع الموظفين حسب الأقسام
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="employeesByDepartmentChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Allowances and Promotions -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i> العلاوات والترفيعات خلال العام <?php echo $year; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="allowancesPromotionsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Appreciation Letters -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-certificate me-2"></i> كتب الشكر خلال العام <?php echo $year; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="appreciationLettersChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Employees by Grade -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-layer-group me-2"></i> توزيع الموظفين حسب الدرجة
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="employeesByGradeChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Allowances -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i> استحقاقات العلاوات القادمة
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="upcomingAllowancesChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Promotions -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-level-up-alt me-2"></i> استحقاقات الترفيعات القادمة
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="upcomingPromotionsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Department Statistics -->
<div class="card mb-4">
    <div class="card-header bg-dark text-white">
        <h5 class="mb-0">
            <i class="fas fa-building me-2"></i> إحصائيات الأقسام
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>القسم</th>
                        <th>عدد الموظفين</th>
                        <th>مستحقي العلاوة</th>
                        <th>مستحقي الترفيع</th>
                        <th>متوسط سنوات الخدمة</th>
                        <th>عدد كتب الشكر</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($departmentStats as $dept): ?>
                        <tr>
                            <td><?php echo $dept['name']; ?></td>
                            <td><?php echo $dept['total_employees']; ?></td>
                            <td><?php echo $dept['eligible_allowances']; ?></td>
                            <td><?php echo $dept['eligible_promotions']; ?></td>
                            <td><?php echo isset($dept['avg_years_service']) ? round((float)$dept['avg_years_service'], 1) : 0; ?> سنة</td>
                            <td><?php echo $dept['total_appreciation']; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Predictions Section -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-magic me-2"></i> التنبؤات والتوقعات المستقبلية
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">توقعات العلاوات للعام القادم</h6>
                    </div>
                    <div class="card-body">
                        <?php
                        $totalUpcoming = (int)$upcomingAllowances['within30Days'] + (int)$upcomingAllowances['within60Days'] + (int)$upcomingAllowances['within90Days'];
                        $totalEmployeesNonZero = max(1, $totalEmployees); // Avoid division by zero
                        ?>
                        <p>بناءً على البيانات الحالية، من المتوقع أن يستحق <strong><?php echo $totalUpcoming; ?></strong> موظف علاوة خلال الـ 90 يوم القادمة.</p>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-danger" style="width: <?php echo ((int)$upcomingAllowances['current'] / $totalEmployeesNonZero) * 100; ?>%" title="مستحق حالياً">
                                <?php echo (int)$upcomingAllowances['current']; ?>
                            </div>
                            <div class="progress-bar bg-warning" style="width: <?php echo ((int)$upcomingAllowances['within30Days'] / $totalEmployeesNonZero) * 100; ?>%" title="خلال 30 يوم">
                                <?php echo (int)$upcomingAllowances['within30Days']; ?>
                            </div>
                            <div class="progress-bar bg-info" style="width: <?php echo ((int)$upcomingAllowances['within60Days'] / $totalEmployeesNonZero) * 100; ?>%" title="خلال 60 يوم">
                                <?php echo (int)$upcomingAllowances['within60Days']; ?>
                            </div>
                            <div class="progress-bar bg-success" style="width: <?php echo ((int)$upcomingAllowances['within90Days'] / $totalEmployeesNonZero) * 100; ?>%" title="خلال 90 يوم">
                                <?php echo (int)$upcomingAllowances['within90Days']; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">توقعات الترفيعات للعام القادم</h6>
                    </div>
                    <div class="card-body">
                        <?php
                        $totalUpcomingPromotions = (int)$upcomingPromotions['within30Days'] + (int)$upcomingPromotions['within60Days'] + (int)$upcomingPromotions['within90Days'];
                        $totalEmployeesNonZero = max(1, $totalEmployees); // Avoid division by zero
                        ?>
                        <p>بناءً على البيانات الحالية، من المتوقع أن يستحق <strong><?php echo $totalUpcomingPromotions; ?></strong> موظف ترفيع خلال الـ 90 يوم القادمة.</p>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-danger" style="width: <?php echo ((int)$upcomingPromotions['current'] / $totalEmployeesNonZero) * 100; ?>%" title="مستحق حالياً">
                                <?php echo (int)$upcomingPromotions['current']; ?>
                            </div>
                            <div class="progress-bar bg-warning" style="width: <?php echo ((int)$upcomingPromotions['within30Days'] / $totalEmployeesNonZero) * 100; ?>%" title="خلال 30 يوم">
                                <?php echo (int)$upcomingPromotions['within30Days']; ?>
                            </div>
                            <div class="progress-bar bg-info" style="width: <?php echo ((int)$upcomingPromotions['within60Days'] / $totalEmployeesNonZero) * 100; ?>%" title="خلال 60 يوم">
                                <?php echo (int)$upcomingPromotions['within60Days']; ?>
                            </div>
                            <div class="progress-bar bg-success" style="width: <?php echo ((int)$upcomingPromotions['within90Days'] / $totalEmployeesNonZero) * 100; ?>%" title="خلال 90 يوم">
                                <?php echo (int)$upcomingPromotions['within90Days']; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
