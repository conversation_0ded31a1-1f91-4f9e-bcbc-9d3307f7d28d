<?php
/**
 * Salary History Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and proper role
requireRole(['admin', 'hr']);

// Check if employee ID is provided
$employeeId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($employeeId <= 0) {
    flash('error_message', 'معرف الموظف غير صحيح', 'alert alert-danger');
    redirect('employees.php');
}

// Get employee details
try {
    $stmt = $pdo->prepare("
        SELECT e.*, d.name as department_name, el.name as education_level_name
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        JOIN education_levels el ON e.education_level_id = el.id
        WHERE e.id = :id
    ");
    $stmt->execute([':id' => $employeeId]);
    $employee = $stmt->fetch();
    
    if (!$employee) {
        flash('error_message', 'الموظف غير موجود', 'alert alert-danger');
        redirect('employees.php');
    }
} catch (PDOException $e) {
    error_log("Get Employee Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الموظف', 'alert alert-danger');
    redirect('employees.php');
}

// Get salary history
try {
    $historyStmt = $pdo->prepare("
        SELECT sh.*, u.name as created_by_name
        FROM salary_history sh
        JOIN users u ON sh.created_by = u.id
        WHERE sh.employee_id = :employee_id
        ORDER BY sh.effective_date DESC
    ");
    $historyStmt->execute([':employee_id' => $employeeId]);
    $salaryHistory = $historyStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Salary History Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع تاريخ الراتب', 'alert alert-danger');
    $salaryHistory = [];
}

// Get higher degree history
try {
    $degreeStmt = $pdo->prepare("
        SELECT hdh.*, el.name as education_level_name, u.name as created_by_name
        FROM higher_degree_history hdh
        JOIN education_levels el ON hdh.education_level_id = el.id
        JOIN users u ON hdh.created_by = u.id
        WHERE hdh.employee_id = :employee_id
        ORDER BY hdh.degree_date DESC
    ");
    $degreeStmt->execute([':employee_id' => $employeeId]);
    $degreeHistory = $degreeStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Higher Degree History Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع تاريخ الشهادات العليا', 'alert alert-danger');
    $degreeHistory = [];
}

// Helper function to translate reason to Arabic
function translateReason($reason) {
    $translations = [
        'initial' => 'الراتب الأولي',
        'allowance' => 'علاوة سنوية',
        'promotion' => 'ترفيع وظيفي',
        'higher_degree' => 'شهادة أعلى'
    ];
    
    return isset($translations[$reason]) ? $translations[$reason] : $reason;
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-money-bill-wave me-2"></i> تاريخ الراتب الاسمي
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="employee_details.php?id=<?php echo $employeeId; ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى تفاصيل الموظف
        </a>
    </div>
</div>

<!-- Employee Information Card -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-user me-2"></i> معلومات الموظف
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الرقم الوظيفي:</div>
                <div><?php echo $employee['employee_number']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الاسم:</div>
                <div><?php echo $employee['full_name']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">القسم:</div>
                <div><?php echo $employee['department_name']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الدرجة الحالية:</div>
                <div>الدرجة <?php echo $employee['current_grade']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">المرحلة الحالية:</div>
                <div>المرحلة <?php echo $employee['current_stage']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الراتب الاسمي الحالي:</div>
                <div><?php echo number_format($employee['nominal_salary'], 2); ?> دينار</div>
            </div>
        </div>
    </div>
</div>

<!-- Current Salary Card -->
<div class="card mb-4">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">
            <i class="fas fa-money-bill me-2"></i> الراتب الاسمي الحالي
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h3 class="mb-0"><?php echo number_format($employee['nominal_salary'], 2); ?></h3>
                        <p class="text-muted mb-0">دينار</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h3 class="mb-0"><?php echo $employee['current_grade']; ?></h3>
                        <p class="text-muted mb-0">الدرجة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h3 class="mb-0"><?php echo $employee['current_stage']; ?></h3>
                        <p class="text-muted mb-0">المرحلة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Salary History Table -->
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">
            <i class="fas fa-history me-2"></i> تاريخ الراتب الاسمي
        </h5>
    </div>
    <div class="card-body">
        <?php if (count($salaryHistory) > 0): ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>الدرجة</th>
                            <th>المرحلة</th>
                            <th>الراتب الاسمي</th>
                            <th>السبب</th>
                            <th>ملاحظات</th>
                            <th>تم بواسطة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($salaryHistory as $record): ?>
                            <tr>
                                <td><?php echo formatArabicDate($record['effective_date']); ?></td>
                                <td>الدرجة <?php echo $record['grade']; ?></td>
                                <td>المرحلة <?php echo $record['stage']; ?></td>
                                <td><?php echo number_format($record['nominal_salary'], 2); ?> دينار</td>
                                <td><?php echo translateReason($record['reason']); ?></td>
                                <td><?php echo $record['notes']; ?></td>
                                <td><?php echo $record['created_by_name']; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                لا يوجد سجل للراتب الاسمي لهذا الموظف.
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Higher Degree History Table -->
<?php if (count($degreeHistory) > 0): ?>
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h5 class="mb-0">
                <i class="fas fa-graduation-cap me-2"></i> تاريخ الشهادات العليا (قانون 103)
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>الشهادة</th>
                            <th>الدرجة السابقة</th>
                            <th>الدرجة الجديدة</th>
                            <th>ملاحظات</th>
                            <th>تم بواسطة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($degreeHistory as $record): ?>
                            <tr>
                                <td><?php echo formatArabicDate($record['degree_date']); ?></td>
                                <td><?php echo $record['education_level_name']; ?></td>
                                <td>الدرجة <?php echo $record['previous_grade']; ?></td>
                                <td>الدرجة <?php echo $record['new_grade']; ?></td>
                                <td><?php echo $record['notes']; ?></td>
                                <td><?php echo $record['created_by_name']; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
// Include footer
require_once 'includes/footer.php';
?>
