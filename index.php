<?php
/**
 * Dashboard Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();

// Get dashboard statistics
try {
    // Total employees count
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM employees");
    $totalEmployees = $stmt->fetch()['count'];

    // Employees eligible for allowance
    $stmt = $pdo->query("
        SELECT COUNT(*) as count FROM employees
        WHERE allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
        AND next_allowance_date <= CURDATE()
    ");
    $eligibleForAllowance = $stmt->fetch()['count'];

    // Employees eligible for promotion
    $stmt = $pdo->query("
        SELECT COUNT(*) as count FROM employees
        WHERE next_promotion_date <= CURDATE()
    ");
    $eligibleForPromotion = $stmt->fetch()['count'];

    // Total appreciation letters
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM appreciation_letters");
    $totalAppreciationLetters = $stmt->fetch()['count'];

    // Recent activities
    $stmt = $pdo->query("
        SELECT sl.*, u.username, u.full_name
        FROM system_logs sl
        JOIN users u ON sl.user_id = u.id
        ORDER BY sl.created_at DESC
        LIMIT 10
    ");
    $recentActivities = $stmt->fetchAll();

    // Upcoming allowances
    $stmt = $pdo->query("
        SELECT e.id, e.employee_number, e.full_name, e.current_grade, e.next_allowance_date, d.name as department_name
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        WHERE e.allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
        AND e.next_allowance_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
        ORDER BY e.next_allowance_date ASC
        LIMIT 5
    ");
    $upcomingAllowances = $stmt->fetchAll();

    // Upcoming promotions
    $stmt = $pdo->query("
        SELECT e.id, e.employee_number, e.full_name, e.current_grade, e.next_promotion_date, d.name as department_name
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        WHERE e.next_promotion_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
        ORDER BY e.next_promotion_date ASC
        LIMIT 5
    ");
    $upcomingPromotions = $stmt->fetchAll();

    // Get retirement age from settings
    $retirementAge = 60; // Default
    $settingStmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'retirement_age'");
    $settingStmt->execute();
    $setting = $settingStmt->fetch();
    if ($setting) {
        $retirementAge = (int)$setting['setting_value'];
    }

    // Upcoming retirements
    $stmt = $pdo->query("
        SELECT e.id, e.employee_number, e.full_name, e.birth_date, e.current_grade, d.name as department_name,
               TIMESTAMPDIFF(YEAR, e.birth_date, CURDATE()) as current_age,
               DATE_ADD(e.birth_date, INTERVAL $retirementAge YEAR) as retirement_date
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        WHERE DATE_ADD(e.birth_date, INTERVAL $retirementAge YEAR) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 180 DAY)
        ORDER BY retirement_date ASC
        LIMIT 5
    ");
    $upcomingRetirements = $stmt->fetchAll();

} catch (PDOException $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء تحميل البيانات', 'alert alert-danger');
    $totalEmployees = $eligibleForAllowance = $eligibleForPromotion = $totalAppreciationLetters = 0;
    $recentActivities = $upcomingAllowances = $upcomingPromotions = $upcomingRetirements = [];
}
?>

<div class="row mb-4">
    <div class="col-12">
        <h1 class="display-5 mb-4">
            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="dashboard-card animate__animated animate__fadeIn">
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="count"><?php echo $totalEmployees; ?></div>
            <div class="title">إجمالي الموظفين</div>
            <a href="employees.php" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="dashboard-card success animate__animated animate__fadeIn" style="animation-delay: 0.1s;">
            <div class="icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="count"><?php echo $eligibleForAllowance; ?></div>
            <div class="title">مستحقي العلاوة</div>
            <a href="allowances.php?status=eligible" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="dashboard-card animate__animated animate__fadeIn" style="animation-delay: 0.2s;">
            <div class="icon">
                <i class="fas fa-level-up-alt"></i>
            </div>
            <div class="count"><?php echo $eligibleForPromotion; ?></div>
            <div class="title">مستحقي الترفيع</div>
            <a href="promotions.php?status=eligible" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="dashboard-card warning animate__animated animate__fadeIn" style="animation-delay: 0.3s;">
            <div class="icon">
                <i class="fas fa-certificate"></i>
            </div>
            <div class="count"><?php echo $totalAppreciationLetters; ?></div>
            <div class="title">كتب الشكر</div>
            <a href="appreciation.php" class="stretched-link"></a>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card h-100 animate__animated animate__fadeIn" style="animation-delay: 0.4s;">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i> توزيع الموظفين حسب الأقسام
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:250px;">
                    <canvas id="employeesByDepartmentChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card h-100 animate__animated animate__fadeIn" style="animation-delay: 0.5s;">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i> العلاوات والترفيعات خلال العام
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:250px;">
                    <canvas id="allowancesPromotionsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Upcoming Allowances -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <i class="fas fa-money-bill-wave me-2"></i> العلاوات القادمة
            </div>
            <div class="card-body">
                <?php if (count($upcomingAllowances) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم</th>
                                    <th>تاريخ الاستحقاق</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($upcomingAllowances as $employee): ?>
                                    <tr>
                                        <td><?php echo $employee['employee_number']; ?></td>
                                        <td>
                                            <a href="employee_details.php?id=<?php echo $employee['id']; ?>">
                                                <?php echo $employee['full_name']; ?>
                                            </a>
                                        </td>
                                        <td><?php echo formatArabicDate($employee['next_allowance_date']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <a href="allowances.php" class="btn btn-sm btn-outline-success">عرض الكل</a>
                <?php else: ?>
                    <p class="text-muted text-center">لا توجد علاوات مستحقة خلال الـ 30 يوم القادمة</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Upcoming Promotions -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-level-up-alt me-2"></i> الترفيعات القادمة
            </div>
            <div class="card-body">
                <?php if (count($upcomingPromotions) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم</th>
                                    <th>تاريخ الاستحقاق</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($upcomingPromotions as $employee): ?>
                                    <tr>
                                        <td><?php echo $employee['employee_number']; ?></td>
                                        <td>
                                            <a href="employee_details.php?id=<?php echo $employee['id']; ?>">
                                                <?php echo $employee['full_name']; ?>
                                            </a>
                                        </td>
                                        <td><?php echo formatArabicDate($employee['next_promotion_date']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <a href="promotions.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                <?php else: ?>
                    <p class="text-muted text-center">لا توجد ترفيعات مستحقة خلال الـ 30 يوم القادمة</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Upcoming Retirements -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <i class="fas fa-user-clock me-2"></i> التقاعد المتوقع
            </div>
            <div class="card-body">
                <?php if (count($upcomingRetirements) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم</th>
                                    <th>تاريخ التقاعد</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($upcomingRetirements as $employee): ?>
                                    <tr>
                                        <td><?php echo $employee['employee_number']; ?></td>
                                        <td>
                                            <a href="employee_details.php?id=<?php echo $employee['id']; ?>">
                                                <?php echo $employee['full_name']; ?>
                                            </a>
                                        </td>
                                        <td><?php echo formatArabicDate($employee['retirement_date']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <a href="reports.php?type=upcoming_retirement&period=180" class="btn btn-sm btn-outline-danger">عرض الكل</a>
                <?php else: ?>
                    <p class="text-muted text-center">لا يوجد موظفين مقبلين على التقاعد خلال الـ 6 أشهر القادمة</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Additional Charts -->
<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card h-100 animate__animated animate__fadeIn" style="animation-delay: 0.6s;">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-certificate me-2"></i> كتب الشكر خلال العام
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:250px;">
                    <canvas id="appreciationLettersChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card h-100 animate__animated animate__fadeIn" style="animation-delay: 0.7s;">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-layer-group me-2"></i> توزيع الموظفين حسب الدرجة
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:250px;">
                    <canvas id="employeesByGradeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Links -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card animate__animated animate__fadeIn" style="animation-delay: 0.8s;">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0">
                    <i class="fas fa-link me-2"></i> روابط سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 col-6 mb-3">
                        <a href="analytics.php" class="btn btn-outline-primary w-100 p-3">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <div>التحليلات المتقدمة</div>
                        </a>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <a href="reminders.php" class="btn btn-outline-warning w-100 p-3">
                            <i class="fas fa-bell fa-2x mb-2"></i>
                            <div>نظام التذكيرات</div>
                        </a>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <a href="reports.php" class="btn btn-outline-success w-100 p-3">
                            <i class="fas fa-file-alt fa-2x mb-2"></i>
                            <div>التقارير</div>
                        </a>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <a href="system_settings.php" class="btn btn-outline-secondary w-100 p-3">
                            <i class="fas fa-cogs fa-2x mb-2"></i>
                            <div>إعدادات النظام</div>
                        </a>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card animate__animated animate__fadeIn" style="animation-delay: 0.9s;">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i> آخر النشاطات
                </h5>
            </div>
            <div class="card-body">
                <?php if (count($recentActivities) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>النشاط</th>
                                    <th>النوع</th>
                                    <th>التفاصيل</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentActivities as $activity): ?>
                                    <tr>
                                        <td><?php echo $activity['full_name']; ?></td>
                                        <td><?php echo $activity['action']; ?></td>
                                        <td><?php echo $activity['entity_type']; ?></td>
                                        <td><?php echo $activity['details']; ?></td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($activity['created_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php if (hasRole(['admin'])): ?>
                        <a href="system_logs.php" class="btn btn-sm btn-outline-secondary">عرض الكل</a>
                    <?php endif; ?>
                <?php else: ?>
                    <p class="text-muted text-center">لا توجد نشاطات حديثة</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
