<?php
/**
 * System Settings Page
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require admin role
requireRole(['admin']);

// Get current settings
try {
    $stmt = $pdo->query("SELECT * FROM system_settings");
    $settings = [];

    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    error_log("Get Settings Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع إعدادات النظام', 'alert alert-danger');
    $settings = [];
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize and validate inputs
    $allowancesPerGrade = (int)$_POST['allowances_per_grade'];
    $promotionYearsLowerGrades = (int)$_POST['promotion_years_lower_grades'];
    $promotionYearsUpperGrades = (int)$_POST['promotion_years_upper_grades'];
    $regularLetterMonths = (int)$_POST['regular_letter_months'];
    $pmLetterMonths = (int)$_POST['pm_letter_months'];
    $enableNotifications = isset($_POST['enable_notifications']) ? 1 : 0;
    $enableReminders = isset($_POST['enable_reminders']) ? 1 : 0;
    $reminderDays = (int)$_POST['reminder_days'];

    // Validate inputs
    $errors = [];

    if ($allowancesPerGrade < 1 || $allowancesPerGrade > 20) {
        $errors[] = 'عدد العلاوات في كل درجة يجب أن يكون بين 1 و 20';
    }

    if ($promotionYearsLowerGrades < 1 || $promotionYearsLowerGrades > 10) {
        $errors[] = 'عدد سنوات الترقية للدرجات الدنيا يجب أن يكون بين 1 و 10';
    }

    if ($promotionYearsUpperGrades < 1 || $promotionYearsUpperGrades > 10) {
        $errors[] = 'عدد سنوات الترقية للدرجات العليا يجب أن يكون بين 1 و 10';
    }

    if ($regularLetterMonths < 0 || $regularLetterMonths > 12) {
        $errors[] = 'عدد أشهر تخفيض كتاب الشكر العادي يجب أن يكون بين 0 و 12';
    }

    if ($pmLetterMonths < 0 || $pmLetterMonths > 24) {
        $errors[] = 'عدد أشهر تخفيض كتاب شكر رئيس الوزراء يجب أن يكون بين 0 و 24';
    }

    if ($reminderDays < 1 || $reminderDays > 90) {
        $errors[] = 'عدد أيام التذكير يجب أن يكون بين 1 و 90';
    }

    // If no errors, update settings
    if (empty($errors)) {
        try {
            // Start transaction
            $pdo->beginTransaction();

            // Update settings
            $updateStmt = $pdo->prepare("
                UPDATE system_settings
                SET setting_value = :value
                WHERE setting_key = :key
            ");

            $settingsToUpdate = [
                'allowances_per_grade' => $allowancesPerGrade,
                'promotion_years_lower_grades' => $promotionYearsLowerGrades,
                'promotion_years_upper_grades' => $promotionYearsUpperGrades,
                'regular_letter_months' => $regularLetterMonths,
                'pm_letter_months' => $pmLetterMonths,
                'enable_notifications' => $enableNotifications,
                'enable_reminders' => $enableReminders,
                'reminder_days' => $reminderDays
            ];

            foreach ($settingsToUpdate as $key => $value) {
                $updateStmt->execute([
                    ':key' => $key,
                    ':value' => $value
                ]);
            }

            // Update config.php constants
            $configFile = file_get_contents('config/config.php');

            $replacements = [
                "define('ALLOWANCES_PER_GRADE', " . $settings['allowances_per_grade'] . ");" => "define('ALLOWANCES_PER_GRADE', " . $allowancesPerGrade . ");",
                "define('PROMOTION_YEARS_LOWER_GRADES', " . $settings['promotion_years_lower_grades'] . ");" => "define('PROMOTION_YEARS_LOWER_GRADES', " . $promotionYearsLowerGrades . ");",
                "define('PROMOTION_YEARS_UPPER_GRADES', " . $settings['promotion_years_upper_grades'] . ");" => "define('PROMOTION_YEARS_UPPER_GRADES', " . $promotionYearsUpperGrades . ");",
                "define('REGULAR_LETTER_MONTHS', " . $settings['regular_letter_months'] . ");" => "define('REGULAR_LETTER_MONTHS', " . $regularLetterMonths . ");",
                "define('PM_LETTER_MONTHS', " . $settings['pm_letter_months'] . ");" => "define('PM_LETTER_MONTHS', " . $pmLetterMonths . ");"
            ];

            foreach ($replacements as $search => $replace) {
                $configFile = str_replace($search, $replace, $configFile);
            }

            file_put_contents('config/config.php', $configFile);

            // Log activity
            logActivity(
                $_SESSION['user_id'],
                'تحديث إعدادات النظام',
                'system_settings',
                0,
                'تم تحديث إعدادات النظام'
            );

            // Commit transaction
            $pdo->commit();

            // Update settings array
            $settings = $settingsToUpdate;

            flash('success_message', 'تم تحديث إعدادات النظام بنجاح', 'alert alert-success');
            redirect('system_settings.php');
        } catch (Exception $e) {
            // Rollback transaction on error
            $pdo->rollBack();

            error_log("Update Settings Error: " . $e->getMessage());
            flash('error_message', 'حدث خطأ أثناء تحديث إعدادات النظام', 'alert alert-danger');
        }
    } else {
        // Display errors
        $errorMessage = '<ul class="mb-0">';
        foreach ($errors as $error) {
            $errorMessage .= '<li>' . $error . '</li>';
        }
        $errorMessage .= '</ul>';

        flash('error_message', $errorMessage, 'alert alert-danger');
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-cogs me-2"></i> إعدادات النظام
        </h1>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-sliders-h me-2"></i> تعديل إعدادات النظام
        </h5>
    </div>
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" class="needs-validation" novalidate>
            <div class="row mb-4">
                <div class="col-12">
                    <h4 class="border-bottom pb-2">إعدادات العلاوات والترقيات</h4>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="allowances_per_grade" class="form-label">عدد العلاوات في كل درجة</label>
                    <input type="number" class="form-control" id="allowances_per_grade" name="allowances_per_grade" min="1" max="20" value="<?php echo isset($settings['allowances_per_grade']) ? $settings['allowances_per_grade'] : 11; ?>" required>
                    <div class="form-text">الحد الأقصى لعدد العلاوات التي يمكن للموظف الحصول عليها في كل درجة</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="promotion_years_lower_grades" class="form-label">سنوات الترقية للدرجات الدنيا</label>
                    <input type="number" class="form-control" id="promotion_years_lower_grades" name="promotion_years_lower_grades" min="1" max="10" value="<?php echo isset($settings['promotion_years_lower_grades']) ? $settings['promotion_years_lower_grades'] : 4; ?>" required>
                    <div class="form-text">عدد السنوات المطلوبة للترقية من الدرجة 10 إلى 5</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="promotion_years_upper_grades" class="form-label">سنوات الترقية للدرجات العليا</label>
                    <input type="number" class="form-control" id="promotion_years_upper_grades" name="promotion_years_upper_grades" min="1" max="10" value="<?php echo isset($settings['promotion_years_upper_grades']) ? $settings['promotion_years_upper_grades'] : 5; ?>" required>
                    <div class="form-text">عدد السنوات المطلوبة للترقية من الدرجة 5 إلى 1</div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-12">
                    <h4 class="border-bottom pb-2">إعدادات كتب الشكر</h4>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="regular_letter_months" class="form-label">تخفيض كتاب الشكر العادي (بالأشهر)</label>
                    <input type="number" class="form-control" id="regular_letter_months" name="regular_letter_months" min="0" max="12" value="<?php echo isset($settings['regular_letter_months']) ? $settings['regular_letter_months'] : 1; ?>" required>
                    <div class="form-text">عدد الأشهر التي يتم تخفيضها من مدة استحقاق العلاوة أو الترقية عند الحصول على كتاب شكر عادي</div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="pm_letter_months" class="form-label">تخفيض كتاب شكر رئيس الوزراء (بالأشهر)</label>
                    <input type="number" class="form-control" id="pm_letter_months" name="pm_letter_months" min="0" max="24" value="<?php echo isset($settings['pm_letter_months']) ? $settings['pm_letter_months'] : 6; ?>" required>
                    <div class="form-text">عدد الأشهر التي يتم تخفيضها من مدة استحقاق العلاوة أو الترقية عند الحصول على كتاب شكر من رئيس الوزراء</div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-12">
                    <h4 class="border-bottom pb-2">إعدادات الإشعارات والتذكيرات</h4>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="enable_notifications" name="enable_notifications" <?php echo (isset($settings['enable_notifications']) && $settings['enable_notifications'] == 1) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="enable_notifications">تفعيل نظام الإشعارات</label>
                    </div>
                    <div class="form-text">تفعيل أو تعطيل نظام الإشعارات في النظام</div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="enable_reminders" name="enable_reminders" <?php echo (isset($settings['enable_reminders']) && $settings['enable_reminders'] == 1) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="enable_reminders">تفعيل نظام التذكيرات</label>
                    </div>
                    <div class="form-text">تفعيل أو تعطيل نظام التذكيرات للمديرين</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="reminder_days" class="form-label">أيام التذكير المسبق</label>
                    <input type="number" class="form-control" id="reminder_days" name="reminder_days" min="1" max="90" value="<?php echo isset($settings['reminder_days']) ? $settings['reminder_days'] : 30; ?>" required>
                    <div class="form-text">عدد الأيام قبل استحقاق العلاوة أو الترقية لإرسال تذكير</div>
                </div>
            </div>

            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                ملاحظة: تغيير هذه الإعدادات سيؤثر على حسابات استحقاقات العلاوات والترقيات للموظفين. يرجى التأكد من صحة القيم قبل الحفظ.
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary px-5">
                    <i class="fas fa-save me-1"></i> حفظ الإعدادات
                </button>
                <a href="index.php" class="btn btn-secondary px-5">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
