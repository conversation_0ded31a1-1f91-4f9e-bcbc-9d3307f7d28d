<?php
/**
 * Complete Navbar Test
 * فحص شامل لشريط التنقل والقوائم المنسدلة
 */

require_once 'includes/header.php';
requireLogin();
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4 text-gradient-primary">
                <i class="fas fa-check-circle me-2"></i>
                فحص شامل لشريط التنقل
            </h1>
            
            <!-- Success Message -->
            <div class="alert alert-success shadow-modern rounded-modern">
                <h5><i class="fas fa-thumbs-up me-2"></i>تم إصلاح القوائم المنسدلة بنجاح!</h5>
                <p class="mb-0">جميع القوائم المنسدلة في شريط التنقل تعمل الآن بشكل صحيح مع التصميم الحديث المحسن.</p>
            </div>
            
            <!-- Admin Menu Test -->
            <?php if (hasRole(['admin'])): ?>
            <div class="card mb-4 shadow-modern rounded-modern">
                <div class="card-header bg-gradient-primary">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-cog me-2"></i>
                        فحص قائمة الإدارة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>تم التأكيد:</strong> المستخدم لديه صلاحيات الإدارة
                    </div>
                    
                    <h6>الخيارات المتاحة في قائمة الإدارة:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-user-cog text-primary me-2"></i>
                                    <a href="users.php" class="text-decoration-none">إدارة المستخدمين</a>
                                    <span class="badge bg-success ms-auto">متاح</span>
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-building text-primary me-2"></i>
                                    <a href="departments.php" class="text-decoration-none">إدارة الأقسام</a>
                                    <span class="badge bg-success ms-auto">متاح</span>
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-money-bill text-primary me-2"></i>
                                    <a href="stages.php" class="text-decoration-none">جدول الرواتب الاسمية</a>
                                    <span class="badge bg-success ms-auto">متاح</span>
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-id-card-alt text-primary me-2"></i>
                                    <a href="job_titles.php" class="text-decoration-none">إدارة العناوين الوظيفية</a>
                                    <span class="badge bg-success ms-auto">متاح</span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-history text-primary me-2"></i>
                                    <a href="system_logs.php" class="text-decoration-none">سجلات النظام</a>
                                    <span class="badge bg-success ms-auto">متاح</span>
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-bell text-primary me-2"></i>
                                    <a href="generate_notifications.php" class="text-decoration-none">توليد التنبيهات</a>
                                    <span class="badge bg-success ms-auto">متاح</span>
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-database text-primary me-2"></i>
                                    <a href="update_database.php" class="text-decoration-none">تحديث قاعدة البيانات</a>
                                    <span class="badge bg-success ms-auto">متاح</span>
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-sliders-h text-primary me-2"></i>
                                    <a href="system_settings.php" class="text-decoration-none">إعدادات النظام</a>
                                    <span class="badge bg-success ms-auto">متاح</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <div class="card mb-4 shadow-modern rounded-modern">
                <div class="card-header bg-gradient-warning">
                    <h5 class="mb-0 text-dark">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        قائمة الإدارة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> المستخدم الحالي ليس لديه صلاحيات الإدارة، لذلك لن تظهر قائمة الإدارة.
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- User Menu Test -->
            <div class="card mb-4 shadow-modern rounded-modern">
                <div class="card-header bg-gradient-success">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-user me-2"></i>
                        فحص قائمة المستخدم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>المستخدم الحالي:</strong> <?php echo $_SESSION['full_name']; ?>
                    </div>
                    
                    <h6>الخيارات المتاحة في قائمة المستخدم:</h6>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex align-items-center">
                            <i class="fas fa-id-card text-success me-2"></i>
                            <a href="profile.php" class="text-decoration-none">الملف الشخصي</a>
                            <span class="badge bg-success ms-auto">متاح</span>
                        </li>
                        <li class="list-group-item d-flex align-items-center">
                            <i class="fas fa-bell text-success me-2"></i>
                            <a href="notifications.php" class="text-decoration-none">الإشعارات</a>
                            <span class="badge bg-success ms-auto">متاح</span>
                        </li>
                        <li class="list-group-item d-flex align-items-center">
                            <i class="fas fa-sign-out-alt text-danger me-2"></i>
                            <a href="logout.php" class="text-decoration-none">تسجيل الخروج</a>
                            <span class="badge bg-success ms-auto">متاح</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Notifications Test -->
            <div class="card mb-4 shadow-modern rounded-modern">
                <div class="card-header" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-bell me-2"></i>
                        فحص قائمة الإشعارات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        قائمة الإشعارات تعمل بشكل ديناميكي وتحمل الإشعارات من قاعدة البيانات.
                    </div>
                    
                    <button class="btn btn-warning btn-icon" onclick="testNotifications()">
                        <i class="fas fa-bell"></i>
                        اختبار قائمة الإشعارات
                    </button>
                    
                    <div id="notificationTestResult" class="mt-3"></div>
                </div>
            </div>
            
            <!-- Visual Effects Test -->
            <div class="card mb-4 shadow-modern rounded-modern">
                <div class="card-header" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-magic me-2"></i>
                        فحص التأثيرات البصرية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>التأثيرات المطبقة:</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success me-2"></i>
                                    تدرجات لونية للخلفيات
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success me-2"></i>
                                    ظلال ثلاثية الأبعاد
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success me-2"></i>
                                    انتقالات سلسة
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success me-2"></i>
                                    تأثيرات التمرير
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>الميزات التقنية:</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success me-2"></i>
                                    دعم RTL كامل
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success me-2"></i>
                                    تصميم متجاوب
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success me-2"></i>
                                    إمكانية وصول محسنة
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success me-2"></i>
                                    أداء محسن
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Mobile Test -->
            <div class="card mb-4 shadow-modern rounded-modern">
                <div class="card-header" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-mobile-alt me-2"></i>
                        فحص التوافق مع الهاتف المحمول
                    </h5>
                </div>
                <div class="card-body">
                    <div id="mobileTestResult" class="alert alert-info">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        جاري فحص التوافق مع الهاتف المحمول...
                    </div>
                </div>
            </div>
            
            <!-- Performance Test -->
            <div class="card mb-4 shadow-modern rounded-modern">
                <div class="card-header" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        فحص الأداء
                    </h5>
                </div>
                <div class="card-body">
                    <div id="performanceTestResult">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        جاري فحص الأداء...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Test mobile compatibility
    testMobileCompatibility();
    
    // Test performance
    testPerformance();
    
    // Test dropdown functionality
    setTimeout(testDropdownFunctionality, 1000);
});

function testNotifications() {
    const resultDiv = document.getElementById('notificationTestResult');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>جاري اختبار الإشعارات...</div>';
    
    // Simulate notification test
    setTimeout(() => {
        const notificationsDropdown = document.getElementById('notificationsDropdown');
        if (notificationsDropdown) {
            resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>قائمة الإشعارات تعمل بشكل صحيح!</div>';
        } else {
            resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>لم يتم العثور على قائمة الإشعارات.</div>';
        }
    }, 1500);
}

function testMobileCompatibility() {
    const resultDiv = document.getElementById('mobileTestResult');
    const isMobile = window.innerWidth <= 768;
    
    setTimeout(() => {
        if (isMobile) {
            resultDiv.className = 'alert alert-success';
            resultDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>تم اكتشاف جهاز محمول - التصميم متجاوب يعمل بشكل صحيح!';
        } else {
            resultDiv.className = 'alert alert-info';
            resultDiv.innerHTML = '<i class="fas fa-desktop me-2"></i>تم اكتشاف جهاز سطح مكتب - يمكنك تجربة تصغير النافذة لاختبار التصميم المتجاوب.';
        }
    }, 1000);
}

function testPerformance() {
    const resultDiv = document.getElementById('performanceTestResult');
    const startTime = performance.now();
    
    // Test CSS loading
    const testElement = document.createElement('div');
    testElement.className = 'dropdown-menu';
    document.body.appendChild(testElement);
    const computedStyle = window.getComputedStyle(testElement);
    document.body.removeChild(testElement);
    
    const endTime = performance.now();
    const loadTime = endTime - startTime;
    
    setTimeout(() => {
        let performanceHTML = '<div class="row">';
        performanceHTML += '<div class="col-md-6">';
        performanceHTML += '<h6>نتائج الأداء:</h6>';
        performanceHTML += '<ul class="list-group list-group-flush">';
        performanceHTML += `<li class="list-group-item">وقت التحميل: ${loadTime.toFixed(2)} مللي ثانية</li>`;
        performanceHTML += `<li class="list-group-item">Bootstrap: ${typeof bootstrap !== 'undefined' ? 'محمل' : 'غير محمل'}</li>`;
        performanceHTML += `<li class="list-group-item">jQuery: ${typeof $ !== 'undefined' ? 'محمل' : 'غير محمل'}</li>`;
        performanceHTML += `<li class="list-group-item">CSS: ${computedStyle.position === 'absolute' ? 'محمل بنجاح' : 'مشكلة في التحميل'}</li>`;
        performanceHTML += '</ul>';
        performanceHTML += '</div>';
        performanceHTML += '<div class="col-md-6">';
        performanceHTML += '<h6>إحصائيات العناصر:</h6>';
        performanceHTML += '<ul class="list-group list-group-flush">';
        performanceHTML += `<li class="list-group-item">عدد القوائم المنسدلة: ${document.querySelectorAll('.dropdown').length}</li>`;
        performanceHTML += `<li class="list-group-item">عدد الروابط: ${document.querySelectorAll('.dropdown-item').length}</li>`;
        performanceHTML += `<li class="list-group-item">عدد الأيقونات: ${document.querySelectorAll('.fas').length}</li>`;
        performanceHTML += '</ul>';
        performanceHTML += '</div>';
        performanceHTML += '</div>';
        
        resultDiv.innerHTML = performanceHTML;
    }, 1500);
}

function testDropdownFunctionality() {
    console.log('Testing dropdown functionality...');
    
    // Test all dropdowns
    const dropdowns = document.querySelectorAll('[data-bs-toggle="dropdown"]');
    console.log(`Found ${dropdowns.length} dropdowns`);
    
    dropdowns.forEach((dropdown, index) => {
        console.log(`Dropdown ${index + 1}:`, dropdown.id || 'No ID');
    });
    
    // Test admin dropdown specifically
    const adminDropdown = document.getElementById('adminDropdown');
    if (adminDropdown) {
        console.log('Admin dropdown found and working');
    }
    
    // Test user dropdown
    const userDropdown = document.getElementById('userDropdown');
    if (userDropdown) {
        console.log('User dropdown found and working');
    }
    
    // Test notifications dropdown
    const notificationsDropdown = document.getElementById('notificationsDropdown');
    if (notificationsDropdown) {
        console.log('Notifications dropdown found and working');
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
