<?php
/**
 * Test MySQL Connection
 * اختبار الاتصال بقاعدة البيانات
 */

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database credentials
$host = 'localhost';
$user = 'root';

// Try different passwords
$passwords = [
    '' => 'كلمة مرور فارغة',
    'root' => 'root',
    'password' => 'password',
    'mysql' => 'mysql',
    'xampp' => 'xampp'
];

// Try different connection methods
$methods = [
    'standard' => "mysql:host=$host",
    'localhost_ip' => "mysql:host=127.0.0.1",
    'port' => "mysql:host=$host;port=3306",
    'xampp_default' => "mysql:host=127.0.0.1;port=3306",
    'xampp_socket' => "mysql:unix_socket=C:/xampp/mysql/mysql.sock",
    'xampp_pipe' => "mysql:host=localhost;port=3306;pipe=MySQL"
];

echo "<h1>اختبار الاتصال بـ MySQL</h1>";

try {
    // Try different connection methods and passwords
    $success = false;
    $lastError = '';
    $successfulPassword = '';
    $successfulMethod = '';

    echo "<h2>محاولة الاتصال باستخدام طرق وكلمات مرور مختلفة:</h2>";
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr><th>طريقة الاتصال</th><th>كلمة المرور</th><th>النتيجة</th></tr>";

    foreach ($methods as $methodName => $dsn) {
        foreach ($passwords as $pass => $passDesc) {
            echo "<tr>";
            echo "<td>$methodName ($dsn)</td>";
            echo "<td>$passDesc</td>";
            echo "<td>";

            try {
                $testPdo = new PDO($dsn, $user, $pass);
                $testPdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                echo "<span style='color:green;'>نجاح!</span>";

                // If we get here, connection was successful
                $success = true;
                $pdo = $testPdo;
                $successfulPassword = $pass;
                $successfulMethod = $methodName;
            } catch (PDOException $e) {
                echo "<span style='color:red;'>فشل: " . $e->getMessage() . "</span>";
                $lastError = $e->getMessage();
            }

            echo "</td></tr>";

            // If we found a working connection, we can stop trying
            if ($success) {
                break 2;
            }
        }
    }

    echo "</table>";

    if (!$success) {
        throw new PDOException("فشلت جميع محاولات الاتصال. آخر خطأ: " . $lastError);
    }

    echo "<p style='color:green;'>تم الاتصال بـ MySQL بنجاح باستخدام طريقة <strong>$successfulMethod</strong> وكلمة المرور <strong>$passDesc</strong>!</p>";

    // Additional server information
    echo "<h2>معلومات الخادم:</h2>";
    echo "<ul>";
    echo "<li>إصدار MySQL: " . $pdo->getAttribute(PDO::ATTR_SERVER_VERSION) . "</li>";
    echo "<li>اسم الخادم: " . $pdo->getAttribute(PDO::ATTR_SERVER_INFO) . "</li>";
    echo "<li>معلومات الاتصال: " . $pdo->getAttribute(PDO::ATTR_CONNECTION_STATUS) . "</li>";
    echo "</ul>";

    // Get list of databases
    $stmt = $pdo->query("SHOW DATABASES");
    $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);

    echo "<h2>قواعد البيانات المتاحة:</h2>";
    echo "<ul>";
    foreach ($databases as $database) {
        echo "<li>$database" . ($database == 'employee_promotions_db' ? " <strong>(قاعدة البيانات المطلوبة)</strong>" : "") . "</li>";
    }
    echo "</ul>";

    // Check if our database exists
    $dbExists = in_array('employee_promotions_db', $databases);

    if ($dbExists) {
        echo "<p style='color:green;'>قاعدة البيانات <strong>employee_promotions_db</strong> موجودة!</p>";

        // Try to connect to the database
        try {
            $dbPdo = new PDO(
                "$dsn;dbname=employee_promotions_db;charset=utf8mb4",
                $user,
                $successfulPassword
            );

            echo "<p style='color:green;'>تم الاتصال بقاعدة البيانات <strong>employee_promotions_db</strong> بنجاح!</p>";

            // Update config file with successful connection details
            echo "<div class='alert alert-success' style='padding: 10px; background-color: #dff0d8; border: 1px solid #d6e9c6; border-radius: 4px; margin: 10px 0;'>";
            echo "<h3>معلومات الاتصال الناجحة:</h3>";
            echo "<p>لإصلاح مشكلة الاتصال، قم بتحديث ملف <code>config/database.php</code> بالمعلومات التالية:</p>";
            echo "<pre style='background-color: #f5f5f5; padding: 10px; border: 1px solid #ccc; border-radius: 4px;'>";
            echo "define('DB_HOST', '" . ($methodName == 'xampp_socket' ? 'localhost' : $host) . "');\n";
            echo "define('DB_NAME', 'employee_promotions_db');\n";
            echo "define('DB_USER', '$user');\n";
            echo "define('DB_PASS', '$successfulPassword');\n";
            echo "define('DB_CHARSET', 'utf8mb4');\n";
            echo "</pre>";
            echo "</div>";

            // Check tables
            $stmt = $dbPdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (count($tables) > 0) {
                echo "<h2>الجداول الموجودة في قاعدة البيانات:</h2>";
                echo "<ul>";
                foreach ($tables as $table) {
                    echo "<li>$table</li>";
                }
                echo "</ul>";
            } else {
                echo "<p style='color:orange;'>لا توجد جداول في قاعدة البيانات. يجب استيراد ملف schema.sql.</p>";
                echo "<p>يمكنك استيراد هيكل قاعدة البيانات من خلال تشغيل الملف: <a href='install.php'>install.php</a></p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color:red;'>خطأ في الاتصال بقاعدة البيانات employee_promotions_db: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color:red;'>قاعدة البيانات <strong>employee_promotions_db</strong> غير موجودة!</p>";
        echo "<p>يجب إنشاء قاعدة البيانات أولاً من خلال phpMyAdmin.</p>";
        echo "<p>1. افتح <a href='http://localhost/phpmyadmin' target='_blank'>phpMyAdmin</a></p>";
        echo "<p>2. انقر على 'قواعد البيانات' أو 'Databases'</p>";
        echo "<p>3. أدخل اسم قاعدة البيانات: employee_promotions_db</p>";
        echo "<p>4. اختر ترميز utf8mb4_unicode_ci</p>";
        echo "<p>5. انقر على 'إنشاء' أو 'Create'</p>";
    }

} catch (PDOException $e) {
    echo "<p style='color:red;'>فشل الاتصال بـ MySQL: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من تشغيل خدمة MySQL وأن بيانات الاتصال صحيحة.</p>";
}
?>
