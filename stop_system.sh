#!/bin/bash

echo "======================================================"
echo "    نظام إدارة العلاوات والترقية وكتب الشكر"
echo "======================================================"
echo ""
echo "جاري إيقاف خدمات النظام..."
echo ""

# تحديد المسار الحالي
CURRENT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
XAMPP_DIR="$CURRENT_DIR/xampp"

# التحقق من وجود مجلد xampp
if [ ! -d "$XAMPP_DIR" ]; then
    echo "خطأ: مجلد xampp غير موجود في المسار $CURRENT_DIR"
    echo "يرجى التأكد من وجود XAMPP في المسار الصحيح."
    echo ""
    read -p "اضغط Enter للاستمرار..."
    exit 1
fi

# إيقاف خدمات XAMPP
cd "$XAMPP_DIR"
echo "جاري إيقاف خدمات XAMPP..."
./xampp stop

echo ""
echo "======================================================"
echo "تم إيقاف خدمات النظام بنجاح!"
echo ""
echo "يمكنك الآن إغلاق هذه النافذة أو فصل وسيط التخزين المحمول."
echo "======================================================"
echo ""
read -p "اضغط Enter للاستمرار..."
