/**
 * Notifications JavaScript
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Global variables
let notifications = [];
let unreadCount = 0;
let notificationsInitialized = false;

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize notifications system
    initNotifications();
    
    // Set up periodic check for new notifications
    setInterval(checkNewNotifications, 60000); // Check every minute
});

/**
 * Initialize notifications system
 */
function initNotifications() {
    // Check if notifications container exists
    if (document.getElementById('notificationsDropdown')) {
        // Add notifications counter to navbar
        addNotificationsCounter();
        
        // Load initial notifications
        loadNotifications();
        
        // Mark notifications as initialized
        notificationsInitialized = true;
    }
}

/**
 * Add notifications counter to navbar
 */
function addNotificationsCounter() {
    // Get the notifications dropdown toggle
    const notificationsToggle = document.querySelector('[data-bs-toggle="notifications"]');
    
    if (notificationsToggle) {
        // Create counter badge
        const counterBadge = document.createElement('span');
        counterBadge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-counter';
        counterBadge.style.fontSize = '0.6rem';
        counterBadge.style.display = 'none';
        counterBadge.textContent = '0';
        
        // Add counter badge to toggle
        notificationsToggle.style.position = 'relative';
        notificationsToggle.appendChild(counterBadge);
    }
}

/**
 * Load notifications from server
 */
function loadNotifications() {
    // Fetch notifications from the server
    fetch('api/notifications/get_notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Store notifications
                notifications = data.notifications;
                
                // Count unread notifications
                unreadCount = notifications.filter(notification => !notification.read).length;
                
                // Update notifications counter
                updateNotificationsCounter();
                
                // Render notifications in dropdown
                renderNotifications();
            } else {
                console.error('Error loading notifications:', data.message);
            }
        })
        .catch(error => {
            console.error('Error fetching notifications:', error);
        });
}

/**
 * Check for new notifications
 */
function checkNewNotifications() {
    if (!notificationsInitialized) return;
    
    // Fetch new notifications from the server
    fetch('api/notifications/get_new_notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.notifications.length > 0) {
                // Add new notifications to the list
                notifications = [...data.notifications, ...notifications];
                
                // Update unread count
                unreadCount += data.notifications.length;
                
                // Update notifications counter
                updateNotificationsCounter();
                
                // Render notifications in dropdown
                renderNotifications();
                
                // Show notification toast for each new notification
                data.notifications.forEach(notification => {
                    showNotificationToast(notification);
                });
            }
        })
        .catch(error => {
            console.error('Error checking new notifications:', error);
        });
}

/**
 * Update notifications counter
 */
function updateNotificationsCounter() {
    const counter = document.querySelector('.notification-counter');
    
    if (counter) {
        if (unreadCount > 0) {
            counter.textContent = unreadCount > 99 ? '99+' : unreadCount;
            counter.style.display = 'block';
        } else {
            counter.style.display = 'none';
        }
    }
}

/**
 * Render notifications in dropdown
 */
function renderNotifications() {
    const container = document.getElementById('notificationsContainer');
    
    if (!container) return;
    
    // Clear container
    container.innerHTML = '';
    
    if (notifications.length === 0) {
        // Show no notifications message
        container.innerHTML = '<div class="text-center p-3 text-muted">لا توجد إشعارات</div>';
        return;
    }
    
    // Add notifications to container
    notifications.slice(0, 10).forEach(notification => {
        const notificationItem = document.createElement('div');
        notificationItem.className = 'dropdown-item notification-item p-2 border-bottom';
        if (!notification.read) {
            notificationItem.classList.add('bg-light');
        }
        
        // Create notification content
        notificationItem.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="notification-icon me-3 ${getNotificationIconClass(notification.type)}">
                    <i class="${getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content flex-grow-1">
                    <div class="notification-title fw-bold">${notification.title}</div>
                    <div class="notification-text small">${notification.message}</div>
                    <div class="notification-time text-muted small">${formatNotificationTime(notification.created_at)}</div>
                </div>
                ${!notification.read ? '<div class="notification-badge"><span class="badge bg-primary">جديد</span></div>' : ''}
            </div>
        `;
        
        // Add click event to mark as read and navigate
        notificationItem.addEventListener('click', function() {
            markNotificationAsRead(notification.id);
            if (notification.link) {
                window.location.href = notification.link;
            }
        });
        
        // Add notification to container
        container.appendChild(notificationItem);
    });
    
    // Add "View All" link if there are more than 10 notifications
    if (notifications.length > 10) {
        const viewAllLink = document.createElement('div');
        viewAllLink.className = 'dropdown-item text-center p-2';
        viewAllLink.innerHTML = '<a href="notifications.php" class="text-primary">عرض جميع الإشعارات</a>';
        container.appendChild(viewAllLink);
    }
}

/**
 * Mark notification as read
 * 
 * @param {number} notificationId Notification ID
 */
function markNotificationAsRead(notificationId) {
    // Find notification in the list
    const index = notifications.findIndex(notification => notification.id === notificationId);
    
    if (index !== -1 && !notifications[index].read) {
        // Mark as read locally
        notifications[index].read = true;
        
        // Update unread count
        unreadCount--;
        
        // Update notifications counter
        updateNotificationsCounter();
        
        // Render notifications in dropdown
        renderNotifications();
        
        // Send request to server to mark as read
        fetch('api/notifications/mark_as_read.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ notification_id: notificationId })
        })
        .catch(error => {
            console.error('Error marking notification as read:', error);
        });
    }
}

/**
 * Show notification toast
 * 
 * @param {object} notification Notification object
 */
function showNotificationToast(notification) {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toastId = 'toast-' + notification.id;
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = 'toast show animate__animated animate__fadeInUp';
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    // Set toast content
    toast.innerHTML = `
        <div class="toast-header">
            <i class="${getNotificationIcon(notification.type)} me-2 ${getNotificationIconClass(notification.type)}"></i>
            <strong class="me-auto">${notification.title}</strong>
            <small>${formatNotificationTime(notification.created_at)}</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            ${notification.message}
        </div>
    `;
    
    // Add toast to container
    toastContainer.appendChild(toast);
    
    // Initialize Bootstrap toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 5000
    });
    
    // Show toast
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

/**
 * Get notification icon based on type
 * 
 * @param {string} type Notification type
 * @return {string} Icon class
 */
function getNotificationIcon(type) {
    switch (type) {
        case 'allowance':
            return 'fas fa-money-bill-wave';
        case 'promotion':
            return 'fas fa-level-up-alt';
        case 'appreciation':
            return 'fas fa-certificate';
        case 'employee':
            return 'fas fa-user';
        case 'system':
            return 'fas fa-cog';
        default:
            return 'fas fa-bell';
    }
}

/**
 * Get notification icon class based on type
 * 
 * @param {string} type Notification type
 * @return {string} Icon class
 */
function getNotificationIconClass(type) {
    switch (type) {
        case 'allowance':
            return 'text-success';
        case 'promotion':
            return 'text-primary';
        case 'appreciation':
            return 'text-warning';
        case 'employee':
            return 'text-info';
        case 'system':
            return 'text-secondary';
        default:
            return 'text-dark';
    }
}

/**
 * Format notification time
 * 
 * @param {string} timestamp Timestamp
 * @return {string} Formatted time
 */
function formatNotificationTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    
    if (diffSec < 60) {
        return 'الآن';
    } else if (diffMin < 60) {
        return `منذ ${diffMin} دقيقة`;
    } else if (diffHour < 24) {
        return `منذ ${diffHour} ساعة`;
    } else if (diffDay < 7) {
        return `منذ ${diffDay} يوم`;
    } else {
        return date.toLocaleDateString('ar-SA');
    }
}
