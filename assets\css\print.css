/**
 * Print Stylesheet
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

/* Hide elements not needed for printing */
header,
.sidebar,
.navbar,
.btn,
.no-print,
footer,
.card-header .btn,
.card-footer,
.dropdown-menu,
.pagination,
.alert {
    display: none !important;
}

/* Adjust page margins and font size */
body {
    margin: 0;
    padding: 0;
    font-size: 12pt;
    background-color: #fff;
    color: #000;
}

/* Ensure container takes full width */
.container {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Show print-only elements */
.print-only {
    display: block !important;
}

/* Print header and footer */
.print-header {
    display: block !important;
    text-align: center;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.print-footer {
    display: block !important;
    text-align: center;
    border-top: 1px solid #ddd;
    padding-top: 10px;
    margin-top: 20px;
    font-size: 10pt;
}

/* Improve table printing */
.table {
    width: 100% !important;
    border-collapse: collapse !important;
    page-break-inside: auto !important;
}

.table th {
    background-color: #f2f2f2 !important;
    color: #000 !important;
    border: 1px solid #ddd !important;
}

.table td {
    border: 1px solid #ddd !important;
}

.table tr {
    page-break-inside: avoid !important;
}

/* Ensure cards print well */
.card {
    border: 1px solid #ddd !important;
    page-break-inside: avoid !important;
    margin-bottom: 20px !important;
}

.card-header {
    background-color: #f2f2f2 !important;
    color: #000 !important;
    border-bottom: 1px solid #ddd !important;
    padding: 10px !important;
}

.card-body {
    padding: 10px !important;
}

/* Add page breaks where needed */
.page-break {
    page-break-after: always;
}

/* Add footer with page numbers */
@page {
    margin: 1cm;
}

@page:first {
    margin-top: 2cm;
}

/* Print links properly */
a {
    text-decoration: none !important;
    color: #000 !important;
}

/* Print charts with proper size */
canvas {
    max-width: 100% !important;
    height: auto !important;
}

/* Print report title */
.report-title {
    text-align: center;
    font-size: 18pt;
    font-weight: bold;
    margin-bottom: 20px;
}

/* Print report date */
.report-date {
    text-align: center;
    font-size: 10pt;
    margin-bottom: 30px;
}

/* Print report footer */
.report-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    font-size: 8pt;
    border-top: 1px solid #ddd;
    padding-top: 5px;
}
