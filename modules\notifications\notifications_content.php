<!-- Notifications Content -->
<div class="p-4">
    <!-- Notifications Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="notification-stat-card unread">
                <div class="stat-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo isset($unreadCount) ? $unreadCount : 0; ?></div>
                    <div class="stat-label">تنبيهات غير مقروءة</div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="notification-stat-card today">
                <div class="stat-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo isset($todayCount) ? $todayCount : 0; ?></div>
                    <div class="stat-label">تنبيهات اليوم</div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="notification-stat-card week">
                <div class="stat-icon">
                    <i class="fas fa-calendar-week"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo isset($weekCount) ? $weekCount : 0; ?></div>
                    <div class="stat-label">تنبيهات هذا الأسبوع</div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="notification-stat-card total">
                <div class="stat-icon">
                    <i class="fas fa-list"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo isset($notifications) ? count($notifications) : 0; ?></div>
                    <div class="stat-label">إجمالي التنبيهات</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Actions -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="btn-group" role="group">
                <?php if (isset($unreadCount) && $unreadCount > 0): ?>
                    <form method="post" class="d-inline">
                        <input type="hidden" name="action" value="mark_all_read">
                        <button type="submit" class="btn btn-outline-success">
                            <i class="fas fa-check-double me-1"></i>
                            تحديد الكل كمقروء
                        </button>
                    </form>
                <?php endif; ?>

                <?php if (hasRole(['admin', 'hr_manager'])): ?>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createNotificationModal">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء تنبيه جديد
                    </button>
                <?php endif; ?>

                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-filter me-1"></i>
                        تصفية
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="filterNotifications('all')">جميع التنبيهات</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterNotifications('unread')">غير مقروءة فقط</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterNotifications('today')">اليوم فقط</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="filterNotifications('allowance')">العلاوات</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterNotifications('promotion')">الترفيعات</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterNotifications('appreciation')">كتب الشكر</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-md-4 text-md-end">
            <div class="input-group">
                <input type="text" class="form-control" id="notificationSearch" placeholder="البحث في التنبيهات...">
                <button class="btn btn-outline-secondary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-modern rounded-modern">
                <div class="card-header bg-gradient-primary">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-bell me-2"></i>قائمة التنبيهات
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (isset($notifications) && count($notifications) > 0): ?>
                        <div class="notifications-list">
                            <?php foreach ($notifications as $notification): ?>
                                <div class="notification-item <?php echo !$notification['is_read'] ? 'unread' : ''; ?>"
                                     data-type="<?php echo $notification['type']; ?>"
                                     data-date="<?php echo date('Y-m-d', strtotime($notification['created_at'])); ?>">

                                    <div class="notification-content">
                                        <div class="notification-header">
                                            <div class="notification-icon">
                                                <?php
                                                $iconClass = 'fas fa-bell';
                                                $iconColor = 'text-primary';

                                                switch ($notification['type']) {
                                                    case 'allowance':
                                                        $iconClass = 'fas fa-money-bill-wave';
                                                        $iconColor = 'text-success';
                                                        break;
                                                    case 'promotion':
                                                        $iconClass = 'fas fa-level-up-alt';
                                                        $iconColor = 'text-info';
                                                        break;
                                                    case 'appreciation':
                                                        $iconClass = 'fas fa-certificate';
                                                        $iconColor = 'text-warning';
                                                        break;
                                                    case 'retirement':
                                                        $iconClass = 'fas fa-user-clock';
                                                        $iconColor = 'text-danger';
                                                        break;
                                                    case 'update':
                                                        $iconClass = 'fas fa-edit';
                                                        $iconColor = 'text-secondary';
                                                        break;
                                                }
                                                ?>
                                                <i class="<?php echo $iconClass . ' ' . $iconColor; ?>"></i>
                                            </div>

                                            <div class="notification-meta">
                                                <h6 class="notification-title"><?php echo $notification['title']; ?></h6>
                                                <div class="notification-info">
                                                    <span class="notification-sender">
                                                        <i class="fas fa-user me-1"></i>
                                                        <?php echo $notification['sender_name'] ?? 'النظام'; ?>
                                                    </span>
                                                    <span class="notification-time">
                                                        <i class="fas fa-clock me-1"></i>
                                                        <?php echo timeAgo($notification['created_at']); ?>
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="notification-actions">
                                                <?php if (!$notification['is_read']): ?>
                                                    <form method="post" class="d-inline">
                                                        <input type="hidden" name="action" value="mark_read">
                                                        <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-success" title="تحديد كمقروء">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>

                                                <form method="post" class="d-inline">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-outline-danger"
                                                            title="حذف"
                                                            onclick="return confirm('هل أنت متأكد من حذف هذا التنبيه؟')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>

                                        <div class="notification-message">
                                            <?php echo $notification['message']; ?>
                                        </div>

                                        <div class="notification-footer">
                                            <span class="notification-type-badge badge bg-<?php
                                                echo match($notification['type']) {
                                                    'allowance' => 'success',
                                                    'promotion' => 'info',
                                                    'appreciation' => 'warning',
                                                    'retirement' => 'danger',
                                                    'update' => 'secondary',
                                                    default => 'primary'
                                                };
                                            ?>">
                                                <?php
                                                echo match($notification['type']) {
                                                    'allowance' => 'علاوة',
                                                    'promotion' => 'ترفيع',
                                                    'appreciation' => 'كتاب شكر',
                                                    'retirement' => 'تقاعد',
                                                    'update' => 'تحديث',
                                                    default => 'نظام'
                                                };
                                                ?>
                                            </span>

                                            <?php if (!$notification['is_read']): ?>
                                                <span class="badge bg-danger">جديد</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-bell-slash fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد تنبيهات</h4>
                            <p class="text-muted">لم يتم العثور على أي تنبيهات حتى الآن</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Notification Modal -->
<?php if (hasRole(['admin', 'hr_manager'])): ?>
<div class="modal fade" id="createNotificationModal" tabindex="-1" aria-labelledby="createNotificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-gradient-primary">
                <h5 class="modal-title text-white" id="createNotificationModalLabel">
                    <i class="fas fa-plus me-2"></i>إنشاء تنبيه جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create">

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="notificationTitle" class="form-label">عنوان التنبيه</label>
                            <input type="text" class="form-control" id="notificationTitle" name="title" required>
                        </div>
                        <div class="col-md-6">
                            <label for="notificationType" class="form-label">نوع التنبيه</label>
                            <select class="form-select" id="notificationType" name="type" required>
                                <option value="system">نظام</option>
                                <option value="allowance">علاوة</option>
                                <option value="promotion">ترفيع</option>
                                <option value="appreciation">كتاب شكر</option>
                                <option value="retirement">تقاعد</option>
                                <option value="update">تحديث</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notificationMessage" class="form-label">رسالة التنبيه</label>
                        <textarea class="form-control" id="notificationMessage" name="message" rows="4" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="targetUser" class="form-label">المستخدم المستهدف</label>
                        <select class="form-select" id="targetUser" name="target_user">
                            <option value="">جميع المستخدمين</option>
                            <?php
                            try {
                                $usersStmt = $pdo->query("SELECT id, full_name, username FROM users WHERE is_active = 1 ORDER BY full_name ASC");
                                $users = $usersStmt->fetchAll();
                                foreach ($users as $user):
                            ?>
                                <option value="<?php echo $user['id']; ?>">
                                    <?php echo $user['full_name'] . ' (' . $user['username'] . ')'; ?>
                                </option>
                            <?php endforeach; } catch (Exception $e) { /* ignore */ } ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إنشاء التنبيه</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// Notification filtering
function filterNotifications(filter) {
    const notifications = document.querySelectorAll('.notification-item');

    notifications.forEach(notification => {
        let show = true;

        switch (filter) {
            case 'unread':
                show = notification.classList.contains('unread');
                break;
            case 'today':
                const today = new Date().toISOString().split('T')[0];
                show = notification.dataset.date === today;
                break;
            case 'all':
                show = true;
                break;
            default:
                show = notification.dataset.type === filter;
        }

        notification.style.display = show ? 'block' : 'none';
    });
}

// Search functionality
document.getElementById('notificationSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const notifications = document.querySelectorAll('.notification-item');

    notifications.forEach(notification => {
        const title = notification.querySelector('.notification-title').textContent.toLowerCase();
        const message = notification.querySelector('.notification-message').textContent.toLowerCase();
        const sender = notification.querySelector('.notification-sender').textContent.toLowerCase();

        const matches = title.includes(searchTerm) || message.includes(searchTerm) || sender.includes(searchTerm);
        notification.style.display = matches ? 'block' : 'none';
    });
});

// Auto-refresh notifications every 30 seconds
setInterval(() => {
    // Only refresh if we're on the notifications tab
    if (document.getElementById('notifications-tab').classList.contains('active')) {
        location.reload();
    }
}, 30000);
</script>
