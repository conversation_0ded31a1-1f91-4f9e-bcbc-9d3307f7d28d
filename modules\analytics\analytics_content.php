<!-- Analytics Content -->
<div class="p-4">
    <!-- Analytics Header -->
    <?php if (isset($usingDummyData) && $usingDummyData): ?>
        <div class="alert alert-warning mb-4">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> يتم عرض بيانات تجريبية لأن بعض الجداول غير موجودة في قاعدة البيانات.
        </div>
    <?php endif; ?>

    <!-- Key Performance Indicators -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="analytics-card primary">
                <div class="analytics-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="analytics-content">
                    <div class="analytics-number"><?php echo number_format($totalEmployees); ?></div>
                    <div class="analytics-label">إجمالي الموظفين</div>
                    <div class="analytics-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+5.2%</span>
                        <small class="text-muted">من الشهر الماضي</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="analytics-card success">
                <div class="analytics-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="analytics-content">
                    <div class="analytics-number"><?php echo $upcomingAllowances['current'] ?? 0; ?></div>
                    <div class="analytics-label">مستحقي العلاوة حالياً</div>
                    <div class="analytics-trend">
                        <i class="fas fa-arrow-down text-danger"></i>
                        <span class="text-danger">-2.1%</span>
                        <small class="text-muted">من الشهر الماضي</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="analytics-card info">
                <div class="analytics-icon">
                    <i class="fas fa-level-up-alt"></i>
                </div>
                <div class="analytics-content">
                    <div class="analytics-number"><?php echo $upcomingPromotions['current'] ?? 0; ?></div>
                    <div class="analytics-label">مستحقي الترفيع حالياً</div>
                    <div class="analytics-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+1.8%</span>
                        <small class="text-muted">من الشهر الماضي</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="analytics-card warning">
                <div class="analytics-icon">
                    <i class="fas fa-certificate"></i>
                </div>
                <div class="analytics-content">
                    <div class="analytics-number"><?php echo array_sum(array_column($appreciationByType, 'count')); ?></div>
                    <div class="analytics-label">كتب الشكر هذا العام</div>
                    <div class="analytics-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+12.5%</span>
                        <small class="text-muted">من العام الماضي</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 1 -->
    <div class="row mb-4">
        <!-- Employees by Grade Chart -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100 shadow-modern rounded-modern">
                <div class="card-header bg-gradient-primary">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-chart-bar me-2"></i>توزيع الموظفين حسب الدرجة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="employeesByGradeAnalyticsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employees by Education Chart -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100 shadow-modern rounded-modern">
                <div class="card-header bg-gradient-success">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-graduation-cap me-2"></i>توزيع الموظفين حسب المؤهل العلمي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="employeesByEducationChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="row mb-4">
        <!-- Monthly Allowances and Promotions Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card h-100 shadow-modern rounded-modern">
                <div class="card-header bg-gradient-info">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-chart-line me-2"></i>العلاوات والترفيعات الشهرية لعام <?php echo $year; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="monthlyAllowancesPromotionsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Appreciation Letters Chart -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100 shadow-modern rounded-modern">
                <div class="card-header bg-gradient-warning">
                    <h5 class="mb-0 text-dark">
                        <i class="fas fa-award me-2"></i>كتب الشكر حسب النوع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="appreciationByTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Activities Analysis -->
    <div class="row mb-4">
        <!-- Upcoming Allowances Timeline -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100 shadow-modern rounded-modern">
                <div class="card-header bg-gradient-success">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-calendar-alt me-2"></i>جدولة العلاوات القادمة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline-chart">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6>مستحق حالياً</h6>
                                <span class="badge bg-danger"><?php echo $upcomingAllowances['current'] ?? 0; ?> موظف</span>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <h6>خلال 30 يوم</h6>
                                <span class="badge bg-warning"><?php echo $upcomingAllowances['within30Days'] ?? 0; ?> موظف</span>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6>خلال 60 يوم</h6>
                                <span class="badge bg-info"><?php echo $upcomingAllowances['within60Days'] ?? 0; ?> موظف</span>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6>خلال 90 يوم</h6>
                                <span class="badge bg-primary"><?php echo $upcomingAllowances['within90Days'] ?? 0; ?> موظف</span>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-secondary"></div>
                            <div class="timeline-content">
                                <h6>أكثر من 90 يوم</h6>
                                <span class="badge bg-secondary"><?php echo $upcomingAllowances['beyond90Days'] ?? 0; ?> موظف</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upcoming Promotions Timeline -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100 shadow-modern rounded-modern">
                <div class="card-header bg-gradient-primary">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-calendar-check me-2"></i>جدولة الترفيعات القادمة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline-chart">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6>مستحق حالياً</h6>
                                <span class="badge bg-danger"><?php echo $upcomingPromotions['current'] ?? 0; ?> موظف</span>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <h6>خلال 30 يوم</h6>
                                <span class="badge bg-warning"><?php echo $upcomingPromotions['within30Days'] ?? 0; ?> موظف</span>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6>خلال 60 يوم</h6>
                                <span class="badge bg-info"><?php echo $upcomingPromotions['within60Days'] ?? 0; ?> موظف</span>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6>خلال 90 يوم</h6>
                                <span class="badge bg-primary"><?php echo $upcomingPromotions['within90Days'] ?? 0; ?> موظف</span>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-secondary"></div>
                            <div class="timeline-content">
                                <h6>أكثر من 90 يوم</h6>
                                <span class="badge bg-secondary"><?php echo $upcomingPromotions['beyond90Days'] ?? 0; ?> موظف</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-modern rounded-modern">
                <div class="card-header bg-gradient-dark">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-building me-2"></i>إحصائيات الأقسام التفصيلية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>القسم</th>
                                    <th>إجمالي الموظفين</th>
                                    <th>مستحقي العلاوة</th>
                                    <th>مستحقي الترفيع</th>
                                    <th>متوسط سنوات الخدمة</th>
                                    <th>كتب الشكر</th>
                                    <th>معدل الأداء</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($departmentStats as $dept): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="department-icon me-2">
                                                    <i class="fas fa-building text-primary"></i>
                                                </div>
                                                <strong><?php echo $dept['name']; ?></strong>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo $dept['total_employees']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo $dept['eligible_allowances']; ?></span>
                                            <?php if ($dept['total_employees'] > 0): ?>
                                                <small class="text-muted d-block">
                                                    (<?php echo round(($dept['eligible_allowances'] / $dept['total_employees']) * 100, 1); ?>%)
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $dept['eligible_promotions']; ?></span>
                                            <?php if ($dept['total_employees'] > 0): ?>
                                                <small class="text-muted d-block">
                                                    (<?php echo round(($dept['eligible_promotions'] / $dept['total_employees']) * 100, 1); ?>%)
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="fw-bold"><?php echo round($dept['avg_years_service'], 1); ?></span>
                                            <small class="text-muted">سنة</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><?php echo $dept['total_appreciation']; ?></span>
                                        </td>
                                        <td>
                                            <?php
                                            // Calculate performance score based on various factors
                                            $performanceScore = 0;
                                            if ($dept['total_employees'] > 0) {
                                                $allowanceRate = ($dept['eligible_allowances'] / $dept['total_employees']) * 100;
                                                $promotionRate = ($dept['eligible_promotions'] / $dept['total_employees']) * 100;
                                                $appreciationRate = ($dept['total_appreciation'] / $dept['total_employees']) * 100;

                                                $performanceScore = ($allowanceRate * 0.3) + ($promotionRate * 0.3) + ($appreciationRate * 0.4);
                                                $performanceScore = min(100, $performanceScore);
                                            }

                                            $scoreClass = 'secondary';
                                            if ($performanceScore >= 80) $scoreClass = 'success';
                                            elseif ($performanceScore >= 60) $scoreClass = 'warning';
                                            elseif ($performanceScore >= 40) $scoreClass = 'info';
                                            else $scoreClass = 'danger';
                                            ?>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-<?php echo $scoreClass; ?>"
                                                     role="progressbar"
                                                     style="width: <?php echo $performanceScore; ?>%">
                                                    <?php echo round($performanceScore, 1); ?>%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Pass PHP data to JavaScript
window.analyticsData = {
    employeesByGrade: <?php echo json_encode($employeesByGrade); ?>,
    employeesByEducation: <?php echo json_encode($employeesByEducation); ?>,
    allowancesByMonth: <?php echo json_encode($allowancesByMonth); ?>,
    promotionsByMonth: <?php echo json_encode($promotionsByMonth); ?>,
    appreciationByType: <?php echo json_encode($appreciationByType); ?>
};
</script>

<!-- Analytics Charts JavaScript -->
<script src="assets/js/analytics-charts.js"></script>