<?php
/**
 * إضافة عمود employee_id إلى جدول التنبيهات
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

try {
    // Check if notifications table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'notifications'");
    $tableExists = $stmt->rowCount() > 0;
    
    echo "<h2>تحديث جدول التنبيهات</h2>";
    
    if ($tableExists) {
        // Check if employee_id column already exists
        $stmt = $pdo->query("DESCRIBE notifications");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $hasEmployeeIdColumn = false;
        
        foreach ($columns as $column) {
            if ($column['Field'] === 'employee_id') {
                $hasEmployeeIdColumn = true;
                break;
            }
        }
        
        if (!$hasEmployeeIdColumn) {
            // Add employee_id column
            $pdo->exec("ALTER TABLE notifications ADD COLUMN employee_id INT AFTER id");
            echo "<p style='color: green;'>تم إضافة عمود employee_id بنجاح!</p>";
            
            // Add foreign key constraint
            try {
                $pdo->exec("ALTER TABLE notifications ADD CONSTRAINT fk_notifications_employee 
                            FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE");
                echo "<p style='color: green;'>تم إضافة قيد المفتاح الأجنبي بنجاح!</p>";
            } catch (PDOException $e) {
                echo "<p style='color: orange;'>تحذير: لم يتم إضافة قيد المفتاح الأجنبي. " . $e->getMessage() . "</p>";
            }
            
            // Show updated table structure
            $stmt = $pdo->query("DESCRIBE notifications");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>هيكل الجدول بعد التحديث:</h3>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . $column['Field'] . "</td>";
                echo "<td>" . $column['Type'] . "</td>";
                echo "<td>" . $column['Null'] . "</td>";
                echo "<td>" . $column['Key'] . "</td>";
                echo "<td>" . $column['Default'] . "</td>";
                echo "<td>" . $column['Extra'] . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>عمود employee_id موجود بالفعل في الجدول.</p>";
        }
    } else {
        echo "<p style='color: red;'>جدول التنبيهات غير موجود!</p>";
    }
} catch (PDOException $e) {
    echo "<h2 style='color: red;'>خطأ في قاعدة البيانات:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
