<?php
/**
 * Salary Stages Management Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and admin role
requireRole(['admin']);

// Get stages data
try {
    $stmt = $pdo->query("
        SELECT s.*, g.name as grade_name
        FROM stages s
        JOIN grades g ON s.grade_id = g.id
        ORDER BY s.grade_id ASC, s.stage_number ASC
    ");
    $stages = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Stages Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات المراحل', 'alert alert-danger');
    $stages = [];
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $gradeId = (int)$_POST['grade_id'];
    $stageNumber = (int)$_POST['stage_number'];
    $nominalSalary = (float)$_POST['nominal_salary'];
    
    // Validate inputs
    $errors = [];
    
    if ($gradeId <= 0 || $gradeId > 10) {
        $errors[] = 'الدرجة غير صالحة';
    }
    
    if ($stageNumber <= 0 || $stageNumber > 7) {
        $errors[] = 'المرحلة غير صالحة';
    }
    
    if ($nominalSalary <= 0) {
        $errors[] = 'الراتب الاسمي يجب أن يكون أكبر من صفر';
    }
    
    // Check if stage already exists
    try {
        $checkStmt = $pdo->prepare("
            SELECT id FROM stages 
            WHERE grade_id = :grade_id AND stage_number = :stage_number
        ");
        $checkStmt->execute([
            ':grade_id' => $gradeId,
            ':stage_number' => $stageNumber
        ]);
        
        if ($checkStmt->rowCount() > 0) {
            $stageId = $checkStmt->fetch()['id'];
            
            // Update existing stage
            $updateStmt = $pdo->prepare("
                UPDATE stages 
                SET nominal_salary = :nominal_salary 
                WHERE id = :id
            ");
            
            $updateStmt->execute([
                ':nominal_salary' => $nominalSalary,
                ':id' => $stageId
            ]);
            
            flash('success_message', 'تم تحديث الراتب الاسمي بنجاح', 'alert alert-success');
        } else {
            // Insert new stage
            $insertStmt = $pdo->prepare("
                INSERT INTO stages (grade_id, stage_number, nominal_salary)
                VALUES (:grade_id, :stage_number, :nominal_salary)
            ");
            
            $insertStmt->execute([
                ':grade_id' => $gradeId,
                ':stage_number' => $stageNumber,
                ':nominal_salary' => $nominalSalary
            ]);
            
            flash('success_message', 'تمت إضافة المرحلة بنجاح', 'alert alert-success');
        }
        
        // Redirect to refresh the page
        redirect('stages.php');
    } catch (PDOException $e) {
        error_log("Save Stage Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء حفظ بيانات المرحلة', 'alert alert-danger');
    }
}

// Get grades
try {
    $gradesStmt = $pdo->query("SELECT id, name FROM grades ORDER BY id ASC");
    $grades = $gradesStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Grades Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الدرجات', 'alert alert-danger');
    $grades = [];
    
    // If grades table doesn't exist, create dummy grades
    if (strpos($e->getMessage(), "Table") !== false && strpos($e->getMessage(), "doesn't exist") !== false) {
        $grades = [];
        for ($i = 1; $i <= 10; $i++) {
            $grades[] = ['id' => $i, 'name' => 'الدرجة ' . $i];
        }
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-money-bill-wave me-2"></i> جدول الرواتب الاسمية
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStageModal">
            <i class="fas fa-plus me-1"></i> إضافة/تعديل مرحلة
        </button>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى الرئيسية
        </a>
    </div>
</div>

<!-- Salary Stages Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>الدرجة</th>
                        <th>المرحلة</th>
                        <th>الراتب الاسمي</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (count($stages) > 0): ?>
                        <?php foreach ($stages as $stage): ?>
                            <tr>
                                <td>الدرجة <?php echo $stage['grade_id']; ?></td>
                                <td>المرحلة <?php echo $stage['stage_number']; ?></td>
                                <td><?php echo number_format($stage['nominal_salary'], 2); ?> دينار</td>
                                <td>
                                    <button class="btn btn-sm btn-warning edit-stage-btn" 
                                            data-grade-id="<?php echo $stage['grade_id']; ?>"
                                            data-stage-number="<?php echo $stage['stage_number']; ?>"
                                            data-nominal-salary="<?php echo $stage['nominal_salary']; ?>">
                                        <i class="fas fa-edit"></i> تعديل
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="4" class="text-center">لا توجد بيانات للمراحل</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add/Edit Stage Modal -->
<div class="modal fade" id="addStageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة/تعديل مرحلة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="stageForm" action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                    <div class="mb-3">
                        <label for="grade_id" class="form-label">الدرجة <span class="text-danger">*</span></label>
                        <select class="form-select" id="grade_id" name="grade_id" required>
                            <option value="">اختر الدرجة</option>
                            <?php for ($i = 1; $i <= 10; $i++): ?>
                                <option value="<?php echo $i; ?>">الدرجة <?php echo $i; ?></option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="stage_number" class="form-label">المرحلة <span class="text-danger">*</span></label>
                        <select class="form-select" id="stage_number" name="stage_number" required>
                            <option value="">اختر المرحلة</option>
                            <?php for ($i = 1; $i <= 7; $i++): ?>
                                <option value="<?php echo $i; ?>">المرحلة <?php echo $i; ?></option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="nominal_salary" class="form-label">الراتب الاسمي <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="nominal_salary" name="nominal_salary" step="0.01" min="0" required>
                            <span class="input-group-text">دينار</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" form="stageForm" class="btn btn-primary">حفظ</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Edit stage button click
        const editButtons = document.querySelectorAll('.edit-stage-btn');
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const gradeId = this.getAttribute('data-grade-id');
                const stageNumber = this.getAttribute('data-stage-number');
                const nominalSalary = this.getAttribute('data-nominal-salary');
                
                document.getElementById('grade_id').value = gradeId;
                document.getElementById('stage_number').value = stageNumber;
                document.getElementById('nominal_salary').value = nominalSalary;
                
                const modal = new bootstrap.Modal(document.getElementById('addStageModal'));
                modal.show();
            });
        });
    });
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
