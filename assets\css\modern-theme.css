/**
 * Modern Theme Enhancements
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 * Additional modern UI components and utilities
 */

/* Enhanced Form Styles */
.form-control {
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    font-size: 1rem;
    transition: all var(--transition-fast);
    background-color: white;
}

.form-control:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.input-group-text {
    background-color: var(--gray-100);
    border: 2px solid var(--gray-200);
    color: var(--gray-600);
    font-weight: 500;
}

/* Enhanced Table Styles */
.table {
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    background: white;
}

.table thead th {
    background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
    color: var(--gray-800);
    font-weight: 600;
    border: none;
    padding: var(--spacing-lg);
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
}

.table tbody td {
    padding: var(--spacing-lg);
    border-color: var(--gray-100);
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: var(--primary-50);
    transform: scale(1.01);
    transition: all var(--transition-fast);
}

/* Enhanced Alert Styles */
.alert {
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border-left: 4px solid;
}

.alert-success {
    background: linear-gradient(135deg, var(--success-50), var(--success-100));
    color: var(--success-800);
    border-left-color: var(--success-500);
}

.alert-danger {
    background: linear-gradient(135deg, var(--danger-50), var(--danger-100));
    color: var(--danger-800);
    border-left-color: var(--danger-500);
}

.alert-warning {
    background: linear-gradient(135deg, var(--warning-50), var(--warning-100));
    color: var(--warning-800);
    border-left-color: var(--warning-500);
}

.alert-info {
    background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
    color: var(--primary-800);
    border-left-color: var(--primary-500);
}

/* Enhanced Badge Styles */
.badge {
    padding: var(--spacing-sm) var(--spacing-md);
    font-weight: 500;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge.bg-success {
    background: linear-gradient(135deg, var(--success-500), var(--success-600)) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, var(--danger-500), var(--danger-600)) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, var(--warning-500), var(--warning-600)) !important;
    color: var(--gray-800) !important;
}

.badge.bg-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600)) !important;
}

/* Modern Loading Spinner */
.spinner-modern {
    width: 40px;
    height: 40px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Modal Styles */
.modal-content {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    border-bottom: none;
    padding: var(--spacing-lg);
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-footer {
    background-color: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: var(--spacing-lg);
}

/* Status Indicators */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-left: var(--spacing-sm);
    box-shadow: var(--shadow-sm);
}

.status-active {
    background: linear-gradient(135deg, var(--success-400), var(--success-500));
}

.status-pending {
    background: linear-gradient(135deg, var(--warning-400), var(--warning-500));
}

.status-expired {
    background: linear-gradient(135deg, var(--danger-400), var(--danger-500));
}

/* Enhanced Pagination */
.pagination .page-link {
    border: none;
    color: var(--gray-600);
    padding: var(--spacing-sm) var(--spacing-md);
    margin: 0 var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.pagination .page-link:hover {
    background-color: var(--primary-100);
    color: var(--primary-700);
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-color: var(--primary-500);
    box-shadow: var(--shadow-md);
}

/* Utility Classes */
.text-gradient-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-700));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600)) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-500), var(--success-600)) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-500), var(--warning-600)) !important;
}

.bg-gradient-danger {
    background: linear-gradient(135deg, var(--danger-500), var(--danger-600)) !important;
}

.shadow-modern {
    box-shadow: var(--shadow-lg) !important;
}

.rounded-modern {
    border-radius: var(--radius-xl) !important;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .dashboard-card {
        padding: var(--spacing-lg);
    }
    
    .dashboard-card .icon {
        font-size: 2.5rem;
    }
    
    .dashboard-card .count {
        font-size: 2rem;
    }
    
    .modern-brand .brand-text {
        display: none;
    }
    
    .card-body {
        padding: var(--spacing-md);
    }
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #1f2937;
        --gray-100: #374151;
        --gray-200: #4b5563;
        --gray-800: #f9fafb;
        --gray-900: #ffffff;
    }
    
    body {
        background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
        color: var(--gray-100);
    }
    
    .card {
        background-color: var(--gray-800);
        border-color: var(--gray-700);
    }
    
    .table {
        background-color: var(--gray-800);
        color: var(--gray-100);
    }
}
