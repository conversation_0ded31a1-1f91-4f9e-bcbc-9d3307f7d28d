/**
 * Print Stylesheet
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

@media print {
    /* Hide elements not needed for printing */
    header, 
    .sidebar,
    .navbar,
    .btn,
    .no-print,
    footer,
    .card-header .btn,
    .card-footer,
    .dropdown-menu,
    .pagination,
    .alert {
        display: none !important;
    }
    
    /* Adjust layout for printing */
    body {
        background-color: white !important;
        font-size: 12pt;
        color: black !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    
    .container-fluid,
    .container {
        width: 100%;
        padding: 0 !important;
        margin: 0 !important;
    }
    
    .content-wrapper {
        margin-right: 0 !important;
        padding: 0 !important;
        width: 100% !important;
    }
    
    /* Adjust card styles */
    .card {
        border: none !important;
        box-shadow: none !important;
        margin-bottom: 20px !important;
    }
    
    .card-header {
        background-color: #f8f9fa !important;
        color: black !important;
        border-bottom: 1px solid #ddd !important;
        padding: 10px 15px !important;
    }
    
    .card-body {
        padding: 15px !important;
    }
    
    /* Table styles */
    table {
        width: 100% !important;
        border-collapse: collapse !important;
    }
    
    table th {
        background-color: #f8f9fa !important;
        color: black !important;
        border: 1px solid #ddd !important;
        padding: 8px !important;
        font-weight: bold !important;
    }
    
    table td {
        border: 1px solid #ddd !important;
        padding: 8px !important;
    }
    
    /* Add page breaks */
    .page-break {
        page-break-before: always;
    }
    
    /* Report specific styles */
    .report-header {
        text-align: center;
        margin-bottom: 20px;
    }
    
    .report-header h1 {
        font-size: 18pt;
        margin-bottom: 5px;
    }
    
    .report-header p {
        font-size: 12pt;
        margin-bottom: 0;
    }
    
    .report-footer {
        text-align: center;
        margin-top: 20px;
        font-size: 10pt;
        border-top: 1px solid #ddd;
        padding-top: 10px;
    }
    
    /* Print URL */
    @page {
        size: A4;
        margin: 1cm;
    }
    
    /* Ensure background colors print */
    * {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    /* Fix for Bootstrap columns */
    .row {
        display: block !important;
    }
    
    .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
    .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
        float: left !important;
        width: 100% !important;
    }
    
    /* Charts */
    canvas {
        max-width: 100% !important;
        height: auto !important;
    }
    
    /* Links */
    a {
        text-decoration: none !important;
        color: black !important;
    }
    
    /* Add print header and footer */
    .print-header {
        display: block !important;
        text-align: center;
        border-bottom: 1px solid #ddd;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    
    .print-footer {
        display: block !important;
        text-align: center;
        border-top: 1px solid #ddd;
        padding-top: 10px;
        margin-top: 20px;
        font-size: 10pt;
    }
    
    /* Show print-only elements */
    .print-only {
        display: block !important;
    }
}
