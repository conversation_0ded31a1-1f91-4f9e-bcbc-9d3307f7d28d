<?php
/**
 * API: Employees by Grade Chart Data
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Define root path
$root_path = dirname(dirname(__DIR__));

// Include required files
require_once $root_path . '/config/config.php';
require_once $root_path . '/config/database.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// Require login
requireLogin();

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Check if employees table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'employees'");

    if ($tableCheck->rowCount() > 0) {
        // Get employees by grade
        $stmt = $pdo->query("
            SELECT current_grade, COUNT(*) as count
            FROM employees
            GROUP BY current_grade
            ORDER BY current_grade ASC
        ");

        $data = $stmt->fetchAll();

        // Format data for chart
        $labels = [];
        $values = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                $labels[] = 'الدرجة ' . $row['current_grade'];
                $values[] = (int)$row['count'];
            }
        } else {
            // Return dummy data if no data found
            $labels = ['الدرجة 1', 'الدرجة 2', 'الدرجة 3', 'الدرجة 4', 'الدرجة 5', 'الدرجة 6', 'الدرجة 7', 'الدرجة 8', 'الدرجة 9', 'الدرجة 10'];
            $values = [5, 8, 12, 15, 20, 25, 18, 14, 10, 7];
        }
    } else {
        // Return dummy data if table doesn't exist
        $labels = ['الدرجة 1', 'الدرجة 2', 'الدرجة 3', 'الدرجة 4', 'الدرجة 5', 'الدرجة 6', 'الدرجة 7', 'الدرجة 8', 'الدرجة 9', 'الدرجة 10'];
        $values = [5, 8, 12, 15, 20, 25, 18, 14, 10, 7];
    }

    // Return JSON response
    echo json_encode([
        'labels' => $labels,
        'values' => $values
    ]);
} catch (PDOException $e) {
    // Log error
    error_log("API Error (employees_by_grade): " . $e->getMessage());

    // Return dummy data instead of error
    $labels = ['الدرجة 1', 'الدرجة 2', 'الدرجة 3', 'الدرجة 4', 'الدرجة 5', 'الدرجة 6', 'الدرجة 7', 'الدرجة 8', 'الدرجة 9', 'الدرجة 10'];
    $values = [5, 8, 12, 15, 20, 25, 18, 14, 10, 7];

    echo json_encode([
        'labels' => $labels,
        'values' => $values
    ]);
}
?>
