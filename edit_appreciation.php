<?php
/**
 * Edit Appreciation Letter Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and proper role
requireRole(['admin', 'hr']);

// Check if appreciation letter ID is provided
$letterId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($letterId <= 0) {
    flash('error_message', 'معرف كتاب الشكر غير صحيح', 'alert alert-danger');
    redirect('appreciation.php');
}

// Get appreciation letter details
try {
    $stmt = $pdo->prepare("
        SELECT a.*, e.full_name, e.employee_number, e.next_allowance_date, e.next_promotion_date, d.name as department_name, e.current_grade
        FROM appreciation_letters a
        JOIN employees e ON a.employee_id = e.id
        JOIN departments d ON e.department_id = d.id
        WHERE a.id = :id
    ");
    $stmt->execute([':id' => $letterId]);
    $letter = $stmt->fetch();
    
    if (!$letter) {
        flash('error_message', 'كتاب الشكر غير موجود', 'alert alert-danger');
        redirect('appreciation.php');
    }
} catch (PDOException $e) {
    error_log("Get Appreciation Letter Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات كتاب الشكر', 'alert alert-danger');
    redirect('appreciation.php');
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $letterNumber = sanitize($_POST['letter_number']);
    $letterDate = sanitize($_POST['letter_date']);
    $issuer = sanitize($_POST['issuer']);
    $notes = sanitize($_POST['notes']);
    
    // Set months reduction based on issuer
    $newMonthsReduction = ($issuer === 'prime_minister') ? PM_LETTER_MONTHS : REGULAR_LETTER_MONTHS;
    $oldMonthsReduction = $letter['months_reduction'];
    
    // Validate inputs
    $errors = [];
    
    if (empty($letterNumber)) {
        $errors[] = 'رقم الكتاب مطلوب';
    }
    
    if (empty($letterDate)) {
        $errors[] = 'تاريخ الكتاب مطلوب';
    }
    
    // Handle file upload
    $filePath = $letter['file_path'];
    if (isset($_FILES['letter_file']) && $_FILES['letter_file']['error'] === UPLOAD_ERR_OK) {
        $fileName = $_FILES['letter_file']['name'];
        $fileSize = $_FILES['letter_file']['size'];
        $fileTmpName = $_FILES['letter_file']['tmp_name'];
        $fileType = $_FILES['letter_file']['type'];
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        // Check file size
        if ($fileSize > MAX_FILE_SIZE) {
            $errors[] = 'حجم الملف يجب أن يكون أقل من ' . (MAX_FILE_SIZE / 1024 / 1024) . ' ميجابايت';
        }
        
        // Check file extension
        if (!in_array($fileExt, ALLOWED_FILE_TYPES)) {
            $errors[] = 'نوع الملف غير مسموح به. الأنواع المسموح بها: ' . implode(', ', ALLOWED_FILE_TYPES);
        }
        
        // If no errors, move file to uploads directory
        if (empty($errors)) {
            $newFileName = uniqid() . '_' . $fileName;
            $uploadPath = UPLOAD_DIR . 'appreciation_letters/' . $newFileName;
            
            if (move_uploaded_file($fileTmpName, $uploadPath)) {
                // Delete old file if exists
                if ($letter['file_path'] && file_exists($letter['file_path'])) {
                    @unlink($letter['file_path']);
                }
                $filePath = 'uploads/appreciation_letters/' . $newFileName;
            } else {
                $errors[] = 'حدث خطأ أثناء رفع الملف';
            }
        }
    }
    
    // If no errors, update appreciation letter
    if (empty($errors)) {
        try {
            // Start transaction
            $pdo->beginTransaction();
            
            // Update appreciation letter record
            $stmt = $pdo->prepare("
                UPDATE appreciation_letters SET
                    letter_date = :letter_date,
                    letter_number = :letter_number,
                    issuer = :issuer,
                    file_path = :file_path,
                    months_reduction = :months_reduction,
                    notes = :notes
                WHERE id = :id
            ");
            
            $stmt->execute([
                ':letter_date' => $letterDate,
                ':letter_number' => $letterNumber,
                ':issuer' => $issuer,
                ':file_path' => $filePath,
                ':months_reduction' => $newMonthsReduction,
                ':notes' => $notes,
                ':id' => $letterId
            ]);
            
            // Update employee's next allowance and promotion dates if months_reduction changed
            if ($newMonthsReduction != $oldMonthsReduction) {
                // Calculate difference in months
                $monthsDiff = $newMonthsReduction - $oldMonthsReduction;
                
                // Get employee current data
                $empStmt = $pdo->prepare("
                    SELECT next_allowance_date, next_promotion_date
                    FROM employees
                    WHERE id = :id
                ");
                $empStmt->execute([':id' => $letter['employee_id']]);
                $empData = $empStmt->fetch();
                
                // Calculate new dates with adjusted reduction
                $nextAllowanceDate = new DateTime($empData['next_allowance_date']);
                $nextAllowanceDate->sub(new DateInterval('P' . $monthsDiff . 'M'));
                
                $nextPromotionDate = new DateTime($empData['next_promotion_date']);
                $nextPromotionDate->sub(new DateInterval('P' . $monthsDiff . 'M'));
                
                // Update employee record
                $updateStmt = $pdo->prepare("
                    UPDATE employees SET
                        next_allowance_date = :next_allowance_date,
                        next_promotion_date = :next_promotion_date
                    WHERE id = :id
                ");
                
                $updateStmt->execute([
                    ':next_allowance_date' => $nextAllowanceDate->format('Y-m-d'),
                    ':next_promotion_date' => $nextPromotionDate->format('Y-m-d'),
                    ':id' => $letter['employee_id']
                ]);
            }
            
            // Log activity
            logActivity(
                $_SESSION['user_id'],
                'تعديل كتاب شكر',
                'appreciation_letters',
                $letterId,
                'تم تعديل كتاب شكر للموظف: ' . $letter['full_name']
            );
            
            // Commit transaction
            $pdo->commit();
            
            // Redirect to appreciation letters page
            flash('success_message', 'تم تعديل كتاب الشكر بنجاح', 'alert alert-success');
            redirect('appreciation.php');
            
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            
            error_log("Edit Appreciation Letter Error: " . $e->getMessage());
            flash('error_message', 'حدث خطأ أثناء تعديل كتاب الشكر', 'alert alert-danger');
        }
    } else {
        // Display errors
        $errorMessage = '<ul class="mb-0">';
        foreach ($errors as $error) {
            $errorMessage .= '<li>' . $error . '</li>';
        }
        $errorMessage .= '</ul>';
        
        flash('error_message', $errorMessage, 'alert alert-danger');
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-edit me-2"></i> تعديل كتاب شكر
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="appreciation.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى قائمة كتب الشكر
        </a>
    </div>
</div>

<!-- Employee Information Card -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-user me-2"></i> معلومات الموظف
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الرقم الوظيفي:</div>
                <div><?php echo $letter['employee_number']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الاسم:</div>
                <div><?php echo $letter['full_name']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">القسم:</div>
                <div><?php echo $letter['department_name']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الدرجة الحالية:</div>
                <div>الدرجة <?php echo $letter['current_grade']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">تاريخ العلاوة القادمة:</div>
                <div><?php echo formatArabicDate($letter['next_allowance_date']); ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">تاريخ الترفيع القادم:</div>
                <div><?php echo formatArabicDate($letter['next_promotion_date']); ?></div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Appreciation Letter Form -->
<div class="card">
    <div class="card-header bg-warning text-dark">
        <h5 class="mb-0">
            <i class="fas fa-edit me-2"></i> تعديل كتاب الشكر
        </h5>
    </div>
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF'] . '?id=' . $letterId; ?>" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="letter_number" class="form-label">رقم الكتاب <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="letter_number" name="letter_number" value="<?php echo $letter['letter_number']; ?>" required>
                    <div class="invalid-feedback">يرجى إدخال رقم الكتاب</div>
                </div>
                <div class="col-md-6">
                    <label for="letter_date" class="form-label">تاريخ الكتاب <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="letter_date" name="letter_date" value="<?php echo $letter['letter_date']; ?>" required>
                    <div class="invalid-feedback">يرجى إدخال تاريخ الكتاب</div>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="issuer" class="form-label">الجهة المانحة <span class="text-danger">*</span></label>
                    <select class="form-select" id="issuer" name="issuer" required>
                        <option value="regular" <?php echo ($letter['issuer'] === 'regular') ? 'selected' : ''; ?>>كتاب شكر عادي (تخفيض شهر واحد)</option>
                        <option value="prime_minister" <?php echo ($letter['issuer'] === 'prime_minister') ? 'selected' : ''; ?>>كتاب شكر رئيس الوزراء (تخفيض 6 أشهر)</option>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار الجهة المانحة</div>
                </div>
                <div class="col-md-6">
                    <label for="letter_file" class="form-label">نسخة من الكتاب</label>
                    <input type="file" class="form-control" id="letter_file" name="letter_file">
                    <div class="form-text">
                        الملفات المسموح بها: PDF, JPG, JPEG, PNG (الحد الأقصى: <?php echo MAX_FILE_SIZE / 1024 / 1024; ?> ميجابايت)
                        <?php if ($letter['file_path']): ?>
                            <br>
                            <a href="<?php echo $letter['file_path']; ?>" target="_blank">عرض الملف الحالي</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="notes" class="form-label">ملاحظات</label>
                <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $letter['notes']; ?></textarea>
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                إذا قمت بتغيير نوع كتاب الشكر، سيتم تحديث تواريخ استحقاق العلاوة والترفيع للموظف تلقائياً.
                <ul class="mb-0 mt-2">
                    <li>كتاب الشكر العادي: تخفيض <?php echo REGULAR_LETTER_MONTHS; ?> شهر</li>
                    <li>كتاب شكر رئيس الوزراء: تخفيض <?php echo PM_LETTER_MONTHS; ?> أشهر</li>
                </ul>
            </div>
            
            <div class="text-center mt-4">
                <button type="submit" class="btn btn-warning px-5">
                    <i class="fas fa-save me-1"></i> حفظ التعديلات
                </button>
                <a href="appreciation.php" class="btn btn-secondary px-5">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
