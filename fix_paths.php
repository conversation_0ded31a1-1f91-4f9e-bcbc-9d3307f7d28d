<?php
/**
 * Fix Paths and Settings
 * إصلاح المسارات والإعدادات بعد نقل النظام
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// CSS styles
$styles = '
<style>
    body {
        font-family: Arial, sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 20px;
        direction: rtl;
    }
    .container {
        max-width: 800px;
        margin: 0 auto;
        background-color: #fff;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    h1, h2 {
        color: #007bff;
    }
    .alert-success {
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .alert-warning {
        color: #856404;
        background-color: #fff3cd;
        border-color: #ffeeba;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .btn {
        display: inline-block;
        padding: 8px 16px;
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.5;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        cursor: pointer;
        border: 1px solid transparent;
        border-radius: 4px;
        text-decoration: none;
    }
    .btn-primary {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }
    .btn-success {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    pre {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        overflow: auto;
    }
    .step {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border-left: 4px solid #007bff;
    }
    .step h3 {
        margin-top: 0;
    }
</style>
';

// Start HTML output
echo '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح المسارات والإعدادات - نظام إدارة العلاوات والترقية</title>
    ' . $styles . '
</head>
<body>
    <div class="container">
        <h1>إصلاح المسارات والإعدادات</h1>
        <p>هذه الأداة تساعد في إصلاح المسارات والإعدادات بعد نقل النظام إلى حاسوب آخر.</p>';

// Get current directory
$currentDir = dirname(__FILE__);
$rootDir = realpath($currentDir);
$htdocsDir = dirname($rootDir);
$xamppDir = dirname($htdocsDir);

echo '<div class="alert-warning">
    <h3>معلومات المسارات الحالية:</h3>
    <p><strong>المسار الحالي:</strong> ' . $currentDir . '</p>
    <p><strong>مسار النظام:</strong> ' . $rootDir . '</p>
    <p><strong>مسار htdocs:</strong> ' . $htdocsDir . '</p>
    <p><strong>مسار XAMPP:</strong> ' . $xamppDir . '</p>
</div>';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['fix_settings'])) {
        echo '<h2>جاري إصلاح الإعدادات...</h2>';
        
        $success = true;
        $messages = [];
        
        // Step 1: Check and fix config files
        echo '<div class="step">
            <h3>الخطوة 1: التحقق من ملفات الإعدادات وإصلاحها</h3>';
        
        // Fix config/config.php
        $configFile = $rootDir . '/config/config.php';
        if (file_exists($configFile)) {
            $configContent = file_get_contents($configFile);
            
            // Update APP_URL
            $serverName = $_SERVER['SERVER_NAME'];
            $serverPort = $_SERVER['SERVER_PORT'] != 80 ? ':' . $_SERVER['SERVER_PORT'] : '';
            $newAppUrl = 'http://' . $serverName . $serverPort . '/ترقيات';
            
            $pattern = "/define\('APP_URL',\s*'[^']*'\);/";
            $replacement = "define('APP_URL', '$newAppUrl');";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            
            // Update APP_ROOT
            $pattern = "/define\('APP_ROOT',\s*'[^']*'\);/";
            $replacement = "define('APP_ROOT', '$rootDir');";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            
            // Update UPLOAD_DIR
            $pattern = "/define\('UPLOAD_DIR',\s*'[^']*'\);/";
            $replacement = "define('UPLOAD_DIR', APP_ROOT . '/uploads/');";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            
            // Save updated config file
            if (file_put_contents($configFile, $configContent)) {
                $messages[] = "تم تحديث ملف config.php بنجاح.";
            } else {
                $success = false;
                $messages[] = "فشل في تحديث ملف config.php.";
            }
        } else {
            $success = false;
            $messages[] = "ملف config.php غير موجود!";
        }
        
        // Display messages
        foreach ($messages as $message) {
            echo '<p>' . $message . '</p>';
        }
        
        echo '</div>';
        
        // Step 2: Fix database connection
        echo '<div class="step">
            <h3>الخطوة 2: إصلاح الاتصال بقاعدة البيانات</h3>';
        
        // Include database configuration
        if (file_exists($rootDir . '/config/database.php')) {
            include_once $rootDir . '/config/database.php';
            
            try {
                // Test database connection
                $testStmt = $pdo->query("SELECT 1");
                echo '<p class="alert-success">تم الاتصال بقاعدة البيانات بنجاح!</p>';
            } catch (PDOException $e) {
                $success = false;
                echo '<p class="alert-danger">فشل الاتصال بقاعدة البيانات: ' . $e->getMessage() . '</p>';
                
                // Suggest creating database
                echo '<p>يرجى التأكد من وجود قاعدة البيانات وإنشائها إذا لم تكن موجودة:</p>';
                echo '<p><a href="create_database.php" class="btn btn-primary">إنشاء قاعدة البيانات</a></p>';
            }
        } else {
            $success = false;
            echo '<p class="alert-danger">ملف database.php غير موجود!</p>';
        }
        
        echo '</div>';
        
        // Step 3: Fix file permissions
        echo '<div class="step">
            <h3>الخطوة 3: إصلاح صلاحيات الملفات</h3>';
        
        // Check and create uploads directory
        $uploadsDir = $rootDir . '/uploads';
        if (!file_exists($uploadsDir)) {
            if (mkdir($uploadsDir, 0755, true)) {
                echo '<p>تم إنشاء مجلد uploads بنجاح.</p>';
            } else {
                $success = false;
                echo '<p class="alert-danger">فشل في إنشاء مجلد uploads.</p>';
            }
        } else {
            echo '<p>مجلد uploads موجود بالفعل.</p>';
            
            // Make sure it's writable
            if (is_writable($uploadsDir)) {
                echo '<p>مجلد uploads قابل للكتابة.</p>';
            } else {
                $success = false;
                echo '<p class="alert-danger">مجلد uploads غير قابل للكتابة. يرجى تعديل الصلاحيات.</p>';
            }
        }
        
        echo '</div>';
        
        // Step 4: Fix character encoding
        echo '<div class="step">
            <h3>الخطوة 4: إصلاح ترميز النصوص</h3>';
        
        // Check PHP mbstring extension
        if (extension_loaded('mbstring')) {
            echo '<p>وحدة mbstring مفعلة.</p>';
        } else {
            $success = false;
            echo '<p class="alert-danger">وحدة mbstring غير مفعلة. يرجى تفعيلها في إعدادات PHP.</p>';
        }
        
        // Check default charset in PHP
        $defaultCharset = ini_get('default_charset');
        echo '<p>الترميز الافتراضي في PHP: ' . $defaultCharset . '</p>';
        
        if ($defaultCharset != 'UTF-8') {
            echo '<p class="alert-warning">الترميز الافتراضي ليس UTF-8. قد يؤدي ذلك إلى مشاكل في عرض النصوص العربية.</p>';
        }
        
        echo '</div>';
        
        // Final result
        if ($success) {
            echo '<div class="alert-success">
                <h2>تم إصلاح الإعدادات بنجاح!</h2>
                <p>يمكنك الآن استخدام النظام بشكل طبيعي.</p>
                <p><a href="index.php" class="btn btn-primary">الذهاب إلى الصفحة الرئيسية</a></p>
            </div>';
        } else {
            echo '<div class="alert-danger">
                <h2>حدثت بعض المشاكل أثناء إصلاح الإعدادات!</h2>
                <p>يرجى مراجعة الرسائل أعلاه ومحاولة إصلاح المشاكل يدويًا.</p>
            </div>';
        }
    }
}

// Display fix form
echo '<h2>إصلاح الإعدادات</h2>
<p>انقر على الزر أدناه لإصلاح المسارات والإعدادات بعد نقل النظام:</p>
<form method="post">
    <button type="submit" name="fix_settings" class="btn btn-primary">إصلاح الإعدادات</button>
</form>

<h2>روابط مفيدة</h2>
<ul>
    <li><a href="test_connection.php">اختبار الاتصال بقاعدة البيانات</a></li>
    <li><a href="create_database.php">إنشاء قاعدة البيانات</a></li>
    <li><a href="create_user.php">إنشاء مستخدم جديد</a></li>
    <li><a href="reset_password.php">إعادة تعيين كلمة المرور</a></li>
    <li><a href="index.php">الصفحة الرئيسية</a></li>
</ul>

</div>
</body>
</html>';
?>
