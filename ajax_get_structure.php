<?php
/**
 * AJAX Get Table Structure
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include database configuration
require_once 'config/config.php';

// Start session
session_start();

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'غير مصرح لك بالوصول إلى هذه الصفحة']);
    exit;
}

// Check if table parameter is provided
if (!isset($_GET['table']) || empty($_GET['table'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'لم يتم تحديد اسم الجدول']);
    exit;
}

// Sanitize table name
$tableName = preg_replace('/[^a-zA-Z0-9_]/', '', $_GET['table']);

// Connect to database
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get table structure
    $stmt = $pdo->prepare("SHOW CREATE TABLE `$tableName`");
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$row) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => 'الجدول غير موجود']);
        exit;
    }
    
    $createTable = $row['Create Table'];
    
    // Get table data (first 10 rows)
    $stmt = $pdo->prepare("SELECT * FROM `$tableName` LIMIT 10");
    $stmt->execute();
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $insertData = '';
    
    if (count($rows) > 0) {
        // Get column names
        $columns = array_keys($rows[0]);
        $columnList = '`' . implode('`, `', $columns) . '`';
        
        // Build insert statement
        $insertData = "INSERT INTO `$tableName` ($columnList) VALUES\n";
        
        foreach ($rows as $i => $row) {
            $values = [];
            foreach ($row as $value) {
                if ($value === null) {
                    $values[] = 'NULL';
                } else {
                    $values[] = $pdo->quote($value);
                }
            }
            
            $insertData .= "(" . implode(', ', $values) . ")";
            
            if ($i < count($rows) - 1) {
                $insertData .= ",\n";
            } else {
                $insertData .= ";";
            }
        }
    } else {
        $insertData = "-- لا توجد بيانات في هذا الجدول";
    }
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'table' => $tableName,
        'create_table' => $createTable,
        'insert_data' => $insertData
    ]);
    
} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
