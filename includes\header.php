<?php
/**
 * Header Template
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include session configuration first (before starting the session)
require_once 'includes/session_config.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include other configuration files
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Get current page
$current_page = basename($_SERVER['PHP_SELF']);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?></title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.1/css/dataTables.bootstrap5.min.css">

    <!-- Animate.css for animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Modern Theme CSS -->
    <link rel="stylesheet" href="assets/css/modern-theme.css">

    <!-- Color Fix CSS -->
    <link rel="stylesheet" href="assets/css/color-fix.css">

    <!-- Enhanced Navbar CSS -->
    <link rel="stylesheet" href="assets/css/navbar-enhanced.css">

    <!-- Dropdown Position Fix CSS -->
    <link rel="stylesheet" href="assets/css/dropdown-fix.css">

    <!-- Print CSS -->
    <link rel="stylesheet" href="assets/css/print.css" media="print">

    <!-- Mobile Responsive CSS -->
    <link rel="stylesheet" href="assets/css/mobile.css" media="screen and (max-width: 768px)">
</head>
<body>
    <!-- Enhanced Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark modern-navbar">
        <div class="container">
            <a class="navbar-brand modern-brand" href="index.php">
                <div class="brand-icon">
                    <i class="fas fa-award"></i>
                </div>
                <span class="brand-text"><?php echo APP_NAME; ?></span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'index.php') ? 'active' : ''; ?>" href="index.php">
                                <i class="fas fa-home me-1"></i> الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'dashboard.php') ? 'active' : ''; ?>" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-1"></i> لوحة المعلومات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'employees.php') ? 'active' : ''; ?>" href="employees.php">
                                <i class="fas fa-users me-1"></i> الموظفين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'allowances.php') ? 'active' : ''; ?>" href="allowances.php">
                                <i class="fas fa-money-bill-wave me-1"></i> العلاوات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'promotions.php') ? 'active' : ''; ?>" href="promotions.php">
                                <i class="fas fa-level-up-alt me-1"></i> الترقيات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'appreciation.php') ? 'active' : ''; ?>" href="appreciation.php">
                                <i class="fas fa-certificate me-1"></i> كتب الشكر
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'reports.php') ? 'active' : ''; ?>" href="reports.php">
                                <i class="fas fa-chart-bar me-1"></i> التقارير
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'analytics.php') ? 'active' : ''; ?>" href="analytics.php">
                                <i class="fas fa-chart-line me-1"></i> التحليلات
                            </a>
                        </li>

                        <?php if (hasRole(['admin', 'hr'])): ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo ($current_page == 'reminders.php') ? 'active' : ''; ?>" href="reminders.php">
                                    <i class="fas fa-bell me-1"></i> التذكيرات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo ($current_page == 'alerts.php') ? 'active' : ''; ?>" href="alerts.php">
                                    <i class="fas fa-exclamation-circle me-1"></i> التنبيهات
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (hasRole(['admin'])): ?>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-cog me-1"></i> الإدارة
                                </a>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="users.php">
                                            <i class="fas fa-user-cog me-1"></i> إدارة المستخدمين
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="departments.php">
                                            <i class="fas fa-building me-1"></i> إدارة الأقسام
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="stages.php">
                                            <i class="fas fa-money-bill me-1"></i> جدول الرواتب الاسمية
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="job_titles.php">
                                            <i class="fas fa-id-card-alt me-1"></i> إدارة العناوين الوظيفية
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="system_logs.php">
                                            <i class="fas fa-history me-1"></i> سجلات النظام
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="generate_notifications.php">
                                            <i class="fas fa-bell me-1"></i> توليد التنبيهات
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="update_database.php">
                                            <i class="fas fa-database me-1"></i> تحديث قاعدة البيانات
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="system_settings.php">
                                            <i class="fas fa-sliders-h me-1"></i> إعدادات النظام
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="php_backup.php">
                                            <i class="fas fa-database me-1"></i> النسخ الاحتياطي
                                        </a>
                                    </li>

                                </ul>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell me-1"></i>
                                <span class="badge bg-danger notification-counter" id="notificationCount" style="display: none;">0</span>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end p-0" id="notificationsContainer" style="width: 320px; max-height: 400px; overflow-y: auto;">
                                <div class="text-center p-3 text-muted">
                                    <i class="fas fa-spinner fa-spin me-2"></i> جاري تحميل الإشعارات...
                                </div>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="profile.php">
                                        <i class="fas fa-id-card me-1"></i> الملف الشخصي
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="notifications.php">
                                        <i class="fas fa-bell me-1"></i> الإشعارات
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="logout.php">
                                        <i class="fas fa-sign-out-alt me-1"></i> تسجيل الخروج
                                    </a>
                                </li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'login.php') ? 'active' : ''; ?>" href="login.php">
                                <i class="fas fa-sign-in-alt me-1"></i> تسجيل الدخول
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content Container -->
    <div class="container mt-4">
        <?php flash('success_message'); ?>
        <?php flash('error_message'); ?>
        <?php flash('info_message'); ?>
        <?php flash('warning_message'); ?>
