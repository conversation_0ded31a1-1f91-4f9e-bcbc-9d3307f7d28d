-- إن<PERSON><PERSON><PERSON> جدول الإشعارات
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` enum('allowance','promotion','appreciation','employee','system') NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `link` varchar(255) DEFAULT NULL,
  `read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إن<PERSON><PERSON><PERSON> جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدخال إعدادات النظام الافتراضية
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('allowances_per_grade', '11', 'عدد العلاوات في كل درجة'),
('promotion_years_lower_grades', '4', 'عدد سنوات الترفيع للدرجات الدنيا (10-5)'),
('promotion_years_upper_grades', '5', 'عدد سنوات الترفيع للدرجات العليا (5-1)'),
('regular_letter_months', '1', 'عدد أشهر تخفيض كتاب الشكر العادي'),
('pm_letter_months', '6', 'عدد أشهر تخفيض كتاب شكر رئيس الوزراء'),
('enable_notifications', '1', 'تفعيل نظام الإشعارات'),
('enable_reminders', '1', 'تفعيل نظام التذكيرات'),
('reminder_days', '30', 'عدد أيام التذكير المسبق');
