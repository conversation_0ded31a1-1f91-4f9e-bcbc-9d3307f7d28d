<?php
/**
 * Create Job Titles Table
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء جدول العناوين الوظيفية</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        .container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        h3 {
            color: #0d6efd;
            margin-bottom: 20px;
        }
        .alert {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">
            <i class="fas fa-database me-2"></i>
            إنشاء جدول العناوين الوظيفية
        </h1>

        <div class="progress mb-4">
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" id="progressBar"></div>
        </div>

        <div id="statusMessages">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                جاري التحقق من وجود الجدول...
            </div>
        </div>

<?php

// Include database configuration
require_once 'config/config.php';
require_once 'config/database.php';

// Make sure we're using UTF-8
$pdo->exec("SET NAMES utf8mb4");
$pdo->exec("SET CHARACTER SET utf8mb4");
$pdo->exec("SET COLLATION_CONNECTION = utf8mb4_unicode_ci");

// Check if table already exists
try {
    $checkTableStmt = $pdo->query("SHOW TABLES LIKE 'job_titles'");
    if ($checkTableStmt->rowCount() > 0) {
        echo '<div class="alert alert-warning">';
        echo '<i class="fas fa-exclamation-triangle me-2"></i>';
        echo '<strong>جدول العناوين الوظيفية موجود بالفعل</strong>';
        echo '<p class="mb-0">إذا كنت ترغب في إعادة إنشاء الجدول، قم بحذفه أولاً من قاعدة البيانات.</p>';
        echo '</div>';
        echo '<div class="text-center mt-4">';
        echo '<a href="job_titles.php" class="btn btn-primary"><i class="fas fa-arrow-right me-2"></i> العودة إلى صفحة إدارة العناوين الوظيفية</a>';
        echo '</div>';

        // Update progress bar
        echo '<script>document.getElementById("progressBar").style.width = "100%";</script>';
        echo '<script>document.getElementById("statusMessages").innerHTML = "";</script>';
        exit;
    }
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">';
    echo '<i class="fas fa-exclamation-circle me-2"></i>';
    echo '<strong>خطأ في التحقق من وجود الجدول</strong>';
    echo '<p class="mb-0">' . $e->getMessage() . '</p>';
    echo '</div>';

    // Update progress bar
    echo '<script>document.getElementById("progressBar").style.width = "100%";</script>';
    echo '<script>document.getElementById("progressBar").classList.add("bg-danger");</script>';
    exit;
}

// Create job_titles table
try {
    $createTableSQL = "
    CREATE TABLE `job_titles` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `grade_id` int(11) NOT NULL,
      `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
      `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `grade_id` (`grade_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($createTableSQL);

    // Update progress bar
    echo '<script>document.getElementById("progressBar").style.width = "50%";</script>';
    echo '<script>document.getElementById("statusMessages").innerHTML = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            <strong>تم إنشاء جدول العناوين الوظيفية بنجاح</strong>
        </div>
        <div class="alert alert-info">
            <i class="fas fa-spinner fa-spin me-2"></i>
            جاري إدخال البيانات الافتراضية...
        </div>
    `;</script>';
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">';
    echo '<i class="fas fa-exclamation-circle me-2"></i>';
    echo '<strong>خطأ في إنشاء الجدول</strong>';
    echo '<p class="mb-0">' . $e->getMessage() . '</p>';
    echo '</div>';

    // Update progress bar
    echo '<script>document.getElementById("progressBar").style.width = "100%";</script>';
    echo '<script>document.getElementById("progressBar").classList.add("bg-danger");</script>';
    exit;
}

// Insert default job titles
try {
    // Define job titles data
    $jobTitlesData = [
        // الدرجة 1 (الأعلى)
        ['grade_id' => 1, 'title' => 'مدير عام', 'description' => 'المدير العام للمؤسسة أو الدائرة'],
        ['grade_id' => 1, 'title' => 'خبير', 'description' => 'خبير متخصص في مجال معين'],
        ['grade_id' => 1, 'title' => 'مستشار', 'description' => 'مستشار في مجال التخصص'],

        // الدرجة 2
        ['grade_id' => 2, 'title' => 'مدير', 'description' => 'مدير قسم أو إدارة'],
        ['grade_id' => 2, 'title' => 'خبير أول', 'description' => 'خبير أول في مجال التخصص'],
        ['grade_id' => 2, 'title' => 'مستشار مساعد', 'description' => 'مستشار مساعد في مجال التخصص'],

        // الدرجة 3
        ['grade_id' => 3, 'title' => 'رئيس قسم', 'description' => 'رئيس قسم في الإدارة'],
        ['grade_id' => 3, 'title' => 'مهندس أقدم', 'description' => 'مهندس بخبرة عالية'],
        ['grade_id' => 3, 'title' => 'محاسب أقدم', 'description' => 'محاسب بخبرة عالية'],
        ['grade_id' => 3, 'title' => 'مبرمج أقدم', 'description' => 'مبرمج بخبرة عالية'],

        // الدرجة 4
        ['grade_id' => 4, 'title' => 'رئيس شعبة', 'description' => 'رئيس شعبة في القسم'],
        ['grade_id' => 4, 'title' => 'مهندس أول', 'description' => 'مهندس بخبرة متوسطة'],
        ['grade_id' => 4, 'title' => 'محاسب أول', 'description' => 'محاسب بخبرة متوسطة'],
        ['grade_id' => 4, 'title' => 'مبرمج أول', 'description' => 'مبرمج بخبرة متوسطة'],

        // الدرجة 5
        ['grade_id' => 5, 'title' => 'مسؤول وحدة', 'description' => 'مسؤول وحدة في الشعبة'],
        ['grade_id' => 5, 'title' => 'مهندس', 'description' => 'مهندس'],
        ['grade_id' => 5, 'title' => 'محاسب', 'description' => 'محاسب'],
        ['grade_id' => 5, 'title' => 'مبرمج', 'description' => 'مبرمج'],
        ['grade_id' => 5, 'title' => 'إداري أول', 'description' => 'إداري بخبرة متوسطة'],

        // الدرجة 6
        ['grade_id' => 6, 'title' => 'مهندس مساعد', 'description' => 'مهندس حديث التخرج'],
        ['grade_id' => 6, 'title' => 'محاسب مساعد', 'description' => 'محاسب حديث التخرج'],
        ['grade_id' => 6, 'title' => 'مبرمج مساعد', 'description' => 'مبرمج حديث التخرج'],
        ['grade_id' => 6, 'title' => 'إداري', 'description' => 'موظف إداري'],

        // الدرجة 7
        ['grade_id' => 7, 'title' => 'فني أول', 'description' => 'فني بخبرة متوسطة'],
        ['grade_id' => 7, 'title' => 'كاتب أول', 'description' => 'كاتب بخبرة متوسطة'],
        ['grade_id' => 7, 'title' => 'سكرتير أول', 'description' => 'سكرتير بخبرة متوسطة'],

        // الدرجة 8
        ['grade_id' => 8, 'title' => 'فني', 'description' => 'فني'],
        ['grade_id' => 8, 'title' => 'كاتب', 'description' => 'كاتب'],
        ['grade_id' => 8, 'title' => 'سكرتير', 'description' => 'سكرتير'],

        // الدرجة 9
        ['grade_id' => 9, 'title' => 'فني مساعد', 'description' => 'فني مساعد'],
        ['grade_id' => 9, 'title' => 'كاتب مساعد', 'description' => 'كاتب مساعد'],
        ['grade_id' => 9, 'title' => 'مساعد إداري', 'description' => 'مساعد إداري'],

        // الدرجة 10 (الأدنى)
        ['grade_id' => 10, 'title' => 'موظف خدمة', 'description' => 'موظف خدمة'],
        ['grade_id' => 10, 'title' => 'حارس', 'description' => 'حارس'],
        ['grade_id' => 10, 'title' => 'سائق', 'description' => 'سائق'],
        ['grade_id' => 10, 'title' => 'مراسل', 'description' => 'مراسل']
    ];

    // Prepare insert statement
    $stmt = $pdo->prepare("
        INSERT INTO `job_titles` (`grade_id`, `title`, `description`)
        VALUES (:grade_id, :title, :description)
    ");

    // Insert each job title
    $insertedCount = 0;
    foreach ($jobTitlesData as $jobTitle) {
        $stmt->execute([
            ':grade_id' => $jobTitle['grade_id'],
            ':title' => $jobTitle['title'],
            ':description' => $jobTitle['description']
        ]);
        $insertedCount++;
    }

    // Update progress bar and show success message
    echo '<script>document.getElementById("progressBar").style.width = "100%";</script>';
    echo '<script>document.getElementById("statusMessages").innerHTML = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            <strong>تمت العملية بنجاح!</strong>
            <p class="mb-0">تم إنشاء جدول العناوين الوظيفية وإدخال ' . $insertedCount . ' عنوان وظيفي بنجاح.</p>
        </div>
    `;</script>';

    echo '<div class="text-center mt-4">';
    echo '<a href="job_titles.php" class="btn btn-primary btn-lg"><i class="fas fa-arrow-right me-2"></i> الذهاب إلى صفحة إدارة العناوين الوظيفية</a>';
    echo '</div>';
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">';
    echo '<i class="fas fa-exclamation-circle me-2"></i>';
    echo '<strong>خطأ في إدخال البيانات</strong>';
    echo '<p class="mb-0">' . $e->getMessage() . '</p>';
    echo '</div>';

    // Update progress bar
    echo '<script>document.getElementById("progressBar").style.width = "100%";</script>';
    echo '<script>document.getElementById("progressBar").classList.add("bg-danger");</script>';

    echo '<div class="text-center mt-4">';
    echo '<a href="job_titles.php" class="btn btn-primary"><i class="fas fa-arrow-right me-2"></i> العودة إلى صفحة إدارة العناوين الوظيفية</a>';
    echo '</div>';
}
?>

    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
