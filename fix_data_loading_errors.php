<?php
/**
 * Data Loading Errors Fix Script
 * إصلاح أخطاء تحميل البيانات
 */

// Include configuration
require_once 'includes/header.php';
requireLogin();

// Only admin can run this script
if (!hasRole(['admin'])) {
    die('ليس لديك صلاحية لتشغيل هذا السكريپت');
}

echo "<h1>فحص وإصلاح أخطاء تحميل البيانات</h1>";

// Check database connection
echo "<h2>فحص الاتصال بقاعدة البيانات:</h2>";
try {
    $stmt = $pdo->query("SELECT 1");
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح</p>";
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit();
}

// Check required constants
echo "<h2>فحص الثوابت المطلوبة:</h2>";
$requiredConstants = [
    'ALLOWANCES_PER_GRADE' => 11,
    'PROMOTION_YEARS_LOWER_GRADES' => 4,
    'PROMOTION_YEARS_UPPER_GRADES' => 5,
    'REGULAR_LETTER_MONTHS' => 1,
    'PM_LETTER_MONTHS' => 6,
    'RETIREMENT_AGE' => 60
];

foreach ($requiredConstants as $constant => $defaultValue) {
    if (defined($constant)) {
        echo "<p style='color: green;'>✅ $constant = " . constant($constant) . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ $constant غير معرف، سيتم استخدام القيمة الافتراضية: $defaultValue</p>";
        define($constant, $defaultValue);
    }
}

// Check required tables
echo "<h2>فحص الجداول المطلوبة:</h2>";
$requiredTables = [
    'users' => 'جدول المستخدمين',
    'employees' => 'جدول الموظفين',
    'departments' => 'جدول الأقسام',
    'education_levels' => 'جدول المستويات التعليمية',
    'job_titles' => 'جدول العناوين الوظيفية',
    'stages' => 'جدول المراحل والرواتب',
    'allowance_history' => 'جدول تاريخ العلاوات',
    'promotion_history' => 'جدول تاريخ الترفيعات',
    'appreciation_letters' => 'جدول كتب الشكر',
    'notifications' => 'جدول التنبيهات',
    'reminders' => 'جدول التذكيرات',
    'system_settings' => 'جدول إعدادات النظام'
];

$missingTables = [];
$existingTables = [];

foreach ($requiredTables as $table => $description) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✅ $description ($table)</p>";
            $existingTables[] = $table;
        } else {
            echo "<p style='color: red;'>❌ $description ($table) - غير موجود</p>";
            $missingTables[] = $table;
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص $description: " . $e->getMessage() . "</p>";
        $missingTables[] = $table;
    }
}

// Test data loading modules
echo "<h2>اختبار وحدات تحميل البيانات:</h2>";

// Test reports module
echo "<h3>وحدة التقارير:</h3>";
try {
    $reportType = 'employees_summary';
    $department = 0;
    $year = date('Y');
    $month = date('m');
    $period = 30;
    $grade = 0;
    
    ob_start();
    include 'modules/reports/reports_data.php';
    $output = ob_get_clean();
    
    if (isset($reportData)) {
        echo "<p style='color: green;'>✅ وحدة التقارير تعمل بشكل صحيح</p>";
        echo "<p>عدد السجلات: " . count($reportData) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ وحدة التقارير لا تعمل بشكل صحيح</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في وحدة التقارير: " . $e->getMessage() . "</p>";
}

// Test analytics module
echo "<h3>وحدة التحليلات:</h3>";
try {
    ob_start();
    include 'modules/analytics/analytics_data.php';
    $output = ob_get_clean();
    
    if (isset($totalEmployees)) {
        echo "<p style='color: green;'>✅ وحدة التحليلات تعمل بشكل صحيح</p>";
        echo "<p>إجمالي الموظفين: $totalEmployees</p>";
        echo "<p>استخدام بيانات تجريبية: " . (isset($usingDummyData) && $usingDummyData ? 'نعم' : 'لا') . "</p>";
    } else {
        echo "<p style='color: red;'>❌ وحدة التحليلات لا تعمل بشكل صحيح</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في وحدة التحليلات: " . $e->getMessage() . "</p>";
}

// Test notifications module
echo "<h3>وحدة التنبيهات:</h3>";
try {
    ob_start();
    include 'modules/notifications/notifications_data.php';
    $output = ob_get_clean();
    
    if (isset($notifications)) {
        echo "<p style='color: green;'>✅ وحدة التنبيهات تعمل بشكل صحيح</p>";
        echo "<p>عدد التنبيهات: " . count($notifications) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ وحدة التنبيهات لا تعمل بشكل صحيح</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في وحدة التنبيهات: " . $e->getMessage() . "</p>";
}

// Test reminders module
echo "<h3>وحدة التذكيرات:</h3>";
try {
    ob_start();
    include 'modules/reminders/reminders_data.php';
    $output = ob_get_clean();
    
    if (isset($reminders)) {
        echo "<p style='color: green;'>✅ وحدة التذكيرات تعمل بشكل صحيح</p>";
        echo "<p>عدد التذكيرات: " . count($reminders) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ وحدة التذكيرات لا تعمل بشكل صحيح</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في وحدة التذكيرات: " . $e->getMessage() . "</p>";
}

// Recommendations
echo "<h2>التوصيات والحلول:</h2>";

if (count($missingTables) > 0) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0;'>";
    echo "<h3>⚠️ جداول مفقودة</h3>";
    echo "<p>الجداول التالية مفقودة من قاعدة البيانات:</p>";
    echo "<ul>";
    foreach ($missingTables as $table) {
        echo "<li>$table - " . $requiredTables[$table] . "</li>";
    }
    echo "</ul>";
    echo "<p><strong>الحل:</strong> تشغيل سكريپت إنشاء قاعدة البيانات:</p>";
    echo "<ol>";
    echo "<li>اذهب إلى <a href='sql/'>مجلد sql/</a></li>";
    echo "<li>استخدم ملف <code>new_complete_database.sql</code> أو <code>complete_database.sql</code></li>";
    echo "<li>أو استخدم <a href='update_database.php'>صفحة تحديث قاعدة البيانات</a></li>";
    echo "</ol>";
    echo "</div>";
}

if (count($existingTables) > 0) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0;'>";
    echo "<h3>✅ جداول موجودة</h3>";
    echo "<p>الجداول التالية موجودة وتعمل بشكل صحيح:</p>";
    echo "<ul>";
    foreach ($existingTables as $table) {
        echo "<li>$table - " . $requiredTables[$table] . "</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// Performance tips
echo "<div style='background: #d1ecf1; color: #0c5460; padding: 15px; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0;'>";
echo "<h3>💡 نصائح لتحسين الأداء</h3>";
echo "<ul>";
echo "<li>تأكد من وجود فهارس على الجداول الكبيرة</li>";
echo "<li>قم بتنظيف البيانات القديمة بانتظام</li>";
echo "<li>استخدم النسخ الاحتياطي بانتظام</li>";
echo "<li>راقب حجم قاعدة البيانات</li>";
echo "</ul>";
echo "</div>";

// Quick fixes
echo "<h2>إصلاحات سريعة:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff;'>";
echo "<h3>إنشاء الجداول الأساسية:</h3>";

if (in_array('system_settings', $missingTables)) {
    echo "<p><button onclick='createSystemSettings()' class='btn btn-primary'>إنشاء جدول إعدادات النظام</button></p>";
}

if (in_array('notifications', $missingTables)) {
    echo "<p><button onclick='createNotifications()' class='btn btn-primary'>إنشاء جدول التنبيهات</button></p>";
}

if (in_array('reminders', $missingTables)) {
    echo "<p><button onclick='createReminders()' class='btn btn-primary'>إنشاء جدول التذكيرات</button></p>";
}

echo "</div>";

echo "<h2>الخلاصة:</h2>";
if (count($missingTables) == 0) {
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border: 1px solid #c3e6cb; border-radius: 4px; text-align: center;'>";
    echo "<h3>🎉 النظام سليم!</h3>";
    echo "<p>جميع الجداول موجودة ووحدات تحميل البيانات تعمل بشكل صحيح.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border: 1px solid #f5c6cb; border-radius: 4px; text-align: center;'>";
    echo "<h3>⚠️ يتطلب إصلاح</h3>";
    echo "<p>يوجد " . count($missingTables) . " جدول مفقود. يرجى اتباع التوصيات أعلاه.</p>";
    echo "</div>";
}

echo "<p style='text-align: center; margin-top: 30px;'>";
echo "<a href='reports_analytics_unified.php' class='btn btn-primary'>اختبار الصفحة الموحدة</a> ";
echo "<a href='update_database.php' class='btn btn-secondary'>تحديث قاعدة البيانات</a> ";
echo "<a href='dashboard_unified.php' class='btn btn-success'>العودة إلى لوحة التحكم</a>";
echo "</p>";

?>

<script>
function createSystemSettings() {
    if (confirm('هل أنت متأكد من إنشاء جدول إعدادات النظام؟')) {
        // This would need to be implemented with AJAX
        alert('يرجى استخدام صفحة تحديث قاعدة البيانات لإنشاء الجداول');
    }
}

function createNotifications() {
    if (confirm('هل أنت متأكد من إنشاء جدول التنبيهات؟')) {
        alert('يرجى استخدام صفحة تحديث قاعدة البيانات لإنشاء الجداول');
    }
}

function createReminders() {
    if (confirm('هل أنت متأكد من إنشاء جدول التذكيرات؟')) {
        alert('يرجى استخدام صفحة تحديث قاعدة البيانات لإنشاء الجداول');
    }
}
</script>
