<!-- Reports Content -->
<div class="p-4">
    <!-- Report Type Selection -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-modern rounded-modern">
                <div class="card-header bg-gradient-success">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-file-alt me-2"></i>اختيار نوع التقرير
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="get" class="row g-3">
                        <input type="hidden" name="tab" value="reports">
                        
                        <div class="col-md-4">
                            <label for="reportType" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="reportType" name="type" required>
                                <option value="">اختر نوع التقرير</option>
                                <optgroup label="تقارير العلاوات">
                                    <option value="allowances_eligible" <?php echo ($reportType === 'allowances_eligible') ? 'selected' : ''; ?>>
                                        الموظفين المستحقين للعلاوة حالياً
                                    </option>
                                    <option value="upcoming_allowances" <?php echo ($reportType === 'upcoming_allowances') ? 'selected' : ''; ?>>
                                        العلاوات المستحقة خلال فترة محددة
                                    </option>
                                    <option value="allowances_monthly" <?php echo ($reportType === 'allowances_monthly') ? 'selected' : ''; ?>>
                                        العلاوات الشهرية
                                    </option>
                                </optgroup>
                                <optgroup label="تقارير الترفيعات">
                                    <option value="promotions_eligible" <?php echo ($reportType === 'promotions_eligible') ? 'selected' : ''; ?>>
                                        الموظفين المستحقين للترفيع حالياً
                                    </option>
                                    <option value="promotions_monthly" <?php echo ($reportType === 'promotions_monthly') ? 'selected' : ''; ?>>
                                        الترفيعات الشهرية
                                    </option>
                                </optgroup>
                                <optgroup label="تقارير كتب الشكر">
                                    <option value="appreciation_yearly" <?php echo ($reportType === 'appreciation_yearly') ? 'selected' : ''; ?>>
                                        كتب الشكر السنوية
                                    </option>
                                </optgroup>
                                <optgroup label="تقارير عامة">
                                    <option value="employees_summary" <?php echo ($reportType === 'employees_summary') ? 'selected' : ''; ?>>
                                        ملخص بيانات الموظفين
                                    </option>
                                    <option value="department_summary" <?php echo ($reportType === 'department_summary') ? 'selected' : ''; ?>>
                                        ملخص الأقسام
                                    </option>
                                    <option value="upcoming_retirement" <?php echo ($reportType === 'upcoming_retirement') ? 'selected' : ''; ?>>
                                        الموظفين المقبلين على التقاعد
                                    </option>
                                </optgroup>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="reportDepartment" class="form-label">القسم</label>
                            <select class="form-select" id="reportDepartment" name="department">
                                <option value="0">جميع الأقسام</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['id']; ?>" <?php echo ($department == $dept['id']) ? 'selected' : ''; ?>>
                                        <?php echo $dept['name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="reportYear" class="form-label">السنة</label>
                            <select class="form-select" id="reportYear" name="year">
                                <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                                    <option value="<?php echo $y; ?>" <?php echo ($year == $y) ? 'selected' : ''; ?>>
                                        <?php echo $y; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="reportMonth" class="form-label">الشهر</label>
                            <select class="form-select" id="reportMonth" name="month">
                                <?php 
                                $months = [
                                    1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
                                    5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
                                    9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
                                ];
                                foreach ($months as $num => $name): ?>
                                    <option value="<?php echo $num; ?>" <?php echo ($month == $num) ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>إنشاء التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Results -->
    <?php if (!empty($reportType) && !empty($reportTitle)): ?>
        <div class="card shadow-modern rounded-modern">
            <div class="card-header bg-gradient-primary">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0 text-white">
                            <i class="fas fa-chart-bar me-2"></i><?php echo $reportTitle; ?>
                        </h5>
                    </div>
                    <div class="col-auto">
                        <div class="btn-group">
                            <button class="btn btn-light btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                            <button class="btn btn-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-1"></i>تصدير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if (count($reportData) > 0): ?>
                    <!-- Report Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stat-card bg-primary">
                                <div class="stat-icon">
                                    <i class="fas fa-list"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number"><?php echo count($reportData); ?></div>
                                    <div class="stat-label">إجمالي السجلات</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card bg-success">
                                <div class="stat-icon">
                                    <i class="fas fa-calendar"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number"><?php echo date('Y-m-d'); ?></div>
                                    <div class="stat-label">تاريخ التقرير</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card bg-info">
                                <div class="stat-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number">
                                        <?php 
                                        if ($department > 0) {
                                            $deptName = '';
                                            foreach ($departments as $dept) {
                                                if ($dept['id'] == $department) {
                                                    $deptName = $dept['name'];
                                                    break;
                                                }
                                            }
                                            echo $deptName;
                                        } else {
                                            echo 'جميع الأقسام';
                                        }
                                        ?>
                                    </div>
                                    <div class="stat-label">القسم</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card bg-warning">
                                <div class="stat-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number"><?php echo $_SESSION['full_name']; ?></div>
                                    <div class="stat-label">المستخدم</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Report Table -->
                    <div class="table-responsive">
                        <table class="table table-hover table-striped" id="reportTable">
                            <thead class="table-dark">
                                <tr>
                                    <?php
                                    // Dynamic table headers based on report type
                                    switch ($reportType) {
                                        case 'allowances_eligible':
                                        case 'upcoming_allowances':
                                            echo '<th>الرقم الوظيفي</th>';
                                            echo '<th>الاسم</th>';
                                            echo '<th>الدرجة</th>';
                                            echo '<th>القسم</th>';
                                            echo '<th>العلاوات المستلمة</th>';
                                            echo '<th>تاريخ العلاوة القادمة</th>';
                                            echo '<th>الحالة</th>';
                                            break;
                                        case 'promotions_eligible':
                                            echo '<th>الرقم الوظيفي</th>';
                                            echo '<th>الاسم</th>';
                                            echo '<th>الدرجة الحالية</th>';
                                            echo '<th>القسم</th>';
                                            echo '<th>تاريخ الترفيع القادم</th>';
                                            echo '<th>الحالة</th>';
                                            break;
                                        case 'upcoming_retirement':
                                            echo '<th>الرقم الوظيفي</th>';
                                            echo '<th>الاسم</th>';
                                            echo '<th>العمر الحالي</th>';
                                            echo '<th>القسم</th>';
                                            echo '<th>تاريخ التقاعد</th>';
                                            echo '<th>الأيام المتبقية</th>';
                                            break;
                                        case 'allowances_monthly':
                                            echo '<th>الرقم الوظيفي</th>';
                                            echo '<th>الاسم</th>';
                                            echo '<th>القسم</th>';
                                            echo '<th>تاريخ العلاوة</th>';
                                            echo '<th>المنشئ</th>';
                                            break;
                                        case 'promotions_monthly':
                                            echo '<th>الرقم الوظيفي</th>';
                                            echo '<th>الاسم</th>';
                                            echo '<th>القسم</th>';
                                            echo '<th>من درجة</th>';
                                            echo '<th>إلى درجة</th>';
                                            echo '<th>تاريخ الترفيع</th>';
                                            echo '<th>المنشئ</th>';
                                            break;
                                        case 'appreciation_yearly':
                                            echo '<th>الرقم الوظيفي</th>';
                                            echo '<th>الاسم</th>';
                                            echo '<th>القسم</th>';
                                            echo '<th>رقم الكتاب</th>';
                                            echo '<th>تاريخ الإصدار</th>';
                                            echo '<th>المصدر</th>';
                                            break;
                                        case 'employees_summary':
                                            echo '<th>الرقم الوظيفي</th>';
                                            echo '<th>الاسم</th>';
                                            echo '<th>الدرجة</th>';
                                            echo '<th>القسم</th>';
                                            echo '<th>سنوات الخدمة</th>';
                                            echo '<th>تاريخ التعيين</th>';
                                            break;
                                        case 'department_summary':
                                            echo '<th>اسم القسم</th>';
                                            echo '<th>إجمالي الموظفين</th>';
                                            echo '<th>مستحقي العلاوة</th>';
                                            echo '<th>مستحقي الترفيع</th>';
                                            echo '<th>متوسط سنوات الخدمة</th>';
                                            echo '<th>كتب الشكر</th>';
                                            break;
                                    }
                                    ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reportData as $row): ?>
                                    <tr>
                                        <?php
                                        // Dynamic table data based on report type
                                        switch ($reportType) {
                                            case 'allowances_eligible':
                                            case 'upcoming_allowances':
                                                echo '<td>' . $row['employee_number'] . '</td>';
                                                echo '<td>' . $row['full_name'] . '</td>';
                                                echo '<td>' . $row['current_grade'] . '</td>';
                                                echo '<td>' . $row['department_name'] . '</td>';
                                                echo '<td>' . $row['allowances_in_current_grade'] . ' من ' . ALLOWANCES_PER_GRADE . '</td>';
                                                echo '<td>' . formatArabicDate($row['next_allowance_date']) . '</td>';
                                                $isEligible = strtotime($row['next_allowance_date']) <= time();
                                                echo '<td><span class="badge bg-' . ($isEligible ? 'danger' : 'warning') . '">' . ($isEligible ? 'مستحق حالياً' : 'قريباً') . '</span></td>';
                                                break;
                                            case 'promotions_eligible':
                                                echo '<td>' . $row['employee_number'] . '</td>';
                                                echo '<td>' . $row['full_name'] . '</td>';
                                                echo '<td>' . $row['current_grade'] . '</td>';
                                                echo '<td>' . $row['department_name'] . '</td>';
                                                echo '<td>' . formatArabicDate($row['next_promotion_date']) . '</td>';
                                                $isEligible = strtotime($row['next_promotion_date']) <= time();
                                                echo '<td><span class="badge bg-' . ($isEligible ? 'danger' : 'warning') . '">' . ($isEligible ? 'مستحق حالياً' : 'قريباً') . '</span></td>';
                                                break;
                                            case 'upcoming_retirement':
                                                echo '<td>' . $row['employee_number'] . '</td>';
                                                echo '<td>' . $row['full_name'] . '</td>';
                                                echo '<td>' . $row['current_age'] . ' سنة</td>';
                                                echo '<td>' . $row['department_name'] . '</td>';
                                                echo '<td>' . formatArabicDate($row['retirement_date']) . '</td>';
                                                $daysToRetirement = (strtotime($row['retirement_date']) - time()) / (60 * 60 * 24);
                                                echo '<td>' . round($daysToRetirement) . ' يوم</td>';
                                                break;
                                            case 'employees_summary':
                                                echo '<td>' . $row['employee_number'] . '</td>';
                                                echo '<td>' . $row['full_name'] . '</td>';
                                                echo '<td>' . $row['current_grade'] . '</td>';
                                                echo '<td>' . $row['department_name'] . '</td>';
                                                echo '<td>' . ($row['years_of_service'] ?? 0) . ' سنة</td>';
                                                echo '<td>' . formatArabicDate($row['hire_date'] ?? date('Y-m-d')) . '</td>';
                                                break;
                                            case 'department_summary':
                                                echo '<td>' . $row['name'] . '</td>';
                                                echo '<td>' . $row['total_employees'] . '</td>';
                                                echo '<td>' . $row['eligible_allowances'] . '</td>';
                                                echo '<td>' . $row['eligible_promotions'] . '</td>';
                                                echo '<td>' . round($row['avg_years_service'], 1) . ' سنة</td>';
                                                echo '<td>' . $row['total_appreciation'] . '</td>';
                                                break;
                                        }
                                        ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <h5>لا توجد بيانات</h5>
                        <p class="mb-0">لم يتم العثور على بيانات للتقرير المحدد. يرجى تجربة معايير أخرى.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-file-alt fa-4x text-muted mb-3"></i>
            <h4 class="text-muted">اختر نوع التقرير</h4>
            <p class="text-muted">يرجى اختيار نوع التقرير من القائمة أعلاه لعرض البيانات</p>
        </div>
    <?php endif; ?>
</div>

<script>
// Export to Excel functionality
function exportToExcel() {
    const table = document.getElementById('reportTable');
    if (!table) return;
    
    const wb = XLSX.utils.table_to_book(table, {sheet: "التقرير"});
    const filename = '<?php echo $reportTitle; ?>_' + new Date().toISOString().split('T')[0] + '.xlsx';
    XLSX.writeFile(wb, filename);
}
</script>
