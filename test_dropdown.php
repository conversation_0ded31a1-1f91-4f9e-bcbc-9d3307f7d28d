<?php
/**
 * Test Dropdown Functionality
 * صفحة اختبار وظائف القوائم المنسدلة
 */

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">اختبار وظائف القوائم المنسدلة</h1>
            
            <!-- Test Admin Dropdown -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>اختبار قائمة الإدارة</h5>
                </div>
                <div class="card-body">
                    <p>حالة المستخدم الحالي:</p>
                    <ul>
                        <li><strong>اسم المستخدم:</strong> <?php echo $_SESSION['username'] ?? 'غير محدد'; ?></li>
                        <li><strong>الاسم الكامل:</strong> <?php echo $_SESSION['full_name'] ?? 'غير محدد'; ?></li>
                        <li><strong>الدور:</strong> <?php echo $_SESSION['role'] ?? 'غير محدد'; ?></li>
                        <li><strong>هل هو مدير؟</strong> <?php echo hasRole(['admin']) ? 'نعم' : 'لا'; ?></li>
                        <li><strong>هل له صلاحيات HR؟</strong> <?php echo hasRole(['admin', 'hr']) ? 'نعم' : 'لا'; ?></li>
                    </ul>
                    
                    <?php if (hasRole(['admin'])): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            المستخدم لديه صلاحيات الإدارة - يجب أن تظهر قائمة الإدارة
                        </div>
                        
                        <!-- Test Dropdown -->
                        <div class="dropdown">
                            <button class="btn btn-primary dropdown-toggle" type="button" id="testAdminDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-cog me-2"></i>قائمة الإدارة التجريبية
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="users.php"><i class="fas fa-user-cog me-2"></i>إدارة المستخدمين</a></li>
                                <li><a class="dropdown-item" href="departments.php"><i class="fas fa-building me-2"></i>إدارة الأقسام</a></li>
                                <li><a class="dropdown-item" href="stages.php"><i class="fas fa-money-bill me-2"></i>جدول الرواتب الاسمية</a></li>
                                <li><a class="dropdown-item" href="job_titles.php"><i class="fas fa-id-card-alt me-2"></i>إدارة العناوين الوظيفية</a></li>
                                <li><a class="dropdown-item" href="system_logs.php"><i class="fas fa-history me-2"></i>سجلات النظام</a></li>
                                <li><a class="dropdown-item" href="system_settings.php"><i class="fas fa-sliders-h me-2"></i>إعدادات النظام</a></li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            المستخدم ليس لديه صلاحيات الإدارة - لن تظهر قائمة الإدارة
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Test Bootstrap -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>اختبار Bootstrap</h5>
                </div>
                <div class="card-body">
                    <p>فحص تحميل Bootstrap JavaScript:</p>
                    <div id="bootstrapStatus" class="alert alert-info">
                        <i class="fas fa-spinner fa-spin me-2"></i>جاري الفحص...
                    </div>
                    
                    <!-- Simple Bootstrap Dropdown Test -->
                    <div class="dropdown">
                        <button class="btn btn-secondary dropdown-toggle" type="button" id="simpleDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            قائمة بسيطة للاختبار
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">خيار 1</a></li>
                            <li><a class="dropdown-item" href="#">خيار 2</a></li>
                            <li><a class="dropdown-item" href="#">خيار 3</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Test CSS -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>اختبار CSS</h5>
                </div>
                <div class="card-body">
                    <p>فحص تحميل ملفات CSS:</p>
                    <div id="cssStatus" class="alert alert-info">
                        <i class="fas fa-spinner fa-spin me-2"></i>جاري الفحص...
                    </div>
                </div>
            </div>
            
            <!-- Debug Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>معلومات التشخيص</h5>
                </div>
                <div class="card-body">
                    <div id="debugInfo">
                        <p>جاري جمع معلومات التشخيص...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Test Bootstrap
    const bootstrapStatus = document.getElementById('bootstrapStatus');
    if (typeof bootstrap !== 'undefined') {
        bootstrapStatus.className = 'alert alert-success';
        bootstrapStatus.innerHTML = '<i class="fas fa-check-circle me-2"></i>Bootstrap JavaScript محمل بنجاح';
    } else {
        bootstrapStatus.className = 'alert alert-danger';
        bootstrapStatus.innerHTML = '<i class="fas fa-times-circle me-2"></i>Bootstrap JavaScript غير محمل';
    }
    
    // Test CSS
    const cssStatus = document.getElementById('cssStatus');
    const testElement = document.createElement('div');
    testElement.className = 'dropdown-menu';
    document.body.appendChild(testElement);
    const computedStyle = window.getComputedStyle(testElement);
    document.body.removeChild(testElement);
    
    if (computedStyle.position === 'absolute') {
        cssStatus.className = 'alert alert-success';
        cssStatus.innerHTML = '<i class="fas fa-check-circle me-2"></i>CSS محمل بنجاح';
    } else {
        cssStatus.className = 'alert alert-warning';
        cssStatus.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>CSS قد لا يكون محمل بشكل صحيح';
    }
    
    // Debug Info
    const debugInfo = document.getElementById('debugInfo');
    const info = {
        'Bootstrap Version': typeof bootstrap !== 'undefined' ? 'محمل' : 'غير محمل',
        'jQuery Version': typeof $ !== 'undefined' ? $.fn.jquery : 'غير محمل',
        'Dropdown Elements': document.querySelectorAll('.dropdown').length,
        'Dropdown Menus': document.querySelectorAll('.dropdown-menu').length,
        'Admin Dropdown': document.getElementById('adminDropdown') ? 'موجود' : 'غير موجود',
        'User Dropdown': document.getElementById('userDropdown') ? 'موجود' : 'غير موجود',
        'Notifications Dropdown': document.getElementById('notificationsDropdown') ? 'موجود' : 'غير موجود'
    };
    
    let debugHTML = '<table class="table table-striped"><tbody>';
    for (const [key, value] of Object.entries(info)) {
        debugHTML += `<tr><td><strong>${key}:</strong></td><td>${value}</td></tr>`;
    }
    debugHTML += '</tbody></table>';
    
    debugInfo.innerHTML = debugHTML;
    
    // Test dropdown functionality
    setTimeout(() => {
        const dropdowns = document.querySelectorAll('[data-bs-toggle="dropdown"]');
        console.log('Found dropdowns:', dropdowns.length);
        
        dropdowns.forEach((dropdown, index) => {
            console.log(`Dropdown ${index + 1}:`, dropdown.id || 'No ID', dropdown);
        });
    }, 1000);
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
