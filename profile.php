<?php
/**
 * User Profile Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();

// Get current user
$user = getCurrentUser();

// Process form submission for updating profile
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $fullName = sanitize($_POST['full_name']);
    $email = sanitize($_POST['email']);
    $currentPassword = $_POST['current_password']; // Don't sanitize password
    $newPassword = $_POST['new_password']; // Don't sanitize password
    $confirmPassword = $_POST['confirm_password']; // Don't sanitize password
    
    // Validate inputs
    $errors = [];
    
    if (empty($fullName)) {
        $errors[] = 'الاسم الكامل مطلوب';
    }
    
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صالح';
    }
    
    // Check if user wants to change password
    $changePassword = !empty($currentPassword) || !empty($newPassword) || !empty($confirmPassword);
    
    if ($changePassword) {
        if (empty($currentPassword)) {
            $errors[] = 'كلمة المرور الحالية مطلوبة';
        }
        
        if (empty($newPassword)) {
            $errors[] = 'كلمة المرور الجديدة مطلوبة';
        } elseif (strlen($newPassword) < 6) {
            $errors[] = 'كلمة المرور الجديدة يجب أن تكون على الأقل 6 أحرف';
        }
        
        if ($newPassword !== $confirmPassword) {
            $errors[] = 'كلمة المرور الجديدة وتأكيدها غير متطابقين';
        }
        
        // Verify current password
        if (!empty($currentPassword)) {
            try {
                $stmt = $pdo->prepare("SELECT password FROM users WHERE id = :id");
                $stmt->execute([':id' => $_SESSION['user_id']]);
                $storedPassword = $stmt->fetch()['password'];
                
                if (!password_verify($currentPassword, $storedPassword)) {
                    $errors[] = 'كلمة المرور الحالية غير صحيحة';
                }
            } catch (PDOException $e) {
                error_log("Verify Password Error: " . $e->getMessage());
                $errors[] = 'حدث خطأ أثناء التحقق من كلمة المرور';
            }
        }
    }
    
    // If no errors, update profile
    if (empty($errors)) {
        try {
            if ($changePassword) {
                // Update profile with new password
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("
                    UPDATE users SET
                        full_name = :full_name,
                        email = :email,
                        password = :password
                    WHERE id = :id
                ");
                
                $stmt->execute([
                    ':full_name' => $fullName,
                    ':email' => $email,
                    ':password' => $hashedPassword,
                    ':id' => $_SESSION['user_id']
                ]);
            } else {
                // Update profile without changing password
                $stmt = $pdo->prepare("
                    UPDATE users SET
                        full_name = :full_name,
                        email = :email
                    WHERE id = :id
                ");
                
                $stmt->execute([
                    ':full_name' => $fullName,
                    ':email' => $email,
                    ':id' => $_SESSION['user_id']
                ]);
            }
            
            // Update session data
            $_SESSION['full_name'] = $fullName;
            
            // Log activity
            logActivity(
                $_SESSION['user_id'],
                'تحديث الملف الشخصي',
                'users',
                $_SESSION['user_id'],
                'تم تحديث الملف الشخصي'
            );
            
            flash('success_message', 'تم تحديث الملف الشخصي بنجاح', 'alert alert-success');
            redirect('profile.php');
        } catch (PDOException $e) {
            error_log("Update Profile Error: " . $e->getMessage());
            flash('error_message', 'حدث خطأ أثناء تحديث الملف الشخصي', 'alert alert-danger');
        }
    } else {
        // Display errors
        $errorMessage = '<ul class="mb-0">';
        foreach ($errors as $error) {
            $errorMessage .= '<li>' . $error . '</li>';
        }
        $errorMessage .= '</ul>';
        
        flash('error_message', $errorMessage, 'alert alert-danger');
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-id-card me-2"></i> الملف الشخصي
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i> معلومات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="username" value="<?php echo $user['username']; ?>" readonly>
                        <div class="form-text">لا يمكن تغيير اسم المستخدم</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo $user['full_name']; ?>" required>
                        <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo $user['email']; ?>">
                        <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صالح</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="role" class="form-label">الصلاحية</label>
                        <input type="text" class="form-control" id="role" value="<?php echo ($user['role'] === 'admin') ? 'مدير النظام' : (($user['role'] === 'hr') ? 'موظف الموارد البشرية' : 'مستخدم عادي'); ?>" readonly>
                        <div class="form-text">لا يمكن تغيير الصلاحية</div>
                    </div>
                    
                    <hr>
                    
                    <h5 class="mb-3">تغيير كلمة المرور</h5>
                    
                    <div class="mb-3">
                        <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                        <input type="password" class="form-control" id="current_password" name="current_password">
                        <div class="form-text">أدخل كلمة المرور الحالية فقط إذا كنت ترغب في تغييرها</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="new_password" name="new_password">
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                    </div>
                    
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary px-5">
                            <i class="fas fa-save me-1"></i> حفظ التغييرات
                        </button>
                        <a href="index.php" class="btn btn-secondary px-5">
                            <i class="fas fa-times me-1"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
