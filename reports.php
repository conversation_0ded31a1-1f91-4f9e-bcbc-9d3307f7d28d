<?php
/**
 * Reports Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();

// Get report type
$reportType = isset($_GET['type']) ? sanitize($_GET['type']) : '';
$department = isset($_GET['department']) ? (int)$_GET['department'] : 0;
$year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');
$month = isset($_GET['month']) ? (int)$_GET['month'] : date('m');

// Get departments for filter
try {
    $deptStmt = $pdo->query("SELECT id, name FROM departments ORDER BY name ASC");
    $departments = $deptStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Departments Error: " . $e->getMessage());
    $departments = [];
}

// Initialize report data
$reportData = [];
$reportTitle = '';

// Get additional parameters
$period = isset($_GET['period']) ? (int)$_GET['period'] : 30;
$grade = isset($_GET['grade']) ? (int)$_GET['grade'] : 0;
$education = isset($_GET['education']) ? (int)$_GET['education'] : 0;

// Generate report based on type
if (!empty($reportType)) {
    try {
        switch ($reportType) {
            case 'allowances_eligible':
                $reportTitle = 'تقرير الموظفين المستحقين للعلاوة';

                $query = "
                    SELECT e.*, d.name as department_name
                    FROM employees e
                    JOIN departments d ON e.department_id = d.id
                    WHERE e.allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
                    AND e.next_allowance_date <= CURDATE()
                ";

                if ($department > 0) {
                    $query .= " AND e.department_id = :department";
                }

                $query .= " ORDER BY e.next_allowance_date ASC, e.full_name ASC";

                $stmt = $pdo->prepare($query);

                if ($department > 0) {
                    $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                }

                $stmt->execute();
                $reportData = $stmt->fetchAll();
                break;

            case 'upcoming_allowances':
                $reportTitle = 'تقرير الموظفين المستحقين للعلاوة خلال ' . $period . ' يوم';

                $query = "
                    SELECT e.*, d.name as department_name
                    FROM employees e
                    JOIN departments d ON e.department_id = d.id
                    WHERE e.allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
                    AND e.next_allowance_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL :period DAY)
                ";

                if ($department > 0) {
                    $query .= " AND e.department_id = :department";
                }

                if ($grade > 0) {
                    $query .= " AND e.current_grade = :grade";
                }

                $query .= " ORDER BY e.next_allowance_date ASC, e.full_name ASC";

                $stmt = $pdo->prepare($query);
                $stmt->bindParam(':period', $period, PDO::PARAM_INT);

                if ($department > 0) {
                    $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                }

                if ($grade > 0) {
                    $stmt->bindParam(':grade', $grade, PDO::PARAM_INT);
                }

                $stmt->execute();
                $reportData = $stmt->fetchAll();
                break;

            case 'upcoming_retirement':
                $reportTitle = 'تقرير الموظفين المقبلين على التقاعد خلال ' . $period . ' يوم';

                // Get retirement age from settings
                $retirementAge = 60; // Default
                $settingStmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'retirement_age'");
                $settingStmt->execute();
                $setting = $settingStmt->fetch();
                if ($setting) {
                    $retirementAge = (int)$setting['setting_value'];
                }

                // Use a variable for the retirement age in the query to avoid binding the same parameter twice
                $query = "
                    SELECT e.*, d.name as department_name,
                           TIMESTAMPDIFF(YEAR, e.birth_date, CURDATE()) as current_age,
                           DATE_ADD(e.birth_date, INTERVAL $retirementAge YEAR) as retirement_date
                    FROM employees e
                    JOIN departments d ON e.department_id = d.id
                    WHERE DATE_ADD(e.birth_date, INTERVAL $retirementAge YEAR)
                          BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL :period DAY)
                ";

                if ($department > 0) {
                    $query .= " AND e.department_id = :department";
                }

                $query .= " ORDER BY retirement_date ASC, e.full_name ASC";

                $stmt = $pdo->prepare($query);
                $stmt->bindParam(':period', $period, PDO::PARAM_INT);

                if ($department > 0) {
                    $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                }

                $stmt->execute();
                $reportData = $stmt->fetchAll();
                break;

            case 'promotions_eligible':
                $reportTitle = 'تقرير الموظفين المستحقين للترفيع';

                $query = "
                    SELECT e.*, d.name as department_name, el.name as education_level_name, el.max_grade
                    FROM employees e
                    JOIN departments d ON e.department_id = d.id
                    JOIN education_levels el ON e.education_level_id = el.id
                    WHERE e.current_grade > el.max_grade
                    AND e.next_promotion_date <= CURDATE()
                ";

                if ($department > 0) {
                    $query .= " AND e.department_id = :department";
                }

                $query .= " ORDER BY e.next_promotion_date ASC, e.full_name ASC";

                $stmt = $pdo->prepare($query);

                if ($department > 0) {
                    $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                }

                $stmt->execute();
                $reportData = $stmt->fetchAll();
                break;

            case 'allowances_monthly':
                $reportTitle = 'تقرير العلاوات الشهرية لشهر ' . $month . '/' . $year;

                $startDate = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01';
                $endDate = date('Y-m-t', strtotime($startDate));

                try {
                    // First, check if the allowances table exists and get its structure
                    $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'allowances'");
                    $tableExists = $tableCheckStmt->rowCount() > 0;

                    if (!$tableExists) {
                        throw new Exception("جدول العلاوات (allowances) غير موجود في قاعدة البيانات");
                    }

                    // Get table columns
                    $columnsStmt = $pdo->query("DESCRIBE allowances");
                    $columns = $columnsStmt->fetchAll(PDO::FETCH_COLUMN);

                    // Check if required columns exist
                    $requiredColumns = ['id', 'employee_id', 'allowance_date', 'created_by'];
                    $missingColumns = array_diff($requiredColumns, $columns);

                    if (!empty($missingColumns)) {
                        throw new Exception("الأعمدة التالية مفقودة في جدول العلاوات: " . implode(', ', $missingColumns));
                    }

                    // Build a more flexible query based on available columns
                    $query = "
                        SELECT a.id, a.employee_id, a.allowance_date,
                               " . (in_array('allowance_number', $columns) ? "a.allowance_number," : "'' as allowance_number,") . "
                               " . (in_array('grade_at_time', $columns) ? "a.grade_at_time," : "e.current_grade as grade_at_time,") . "
                               " . (in_array('notes', $columns) ? "a.notes," : "'' as notes,") . "
                               e.employee_number, e.full_name, d.name as department_name,
                               u.full_name as created_by_name
                        FROM allowances a
                        JOIN employees e ON a.employee_id = e.id
                        JOIN departments d ON e.department_id = d.id
                        JOIN users u ON a.created_by = u.id
                        WHERE a.allowance_date BETWEEN :start_date AND :end_date
                    ";

                    if ($department > 0) {
                        $query .= " AND e.department_id = :department";
                    }

                    $query .= " ORDER BY a.allowance_date ASC, e.full_name ASC";

                    $stmt = $pdo->prepare($query);
                    $stmt->bindParam(':start_date', $startDate);
                    $stmt->bindParam(':end_date', $endDate);

                    if ($department > 0) {
                        $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                    }

                    $stmt->execute();
                    $reportData = $stmt->fetchAll();

                    // If no data found, check if there are any allowances in the table
                    if (empty($reportData)) {
                        $countStmt = $pdo->query("SELECT COUNT(*) FROM allowances");
                        $totalAllowances = $countStmt->fetchColumn();

                        if ($totalAllowances == 0) {
                            error_log("No allowances found in the table. Total records: 0");
                        } else {
                            error_log("Allowances exist in the table ($totalAllowances records) but none match the criteria for $month/$year");

                            // Get the date range of available allowances
                            $rangeStmt = $pdo->query("SELECT MIN(allowance_date) as min_date, MAX(allowance_date) as max_date FROM allowances");
                            $dateRange = $rangeStmt->fetch(PDO::FETCH_ASSOC);
                            error_log("Available allowances date range: " . $dateRange['min_date'] . " to " . $dateRange['max_date']);
                        }
                    }

                } catch (Exception $ex) {
                    error_log("Allowances Monthly Report Error: " . $ex->getMessage());
                    flash('error_message', 'خطأ في تقرير العلاوات الشهرية: ' . $ex->getMessage(), 'alert alert-danger');
                    $reportData = [];
                }
                break;

            case 'promotions_monthly':
                $reportTitle = 'تقرير الترفيعات الشهرية لشهر ' . $month . '/' . $year;

                $startDate = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01';
                $endDate = date('Y-m-t', strtotime($startDate));

                try {
                    // First, check if the promotions table exists and get its structure
                    $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'promotions'");
                    $tableExists = $tableCheckStmt->rowCount() > 0;

                    if (!$tableExists) {
                        throw new Exception("جدول الترفيعات (promotions) غير موجود في قاعدة البيانات");
                    }

                    // Get table columns
                    $columnsStmt = $pdo->query("DESCRIBE promotions");
                    $columns = $columnsStmt->fetchAll(PDO::FETCH_COLUMN);

                    // Check if required columns exist
                    $requiredColumns = ['id', 'employee_id', 'promotion_date', 'created_by'];
                    $missingColumns = array_diff($requiredColumns, $columns);

                    if (!empty($missingColumns)) {
                        throw new Exception("الأعمدة التالية مفقودة في جدول الترفيعات: " . implode(', ', $missingColumns));
                    }

                    // Build a more flexible query based on available columns
                    $query = "
                        SELECT p.id, p.employee_id, p.promotion_date,
                               " . (in_array('from_grade', $columns) ? "p.from_grade," : "e.current_grade - 1 as from_grade,") . "
                               " . (in_array('to_grade', $columns) ? "p.to_grade," : "e.current_grade as to_grade,") . "
                               " . (in_array('years_in_previous_grade', $columns) ? "p.years_in_previous_grade," : "0 as years_in_previous_grade,") . "
                               " . (in_array('notes', $columns) ? "p.notes," : "'' as notes,") . "
                               e.employee_number, e.full_name, d.name as department_name,
                               u.full_name as created_by_name
                        FROM promotions p
                        JOIN employees e ON p.employee_id = e.id
                        JOIN departments d ON e.department_id = d.id
                        JOIN users u ON p.created_by = u.id
                        WHERE p.promotion_date BETWEEN :start_date AND :end_date
                    ";

                    if ($department > 0) {
                        $query .= " AND e.department_id = :department";
                    }

                    $query .= " ORDER BY p.promotion_date ASC, e.full_name ASC";

                    $stmt = $pdo->prepare($query);
                    $stmt->bindParam(':start_date', $startDate);
                    $stmt->bindParam(':end_date', $endDate);

                    if ($department > 0) {
                        $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                    }

                    $stmt->execute();
                    $reportData = $stmt->fetchAll();

                    // If no data found, check if there are any promotions in the table
                    if (empty($reportData)) {
                        $countStmt = $pdo->query("SELECT COUNT(*) FROM promotions");
                        $totalPromotions = $countStmt->fetchColumn();

                        if ($totalPromotions == 0) {
                            error_log("No promotions found in the table. Total records: 0");
                        } else {
                            error_log("Promotions exist in the table ($totalPromotions records) but none match the criteria for $month/$year");

                            // Get the date range of available promotions
                            $rangeStmt = $pdo->query("SELECT MIN(promotion_date) as min_date, MAX(promotion_date) as max_date FROM promotions");
                            $dateRange = $rangeStmt->fetch(PDO::FETCH_ASSOC);
                            error_log("Available promotions date range: " . $dateRange['min_date'] . " to " . $dateRange['max_date']);
                        }
                    }

                } catch (Exception $ex) {
                    error_log("Promotions Monthly Report Error: " . $ex->getMessage());
                    flash('error_message', 'خطأ في تقرير الترفيعات الشهرية: ' . $ex->getMessage(), 'alert alert-danger');
                    $reportData = [];
                }
                break;

            case 'appreciation_yearly':
                $reportTitle = 'تقرير كتب الشكر السنوية لعام ' . $year;

                $startDate = $year . '-01-01';
                $endDate = $year . '-12-31';

                try {
                    // First, check if the appreciation_letters table exists and get its structure
                    $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'appreciation_letters'");
                    $tableExists = $tableCheckStmt->rowCount() > 0;

                    if (!$tableExists) {
                        throw new Exception("جدول كتب الشكر (appreciation_letters) غير موجود في قاعدة البيانات");
                    }

                    // Get table columns
                    $columnsStmt = $pdo->query("DESCRIBE appreciation_letters");
                    $columns = $columnsStmt->fetchAll(PDO::FETCH_COLUMN);

                    // Check if required columns exist
                    $requiredColumns = ['id', 'employee_id', 'letter_date', 'created_by'];
                    $missingColumns = array_diff($requiredColumns, $columns);

                    if (!empty($missingColumns)) {
                        throw new Exception("الأعمدة التالية مفقودة في جدول كتب الشكر: " . implode(', ', $missingColumns));
                    }

                    // Build a more flexible query based on available columns
                    $query = "
                        SELECT a.id, a.employee_id, a.letter_date,
                               " . (in_array('letter_number', $columns) ? "a.letter_number," : "'' as letter_number,") . "
                               " . (in_array('issuer', $columns) ? "a.issuer," : "'normal' as issuer,") . "
                               " . (in_array('months_reduction', $columns) ? "a.months_reduction," : "0 as months_reduction,") . "
                               " . (in_array('notes', $columns) ? "a.notes," : "'' as notes,") . "
                               e.employee_number, e.full_name, d.name as department_name,
                               u.full_name as created_by_name
                        FROM appreciation_letters a
                        JOIN employees e ON a.employee_id = e.id
                        JOIN departments d ON e.department_id = d.id
                        JOIN users u ON a.created_by = u.id
                        WHERE a.letter_date BETWEEN :start_date AND :end_date
                    ";

                    if ($department > 0) {
                        $query .= " AND e.department_id = :department";
                    }

                    $query .= " ORDER BY a.letter_date ASC, e.full_name ASC";

                    $stmt = $pdo->prepare($query);
                    $stmt->bindParam(':start_date', $startDate);
                    $stmt->bindParam(':end_date', $endDate);

                    if ($department > 0) {
                        $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                    }

                    $stmt->execute();
                    $reportData = $stmt->fetchAll();

                    // If no data found, check if there are any appreciation letters in the table
                    if (empty($reportData)) {
                        $countStmt = $pdo->query("SELECT COUNT(*) FROM appreciation_letters");
                        $totalLetters = $countStmt->fetchColumn();

                        if ($totalLetters == 0) {
                            error_log("No appreciation letters found in the table. Total records: 0");
                        } else {
                            error_log("Appreciation letters exist in the table ($totalLetters records) but none match the criteria for year $year");

                            // Get the date range of available appreciation letters
                            $rangeStmt = $pdo->query("SELECT MIN(letter_date) as min_date, MAX(letter_date) as max_date FROM appreciation_letters");
                            $dateRange = $rangeStmt->fetch(PDO::FETCH_ASSOC);
                            error_log("Available appreciation letters date range: " . $dateRange['min_date'] . " to " . $dateRange['max_date']);
                        }
                    }

                } catch (Exception $ex) {
                    error_log("Appreciation Letters Yearly Report Error: " . $ex->getMessage());
                    flash('error_message', 'خطأ في تقرير كتب الشكر السنوية: ' . $ex->getMessage(), 'alert alert-danger');
                    $reportData = [];
                }
                break;

            case 'employees_summary':
                $reportTitle = 'تقرير ملخص بيانات الموظفين';

                $query = "
                    SELECT e.*, d.name as department_name, el.name as education_level_name
                    FROM employees e
                    JOIN departments d ON e.department_id = d.id
                    JOIN education_levels el ON e.education_level_id = el.id
                ";

                if ($department > 0) {
                    $query .= " WHERE e.department_id = :department";
                }

                $query .= " ORDER BY e.current_grade ASC, e.full_name ASC";

                $stmt = $pdo->prepare($query);

                if ($department > 0) {
                    $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                }

                $stmt->execute();
                $reportData = $stmt->fetchAll();
                break;

            case 'department_summary':
                $reportTitle = 'تقرير ملخص الأقسام';

                try {
                    // Check if appreciation_letters table exists
                    $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'appreciation_letters'");
                    $appreciationTableExists = $tableCheckStmt->rowCount() > 0;

                    // Get department statistics
                    $query = "
                        SELECT
                            d.id,
                            d.name,
                            COUNT(e.id) as total_employees,
                            IFNULL(SUM(CASE WHEN e.allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . " AND e.next_allowance_date <= CURDATE() THEN 1 ELSE 0 END), 0) as eligible_allowances,
                            IFNULL(SUM(CASE WHEN e.next_promotion_date <= CURDATE() THEN 1 ELSE 0 END), 0) as eligible_promotions,
                            IFNULL(AVG(e.years_of_service), 0) as avg_years_service
                    ";

                    // Add appreciation letters count only if the table exists
                    if ($appreciationTableExists) {
                        $query .= ",
                            IFNULL((SELECT COUNT(*) FROM appreciation_letters a JOIN employees e2 ON a.employee_id = e2.id WHERE e2.department_id = d.id), 0) as total_appreciation
                        ";
                    } else {
                        $query .= ",
                            0 as total_appreciation
                        ";
                    }

                    $query .= "
                        FROM departments d
                        LEFT JOIN employees e ON d.id = e.department_id
                        GROUP BY d.id, d.name
                        ORDER BY d.name ASC
                    ";

                    $stmt = $pdo->prepare($query);
                    $stmt->execute();
                    $reportData = $stmt->fetchAll();

                } catch (Exception $ex) {
                    error_log("Department Summary Report Error: " . $ex->getMessage());
                    flash('error_message', 'خطأ في تقرير ملخص الأقسام: ' . $ex->getMessage(), 'alert alert-danger');
                    $reportData = [];
                }
                break;

            case 'salary_history':
                $reportTitle = 'تقرير تاريخ الرواتب الاسمية لعام ' . $year;

                $startDate = $year . '-01-01';
                $endDate = $year . '-12-31';

                $query = "
                    SELECT sh.*, e.employee_number, e.full_name, d.name as department_name, u.full_name as created_by_name
                    FROM salary_history sh
                    JOIN employees e ON sh.employee_id = e.id
                    JOIN departments d ON e.department_id = d.id
                    JOIN users u ON sh.created_by = u.id
                    WHERE sh.effective_date BETWEEN :start_date AND :end_date
                ";

                if ($department > 0) {
                    $query .= " AND e.department_id = :department";
                }

                if ($grade > 0) {
                    $query .= " AND sh.grade = :grade";
                }

                $query .= " ORDER BY sh.effective_date DESC, e.full_name ASC";

                $stmt = $pdo->prepare($query);
                $stmt->bindParam(':start_date', $startDate);
                $stmt->bindParam(':end_date', $endDate);

                if ($department > 0) {
                    $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                }

                if ($grade > 0) {
                    $stmt->bindParam(':grade', $grade, PDO::PARAM_INT);
                }

                $stmt->execute();
                $reportData = $stmt->fetchAll();
                break;
        }
    } catch (PDOException $e) {
        error_log("Generate Report Error for type $reportType: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء إنشاء التقرير: ' . $e->getMessage(), 'alert alert-danger');
        $reportData = [];
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-chart-bar me-2"></i> التقارير
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <?php if (!empty($reportType)): ?>
            <button onclick="window.print();" class="btn btn-outline-secondary no-print">
                <i class="fas fa-print me-1"></i> طباعة التقرير
            </button>
            <button class="btn btn-outline-success btn-export no-print" data-table-target="#reportTable" data-filename="<?php echo $reportType . '_' . date('Y-m-d'); ?>">
                <i class="fas fa-file-excel me-1"></i> تصدير
            </button>
            <a href="index.php" class="btn btn-outline-primary no-print ms-2">
                <i class="fas fa-home me-1"></i> الرئيسية
            </a>
        <?php else: ?>
            <a href="index.php" class="btn btn-outline-primary no-print">
                <i class="fas fa-home me-1"></i> الرئيسية
            </a>
        <?php endif; ?>
    </div>

    <!-- Print Header (visible only when printing) -->
    <div class="print-header print-only" style="display: none;">
        <h1>نظام إدارة العلاوات والترفيع وكتب الشكر</h1>
        <h2><?php echo $reportTitle ?? 'التقارير'; ?></h2>
        <p>تاريخ الطباعة: <?php echo date('d/m/Y H:i'); ?></p>
    </div>
</div>

<!-- Report Selection -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i> اختيار التقرير
        </h5>
    </div>
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="get" class="row g-3">
            <div class="col-md-4">
                <label for="type" class="form-label">نوع التقرير</label>
                <select class="form-select" id="type" name="type" required>
                    <option value="">اختر نوع التقرير</option>
                    <optgroup label="تقارير الاستحقاقات">
                        <option value="allowances_eligible" <?php echo ($reportType === 'allowances_eligible') ? 'selected' : ''; ?>>الموظفين المستحقين للعلاوة</option>
                        <option value="promotions_eligible" <?php echo ($reportType === 'promotions_eligible') ? 'selected' : ''; ?>>الموظفين المستحقين للترفيع</option>
                        <option value="upcoming_allowances" <?php echo ($reportType === 'upcoming_allowances') ? 'selected' : ''; ?>>الموظفين المستحقين للعلاوة خلال فترة محددة</option>
                        <option value="upcoming_promotions" <?php echo ($reportType === 'upcoming_promotions') ? 'selected' : ''; ?>>الموظفين المستحقين للترفيع خلال فترة محددة</option>
                        <option value="upcoming_retirement" <?php echo ($reportType === 'upcoming_retirement') ? 'selected' : ''; ?>>الموظفين المقبلين على التقاعد</option>
                    </optgroup>
                    <optgroup label="تقارير الإجراءات">
                        <option value="allowances_monthly" <?php echo ($reportType === 'allowances_monthly') ? 'selected' : ''; ?>>العلاوات الشهرية</option>
                        <option value="promotions_monthly" <?php echo ($reportType === 'promotions_monthly') ? 'selected' : ''; ?>>الترفيعات الشهرية</option>
                        <option value="appreciation_yearly" <?php echo ($reportType === 'appreciation_yearly') ? 'selected' : ''; ?>>كتب الشكر السنوية</option>
                        <option value="salary_history" <?php echo ($reportType === 'salary_history') ? 'selected' : ''; ?>>تاريخ الرواتب الاسمية</option>
                    </optgroup>
                    <optgroup label="تقارير إحصائية">
                        <option value="employees_summary" <?php echo ($reportType === 'employees_summary') ? 'selected' : ''; ?>>ملخص بيانات الموظفين</option>
                        <option value="department_summary" <?php echo ($reportType === 'department_summary') ? 'selected' : ''; ?>>ملخص الأقسام</option>
                        <option value="education_summary" <?php echo ($reportType === 'education_summary') ? 'selected' : ''; ?>>ملخص حسب التحصيل الدراسي</option>
                        <option value="grade_summary" <?php echo ($reportType === 'grade_summary') ? 'selected' : ''; ?>>ملخص حسب الدرجة الوظيفية</option>
                    </optgroup>
                </select>
            </div>
            <div class="col-md-3">
                <label for="department" class="form-label">القسم</label>
                <select class="form-select" id="department" name="department">
                    <option value="0">جميع الأقسام</option>
                    <?php foreach ($departments as $dept): ?>
                        <option value="<?php echo $dept['id']; ?>" <?php echo ($department == $dept['id']) ? 'selected' : ''; ?>>
                            <?php echo $dept['name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="col-md-2 date-filter">
                <label for="year" class="form-label">السنة</label>
                <select class="form-select" id="year" name="year">
                    <?php for ($y = date('Y') + 1; $y >= date('Y') - 5; $y--): ?>
                        <option value="<?php echo $y; ?>" <?php echo ($year == $y) ? 'selected' : ''; ?>>
                            <?php echo $y; ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>

            <div class="col-md-2 date-filter">
                <label for="month" class="form-label">الشهر</label>
                <select class="form-select" id="month" name="month">
                    <?php for ($m = 1; $m <= 12; $m++): ?>
                        <option value="<?php echo $m; ?>" <?php echo ($month == $m) ? 'selected' : ''; ?>>
                            <?php echo getArabicMonthName($m); ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>

            <div class="col-md-3 period-filter" style="display: none;">
                <label for="period" class="form-label">الفترة</label>
                <select class="form-select" id="period" name="period">
                    <option value="30" <?php echo (isset($_GET['period']) && $_GET['period'] == '30') ? 'selected' : ''; ?>>خلال 30 يوم</option>
                    <option value="60" <?php echo (isset($_GET['period']) && $_GET['period'] == '60') ? 'selected' : ''; ?>>خلال 60 يوم</option>
                    <option value="90" <?php echo (isset($_GET['period']) && $_GET['period'] == '90') ? 'selected' : ''; ?>>خلال 90 يوم</option>
                    <option value="180" <?php echo (isset($_GET['period']) && $_GET['period'] == '180') ? 'selected' : ''; ?>>خلال 6 أشهر</option>
                    <option value="365" <?php echo (isset($_GET['period']) && $_GET['period'] == '365') ? 'selected' : ''; ?>>خلال سنة</option>
                </select>
            </div>

            <div class="col-md-3 grade-filter" style="display: none;">
                <label for="grade" class="form-label">الدرجة</label>
                <select class="form-select" id="grade" name="grade">
                    <option value="0">جميع الدرجات</option>
                    <?php for ($g = 1; $g <= 10; $g++): ?>
                        <option value="<?php echo $g; ?>" <?php echo (isset($_GET['grade']) && $_GET['grade'] == $g) ? 'selected' : ''; ?>>
                            الدرجة <?php echo $g; ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>

            <div class="col-md-3 education-filter" style="display: none;">
                <label for="education" class="form-label">التحصيل الدراسي</label>
                <select class="form-select" id="education" name="education">
                    <option value="0">جميع المستويات</option>
                    <?php
                    // Get education levels
                    try {
                        $eduStmt = $pdo->query("SELECT id, name FROM education_levels ORDER BY max_grade ASC");
                        $educationLevels = $eduStmt->fetchAll();
                        foreach ($educationLevels as $edu):
                    ?>
                        <option value="<?php echo $edu['id']; ?>" <?php echo (isset($_GET['education']) && $_GET['education'] == $edu['id']) ? 'selected' : ''; ?>>
                            <?php echo $edu['name']; ?>
                        </option>
                    <?php
                        endforeach;
                    } catch (PDOException $e) {
                        error_log("Get Education Levels Error: " . $e->getMessage());
                    }
                    ?>
                </select>
            </div>

            <div class="col-md-1 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i> عرض
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Report Results -->
<?php if (!empty($reportType)): ?>
    <div class="card">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">
                <i class="fas fa-file-alt me-2"></i> <?php echo $reportTitle; ?>
            </h5>
        </div>
        <div class="card-body">
            <?php if (count($reportData) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="reportTable">
                        <thead>
                            <tr>
                                <?php if ($reportType === 'allowances_eligible' || $reportType === 'promotions_eligible' || $reportType === 'employees_summary'): ?>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم</th>
                                    <th>الدرجة</th>
                                    <th>القسم</th>
                                    <?php if ($reportType === 'employees_summary'): ?>
                                        <th>التحصيل الدراسي</th>
                                        <th>تاريخ التعيين</th>
                                        <th>سنوات الخدمة</th>
                                    <?php endif; ?>
                                    <?php if ($reportType === 'allowances_eligible'): ?>
                                        <th>العلاوات المستلمة</th>
                                        <th>تاريخ آخر علاوة</th>
                                        <th>تاريخ العلاوة القادمة</th>
                                    <?php endif; ?>
                                    <?php if ($reportType === 'promotions_eligible'): ?>
                                        <th>التحصيل الدراسي</th>
                                        <th>الحد الأقصى للدرجة</th>
                                        <th>تاريخ آخر ترفيع</th>
                                        <th>تاريخ الترفيع القادم</th>
                                    <?php endif; ?>
                                <?php elseif ($reportType === 'allowances_monthly'): ?>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم</th>
                                    <th>القسم</th>
                                    <th>الدرجة</th>
                                    <th>تاريخ العلاوة</th>
                                    <th>رقم العلاوة</th>
                                    <th>تمت بواسطة</th>
                                    <th>ملاحظات</th>
                                <?php elseif ($reportType === 'promotions_monthly'): ?>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم</th>
                                    <th>القسم</th>
                                    <th>تاريخ الترفيع</th>
                                    <th>من الدرجة</th>
                                    <th>إلى الدرجة</th>
                                    <th>سنوات الخدمة في الدرجة السابقة</th>
                                    <th>تمت بواسطة</th>
                                    <th>ملاحظات</th>
                                <?php elseif ($reportType === 'appreciation_yearly'): ?>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم</th>
                                    <th>القسم</th>
                                    <th>رقم الكتاب</th>
                                    <th>تاريخ الكتاب</th>
                                    <th>الجهة المانحة</th>
                                    <th>تخفيض المدة</th>
                                    <th>تمت بواسطة</th>
                                    <th>ملاحظات</th>
                                <?php elseif ($reportType === 'department_summary'): ?>
                                    <th>القسم</th>
                                    <th>عدد الموظفين</th>
                                    <th>مستحقي العلاوة</th>
                                    <th>مستحقي الترفيع</th>
                                    <th>متوسط سنوات الخدمة</th>
                                    <th>عدد كتب الشكر</th>
                                <?php elseif ($reportType === 'upcoming_retirement'): ?>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم</th>
                                    <th>القسم</th>
                                    <th>الدرجة</th>
                                    <th>تاريخ الميلاد</th>
                                    <th>العمر الحالي</th>
                                    <th>تاريخ التقاعد</th>
                                <?php elseif ($reportType === 'salary_history'): ?>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم</th>
                                    <th>القسم</th>
                                    <th>الدرجة</th>
                                    <th>المرحلة</th>
                                    <th>الراتب الاسمي</th>
                                    <th>تاريخ التغيير</th>
                                    <th>السبب</th>
                                    <th>تمت بواسطة</th>
                                    <th>ملاحظات</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($reportData as $row): ?>
                                <tr>
                                    <?php if ($reportType === 'allowances_eligible' || $reportType === 'promotions_eligible' || $reportType === 'employees_summary'): ?>
                                        <td><?php echo $row['employee_number']; ?></td>
                                        <td><?php echo $row['full_name']; ?></td>
                                        <td><?php echo $row['current_grade']; ?></td>
                                        <td><?php echo $row['department_name']; ?></td>
                                        <?php if ($reportType === 'employees_summary'): ?>
                                            <td><?php echo $row['education_level_name']; ?></td>
                                            <td><?php echo formatArabicDate($row['hire_date']); ?></td>
                                            <td><?php echo $row['years_of_service']; ?> سنة</td>
                                        <?php endif; ?>
                                        <?php if ($reportType === 'allowances_eligible'): ?>
                                            <td><?php echo $row['allowances_in_current_grade']; ?> من <?php echo ALLOWANCES_PER_GRADE; ?></td>
                                            <td><?php echo $row['last_allowance_date'] ? formatArabicDate($row['last_allowance_date']) : '-'; ?></td>
                                            <td><?php echo formatArabicDate($row['next_allowance_date']); ?></td>
                                        <?php endif; ?>
                                        <?php if ($reportType === 'promotions_eligible'): ?>
                                            <td><?php echo $row['education_level_name']; ?></td>
                                            <td><?php echo $row['max_grade']; ?></td>
                                            <td><?php echo $row['last_promotion_date'] ? formatArabicDate($row['last_promotion_date']) : '-'; ?></td>
                                            <td><?php echo formatArabicDate($row['next_promotion_date']); ?></td>
                                        <?php endif; ?>
                                    <?php elseif ($reportType === 'allowances_monthly'): ?>
                                        <td><?php echo $row['employee_number']; ?></td>
                                        <td><?php echo $row['full_name']; ?></td>
                                        <td><?php echo $row['department_name']; ?></td>
                                        <td><?php echo $row['grade_at_time']; ?></td>
                                        <td><?php echo formatArabicDate($row['allowance_date']); ?></td>
                                        <td><?php echo $row['allowance_number']; ?></td>
                                        <td><?php echo $row['created_by_name']; ?></td>
                                        <td><?php echo $row['notes'] ?: '-'; ?></td>
                                    <?php elseif ($reportType === 'promotions_monthly'): ?>
                                        <td><?php echo $row['employee_number']; ?></td>
                                        <td><?php echo $row['full_name']; ?></td>
                                        <td><?php echo $row['department_name']; ?></td>
                                        <td><?php echo formatArabicDate($row['promotion_date']); ?></td>
                                        <td><?php echo $row['from_grade']; ?></td>
                                        <td><?php echo $row['to_grade']; ?></td>
                                        <td><?php echo $row['years_in_previous_grade']; ?> سنة</td>
                                        <td><?php echo $row['created_by_name']; ?></td>
                                        <td><?php echo $row['notes'] ?: '-'; ?></td>
                                    <?php elseif ($reportType === 'appreciation_yearly'): ?>
                                        <td><?php echo $row['employee_number']; ?></td>
                                        <td><?php echo $row['full_name']; ?></td>
                                        <td><?php echo $row['department_name']; ?></td>
                                        <td><?php echo $row['letter_number']; ?></td>
                                        <td><?php echo formatArabicDate($row['letter_date']); ?></td>
                                        <td><?php echo $row['issuer'] == 'prime_minister' ? 'رئيس الوزراء' : 'عادي'; ?></td>
                                        <td><?php echo $row['months_reduction']; ?> شهر</td>
                                        <td><?php echo $row['created_by_name']; ?></td>
                                        <td><?php echo $row['notes'] ?: '-'; ?></td>
                                    <?php elseif ($reportType === 'department_summary'): ?>
                                        <td><?php echo $row['name']; ?></td>
                                        <td><?php echo $row['total_employees']; ?></td>
                                        <td><?php echo $row['eligible_allowances']; ?></td>
                                        <td><?php echo $row['eligible_promotions']; ?></td>
                                        <td><?php echo ($row['avg_years_service'] !== null ? round($row['avg_years_service'], 1) : 0); ?> سنة</td>
                                        <td><?php echo $row['total_appreciation']; ?></td>
                                    <?php elseif ($reportType === 'upcoming_retirement'): ?>
                                        <td><?php echo $row['employee_number']; ?></td>
                                        <td><?php echo $row['full_name']; ?></td>
                                        <td><?php echo $row['department_name']; ?></td>
                                        <td><?php echo $row['current_grade']; ?></td>
                                        <td><?php echo formatArabicDate($row['birth_date']); ?></td>
                                        <td><?php echo $row['current_age']; ?> سنة</td>
                                        <td><?php echo formatArabicDate($row['retirement_date']); ?></td>
                                    <?php elseif ($reportType === 'salary_history'): ?>
                                        <td><?php echo $row['employee_number']; ?></td>
                                        <td><?php echo $row['full_name']; ?></td>
                                        <td><?php echo $row['department_name']; ?></td>
                                        <td><?php echo $row['grade']; ?></td>
                                        <td><?php echo $row['stage']; ?></td>
                                        <td><?php echo number_format($row['nominal_salary'], 2); ?> دينار</td>
                                        <td><?php echo formatArabicDate($row['effective_date']); ?></td>
                                        <td>
                                            <?php
                                            $reasons = [
                                                'initial' => 'راتب أولي',
                                                'allowance' => 'علاوة سنوية',
                                                'promotion' => 'ترفيع',
                                                'higher_degree' => 'شهادة أعلى',
                                                'manual' => 'تعديل يدوي'
                                            ];
                                            echo $reasons[$row['reason']] ?? $row['reason'];
                                            ?>
                                        </td>
                                        <td><?php echo $row['created_by_name']; ?></td>
                                        <td><?php echo $row['notes'] ?: '-'; ?></td>
                                    <?php endif; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> لا توجد بيانات للعرض
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php else: ?>
    <div class="row">
        <div class="col-md-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> يرجى اختيار نوع التقرير لعرض البيانات
            </div>
        </div>
    </div>

    <!-- Report Types Cards -->
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill-wave fa-3x text-success mb-3"></i>
                    <h5 class="card-title">تقرير المستحقين للعلاوة</h5>
                    <p class="card-text">عرض قائمة بالموظفين المستحقين للعلاوة حالياً</p>
                    <a href="?type=allowances_eligible" class="btn btn-success">عرض التقرير</a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-level-up-alt fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">تقرير المستحقين للترفيع</h5>
                    <p class="card-text">عرض قائمة بالموظفين المستحقين للترفيع حالياً</p>
                    <a href="?type=promotions_eligible" class="btn btn-primary">عرض التقرير</a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-certificate fa-3x text-warning mb-3"></i>
                    <h5 class="card-title">تقرير كتب الشكر السنوية</h5>
                    <p class="card-text">عرض كتب الشكر الممنوحة خلال العام الحالي</p>
                    <a href="?type=appreciation_yearly&year=<?php echo date('Y'); ?>" class="btn btn-warning">عرض التقرير</a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-alt fa-3x text-info mb-3"></i>
                    <h5 class="card-title">تقرير العلاوات الشهرية</h5>
                    <p class="card-text">عرض العلاوات الممنوحة خلال شهر محدد</p>
                    <a href="?type=allowances_monthly&year=<?php echo date('Y'); ?>&month=<?php echo date('m'); ?>" class="btn btn-info">عرض التقرير</a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-3x text-secondary mb-3"></i>
                    <h5 class="card-title">تقرير ملخص بيانات الموظفين</h5>
                    <p class="card-text">عرض ملخص لبيانات جميع الموظفين</p>
                    <a href="?type=employees_summary" class="btn btn-secondary">عرض التقرير</a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-chart-pie fa-3x text-danger mb-3"></i>
                    <h5 class="card-title">تقرير ملخص الأقسام</h5>
                    <p class="card-text">عرض إحصائيات وملخص لكل قسم</p>
                    <a href="?type=department_summary" class="btn btn-danger">عرض التقرير</a>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
// Print footer (visible only when printing)
?>
<div class="print-footer print-only" style="display: none;">
    <p>تم إنشاء هذا التقرير بواسطة نظام إدارة العلاوات والترفيع وكتب الشكر</p>
    <p>© <?php echo date('Y'); ?> جميع الحقوق محفوظة</p>
</div>
<?php

// Include footer
require_once 'includes/footer.php';
?>

<script>
// Function to show/hide filters based on report type
function toggleFilters() {
    const reportType = document.getElementById('type').value;

    // Hide all filters first
    document.querySelectorAll('.date-filter').forEach(el => el.style.display = 'none');
    document.querySelector('.period-filter').style.display = 'none';
    document.querySelector('.grade-filter').style.display = 'none';
    document.querySelector('.education-filter').style.display = 'none';

    // Show relevant filters based on report type
    if (reportType === 'allowances_monthly' || reportType === 'promotions_monthly') {
        // Show year and month filters for monthly reports
        document.querySelectorAll('.date-filter').forEach(el => el.style.display = 'block');
    } else if (reportType === 'appreciation_yearly' || reportType === 'salary_history') {
        // Show only year filter for yearly reports
        document.querySelector('.date-filter:first-of-type').style.display = 'block';
    } else if (reportType === 'upcoming_allowances' || reportType === 'upcoming_promotions' || reportType === 'upcoming_retirement') {
        // Show period filter for upcoming reports
        document.querySelector('.period-filter').style.display = 'block';
    }

    // Show grade filter for grade-related reports
    if (reportType === 'grade_summary' || reportType === 'upcoming_promotions' || reportType === 'upcoming_allowances') {
        document.querySelector('.grade-filter').style.display = 'block';
    }

    // Show education filter for education-related reports
    if (reportType === 'education_summary' || reportType === 'upcoming_promotions') {
        document.querySelector('.education-filter').style.display = 'block';
    }
}

// Initialize filters on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleFilters();

    // Add event listener for report type change
    document.getElementById('type').addEventListener('change', toggleFilters);
});
</script>
