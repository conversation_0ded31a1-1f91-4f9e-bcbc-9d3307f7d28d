<?php
/**
 * Reset Admin Password
 * إعادة تعيين كلمة مرور المدير
 */

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once 'config/database.php';

echo "<h1>إعادة تعيين كلمة مرور المدير</h1>";

// Define new admin credentials
$adminUsername = 'admin';
$adminPassword = 'admin123';
$hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);

try {
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<p style='color:red;'>جدول المستخدمين غير موجود!</p>";
        
        // Create users table
        $createTableSQL = "
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL,
                role ENUM('admin', 'hr', 'manager', 'viewer') NOT NULL DEFAULT 'viewer',
                status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
                last_login DATETIME,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($createTableSQL);
        echo "<p style='color:green;'>تم إنشاء جدول المستخدمين بنجاح!</p>";
        
        // Create admin user
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, full_name, email, role, status)
            VALUES (?, ?, ?, ?, 'admin', 'active')
        ");
        
        $stmt->execute([$adminUsername, $hashedPassword, 'مدير النظام', '<EMAIL>']);
        
        echo "<p style='color:green;'>تم إنشاء مستخدم المدير بنجاح!</p>";
    } else {
        // Check if admin user exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$adminUsername]);
        $adminExists = $stmt->rowCount() > 0;
        
        if ($adminExists) {
            // Update admin password
            $stmt = $pdo->prepare("UPDATE users SET password = ?, status = 'active' WHERE username = ?");
            $stmt->execute([$hashedPassword, $adminUsername]);
            
            echo "<p style='color:green;'>تم تحديث كلمة مرور المدير بنجاح!</p>";
        } else {
            // Create admin user
            $stmt = $pdo->prepare("
                INSERT INTO users (username, password, full_name, email, role, status)
                VALUES (?, ?, ?, ?, 'admin', 'active')
            ");
            
            $stmt->execute([$adminUsername, $hashedPassword, 'مدير النظام', '<EMAIL>']);
            
            echo "<p style='color:green;'>تم إنشاء مستخدم المدير بنجاح!</p>";
        }
    }
    
    // Verify password hash
    $stmt = $pdo->prepare("SELECT password FROM users WHERE username = ?");
    $stmt->execute([$adminUsername]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        $storedHash = $admin['password'];
        $verifyResult = password_verify($adminPassword, $storedHash);
        
        echo "<h2>التحقق من كلمة المرور:</h2>";
        echo "<p>كلمة المرور المدخلة: " . $adminPassword . "</p>";
        echo "<p>التشفير المخزن: " . $storedHash . "</p>";
        echo "<p>نتيجة التحقق: " . ($verifyResult ? "<span style='color:green;'>صحيحة</span>" : "<span style='color:red;'>غير صحيحة</span>") . "</p>";
    }
    
    // Show login details
    echo "<div style='background-color: #f8f9fa; padding: 15px; border: 1px solid #ddd; margin-top: 20px;'>";
    echo "<h2>معلومات تسجيل الدخول:</h2>";
    echo "<p><strong>اسم المستخدم:</strong> " . $adminUsername . "</p>";
    echo "<p><strong>كلمة المرور:</strong> " . $adminPassword . "</p>";
    echo "<p><a href='login.php' style='display: inline-block; padding: 10px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px;'>انتقل إلى صفحة تسجيل الدخول</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color:red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
