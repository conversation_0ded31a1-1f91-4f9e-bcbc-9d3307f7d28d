<?php
/**
 * Basic Database Restore - استعادة قاعدة البيانات الأساسية
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and admin role
requireRole(['admin']);

// Create backup directory if it doesn't exist
$backupDir = 'backups';
if (!file_exists($backupDir)) {
    mkdir($backupDir, 0755, true);
}

// Process restore request
if (isset($_POST['restore_table'])) {
    try {
        // Get form data
        $tableName = sanitize($_POST['table_name']);
        $dropTable = isset($_POST['drop_table']) ? true : false;
        $createTable = isset($_POST['create_table']) ? true : false;
        $createTableSQL = isset($_POST['create_table_sql']) ? $_POST['create_table_sql'] : '';
        $insertData = isset($_POST['insert_data']) ? true : false;
        $insertDataSQL = isset($_POST['insert_data_sql']) ? $_POST['insert_data_sql'] : '';
        
        // Get database connection
        $pdo = $GLOBALS['pdo'];
        
        // Create log file
        $logFile = $backupDir . '/basic_restore_' . date('Y-m-d_H-i-s') . '.log';
        file_put_contents($logFile, "بدء عملية استعادة الجدول: $tableName\n");
        file_put_contents($logFile, "التاريخ: " . date('Y-m-d H:i:s') . "\n\n", FILE_APPEND);
        
        // Disable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS=0");
        file_put_contents($logFile, "تم تعطيل فحص المفاتيح الخارجية\n", FILE_APPEND);
        
        // Drop table if requested
        if ($dropTable) {
            try {
                $pdo->exec("DROP TABLE IF EXISTS `$tableName`");
                file_put_contents($logFile, "تم حذف الجدول $tableName بنجاح\n", FILE_APPEND);
            } catch (PDOException $e) {
                file_put_contents($logFile, "خطأ في حذف الجدول: " . $e->getMessage() . "\n", FILE_APPEND);
                throw new Exception("خطأ في حذف الجدول: " . $e->getMessage());
            }
        }
        
        // Create table if requested
        if ($createTable && !empty($createTableSQL)) {
            try {
                $pdo->exec($createTableSQL);
                file_put_contents($logFile, "تم إنشاء الجدول $tableName بنجاح\n", FILE_APPEND);
            } catch (PDOException $e) {
                file_put_contents($logFile, "خطأ في إنشاء الجدول: " . $e->getMessage() . "\n", FILE_APPEND);
                throw new Exception("خطأ في إنشاء الجدول: " . $e->getMessage());
            }
        }
        
        // Insert data if requested
        if ($insertData && !empty($insertDataSQL)) {
            try {
                $pdo->exec($insertDataSQL);
                file_put_contents($logFile, "تم إدخال البيانات في الجدول $tableName بنجاح\n", FILE_APPEND);
            } catch (PDOException $e) {
                file_put_contents($logFile, "خطأ في إدخال البيانات: " . $e->getMessage() . "\n", FILE_APPEND);
                throw new Exception("خطأ في إدخال البيانات: " . $e->getMessage());
            }
        }
        
        // Enable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS=1");
        file_put_contents($logFile, "تم إعادة تفعيل فحص المفاتيح الخارجية\n", FILE_APPEND);
        
        // Log activity
        logActivity(
            $_SESSION['user_id'],
            'استعادة جدول',
            'backup',
            0,
            'تم استعادة الجدول: ' . $tableName
        );
        
        file_put_contents($logFile, "انتهت عملية استعادة الجدول بنجاح\n", FILE_APPEND);
        flash('success_message', 'تم استعادة الجدول بنجاح. يمكنك الاطلاع على سجل العملية في الملف: ' . $logFile, 'alert alert-success');
    } catch (Exception $e) {
        error_log("Basic Restore Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء استعادة الجدول: ' . $e->getMessage(), 'alert alert-danger');
    }
}

// Get list of tables
$tables = [];
try {
    $result = $pdo->query("SHOW TABLES");
    while ($row = $result->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }
} catch (PDOException $e) {
    error_log("Error getting tables: " . $e->getMessage());
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-database me-2"></i> استعادة قاعدة البيانات الأساسية
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى الرئيسية
        </a>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i> استعادة جدول
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>تعليمات:</strong> استخدم هذه الصفحة لاستعادة جدول واحد في المرة الواحدة. يمكنك إما إنشاء جدول جديد أو إدخال بيانات في جدول موجود.
                </div>
                
                <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" id="restoreForm">
                    <div class="mb-3">
                        <label for="table_name" class="form-label">اسم الجدول</label>
                        <input type="text" class="form-control" id="table_name" name="table_name" required>
                        <div class="form-text">أدخل اسم الجدول الذي تريد استعادته. إذا كان الجدول موجود بالفعل، يمكنك اختيار حذفه أولاً.</div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="drop_table" name="drop_table" checked>
                        <label class="form-check-label" for="drop_table">حذف الجدول إذا كان موجوداً</label>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="create_table" name="create_table" checked>
                        <label class="form-check-label" for="create_table">إنشاء الجدول</label>
                    </div>
                    
                    <div class="mb-3">
                        <label for="create_table_sql" class="form-label">SQL لإنشاء الجدول</label>
                        <textarea class="form-control" id="create_table_sql" name="create_table_sql" rows="5" placeholder="CREATE TABLE `table_name` (...)"></textarea>
                        <div class="form-text">أدخل استعلام SQL لإنشاء الجدول. يجب أن يبدأ بـ CREATE TABLE.</div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="insert_data" name="insert_data" checked>
                        <label class="form-check-label" for="insert_data">إدخال بيانات</label>
                    </div>
                    
                    <div class="mb-3">
                        <label for="insert_data_sql" class="form-label">SQL لإدخال البيانات</label>
                        <textarea class="form-control" id="insert_data_sql" name="insert_data_sql" rows="5" placeholder="INSERT INTO `table_name` (...) VALUES (...)"></textarea>
                        <div class="form-text">أدخل استعلام SQL لإدخال البيانات. يجب أن يبدأ بـ INSERT INTO.</div>
                    </div>
                    
                    <button type="submit" name="restore_table" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> استعادة الجدول
                    </button>
                </form>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i> الجداول الموجودة
                </h5>
            </div>
            <div class="card-body">
                <?php if (count($tables) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الجدول</th>
                                    <th>عدد الصفوف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tables as $table): ?>
                                    <?php
                                    // Get row count
                                    $rowCount = 0;
                                    try {
                                        $result = $pdo->query("SELECT COUNT(*) FROM `$table`");
                                        $rowCount = $result->fetchColumn();
                                    } catch (PDOException $e) {
                                        // Ignore errors
                                    }
                                    ?>
                                    <tr>
                                        <td><?php echo $table; ?></td>
                                        <td><?php echo $rowCount; ?></td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-info view-structure" data-table="<?php echo $table; ?>">
                                                <i class="fas fa-table"></i> عرض البنية
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> لا توجد جداول في قاعدة البيانات.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal for table structure -->
<div class="modal fade" id="structureModal" tabindex="-1" aria-labelledby="structureModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="structureModalLabel">بنية الجدول</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="structureContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle view structure buttons
    const viewStructureButtons = document.querySelectorAll('.view-structure');
    const structureModal = new bootstrap.Modal(document.getElementById('structureModal'));
    const structureContent = document.getElementById('structureContent');
    
    viewStructureButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const table = this.getAttribute('data-table');
            
            // Show loading
            structureContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin me-2"></i> جاري تحميل بنية الجدول...</div>';
            structureModal.show();
            
            // Fetch table structure using AJAX
            fetch('ajax_get_structure.php?table=' + table)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        structureContent.innerHTML = `
                            <h6>استعلام إنشاء الجدول:</h6>
                            <pre class="bg-light p-3">${data.create_table}</pre>
                            
                            <h6>استعلام إدخال البيانات (أول 10 صفوف):</h6>
                            <pre class="bg-light p-3">${data.insert_data}</pre>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                يمكنك نسخ هذه الاستعلامات واستخدامها في نموذج استعادة الجدول.
                            </div>
                        `;
                    } else {
                        structureContent.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                ${data.error}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    structureContent.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            حدث خطأ أثناء جلب بنية الجدول: ${error}
                        </div>
                    `;
                });
        });
    });
});
</script>
