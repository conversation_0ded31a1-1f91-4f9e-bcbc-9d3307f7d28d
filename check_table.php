<?php
/**
 * Check Table Structure
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

// Check if table exists
try {
    $tableName = 'notifications';
    $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
    $tableExists = $stmt->rowCount() > 0;

    echo "<h2>فحص جدول $tableName</h2>";

    if ($tableExists) {
        echo "<p>الجدول موجود.</p>";

        // Get table structure
        $stmt = $pdo->query("DESCRIBE $tableName");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<h3>هيكل الجدول:</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

        $hasStatusColumn = false;

        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";

            if (strtolower($column['Field']) === 'status') {
                $hasStatusColumn = true;
            }
        }

        echo "</table>";

        if (!$hasStatusColumn) {
            echo "<p style='color: red;'>عمود 'status' غير موجود في الجدول!</p>";

            // Check for similar columns that might be used instead
            echo "<h3>أعمدة مشابهة قد تكون بديلة:</h3>";
            $possibleAlternatives = ['active', 'is_active', 'state', 'employee_status', 'emp_status'];
            $foundAlternatives = [];

            foreach ($columns as $column) {
                foreach ($possibleAlternatives as $alternative) {
                    if (stripos($column['Field'], $alternative) !== false) {
                        $foundAlternatives[] = $column['Field'];
                    }
                }
            }

            if (!empty($foundAlternatives)) {
                echo "<ul>";
                foreach ($foundAlternatives as $alt) {
                    echo "<li>$alt</li>";
                }
                echo "</ul>";
            } else {
                echo "<p>لم يتم العثور على أعمدة مشابهة.</p>";
            }
        } else {
            echo "<p style='color: green;'>عمود 'status' موجود في الجدول.</p>";
        }

        // Get sample data
        $stmt = $pdo->query("SELECT * FROM $tableName LIMIT 1");
        $sampleData = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($sampleData) {
            echo "<h3>نموذج بيانات:</h3>";
            echo "<pre>";
            print_r($sampleData);
            echo "</pre>";
        } else {
            echo "<p>لا توجد بيانات في الجدول.</p>";
        }
    } else {
        echo "<p style='color: red;'>الجدول غير موجود!</p>";
    }
} catch (PDOException $e) {
    echo "<h2 style='color: red;'>خطأ في قاعدة البيانات:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
