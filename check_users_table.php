<?php
/**
 * Check Users Table
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

// Start HTML output
echo "<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>فحص جدول المستخدمين</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #0066cc; margin-top: 30px; }
        p { margin: 10px 0; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .back-link { display: inline-block; margin-top: 20px; padding: 10px 15px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>فحص جدول المستخدمين</h1>";

// Check users table structure
echo "<h2>هيكل جدول المستخدمين</h2>";
try {
    $columnsStmt = $pdo->query("DESCRIBE users");
    $columns = $columnsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>أعمدة جدول المستخدمين:</p>";
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
    // Check if status column exists
    $statusExists = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'status') {
            $statusExists = true;
            break;
        }
    }
    
    if ($statusExists) {
        echo "<p class='success'>✓ عمود status موجود في جدول المستخدمين</p>";
    } else {
        echo "<p class='warning'>⚠️ عمود status غير موجود في جدول المستخدمين</p>";
        
        // Add status column
        try {
            $pdo->exec("ALTER TABLE users ADD COLUMN status ENUM('active', 'inactive') NOT NULL DEFAULT 'active' AFTER role");
            echo "<p class='success'>✓ تم إضافة عمود status إلى جدول المستخدمين بنجاح</p>";
        } catch (PDOException $e) {
            echo "<p class='error'>خطأ في إضافة عمود status: " . $e->getMessage() . "</p>";
        }
    }
} catch (PDOException $e) {
    echo "<p class='error'>خطأ في جلب هيكل جدول المستخدمين: " . $e->getMessage() . "</p>";
}

// Get users data
echo "<h2>بيانات المستخدمين</h2>";
try {
    $usersStmt = $pdo->query("SELECT id, username, full_name, role FROM users");
    $users = $usersStmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) > 0) {
        echo "<p>عدد المستخدمين: " . count($users) . "</p>";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>الصلاحية</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['username'] . "</td>";
            echo "<td>" . $user['full_name'] . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ لا يوجد مستخدمين في قاعدة البيانات</p>";
    }
} catch (PDOException $e) {
    echo "<p class='error'>خطأ في جلب بيانات المستخدمين: " . $e->getMessage() . "</p>";
}

// Add links to navigate
echo "
    <div style='margin-top: 30px;'>
        <a href='generate_notifications.php' class='back-link'>العودة إلى صفحة توليد التنبيهات</a>
    </div>
</body>
</html>";
?>
