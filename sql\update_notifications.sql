-- تحديث جدول الإشعارات
ALTER TABLE `notifications`
  ADD COLUMN `employee_id` int(11) DEFAULT NULL AFTER `user_id`,
  ADD COLUMN `due_date` date DEFAULT NULL COMMENT 'تاريخ الاستحقاق إن وجد' AFTER `created_at`,
  ADD COLUMN `priority` varchar(20) NOT NULL DEFAULT 'normal' COMMENT 'أولوية التنبيه: high, normal, low' AFTER `due_date`,
  ADD COLUMN `read_at` datetime DEFAULT NULL AFTER `read`,
  <PERSON><PERSON><PERSON> COLUMN `read` `is_read` tinyint(1) NOT NULL DEFAULT 0,
  MODIFY COLUMN `type` varchar(50) NOT NULL COMMENT 'نوع التنبيه: promotion, allowance, retirement, etc.',
  ADD INDEX `employee_id` (`employee_id`),
  ADD INDEX `due_date` (`due_date`),
  ADD INDEX `priority` (`priority`),
  ADD CONSTRAINT `notifications_employee_fk` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE SET NULL;

-- إنشاء جدول إعدادات التنبيهات
CREATE TABLE IF NOT EXISTS `notification_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `promotion_notify_days` int(11) NOT NULL DEFAULT 30 COMMENT 'عدد أيام التنبيه قبل استحقاق الترقية',
  `allowance_notify_days` int(11) NOT NULL DEFAULT 30 COMMENT 'عدد أيام التنبيه قبل استحقاق العلاوة',
  `retirement_notify_days` int(11) NOT NULL DEFAULT 180 COMMENT 'عدد أيام التنبيه قبل سن التقاعد',
  `email_notifications` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'تفعيل التنبيهات عبر البريد الإلكتروني',
  `browser_notifications` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'تفعيل التنبيهات عبر المتصفح',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  CONSTRAINT `notification_settings_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدخال إعدادات افتراضية للمستخدمين الحاليين
INSERT INTO `notification_settings` (`user_id`, `promotion_notify_days`, `allowance_notify_days`, `retirement_notify_days`, `email_notifications`, `browser_notifications`)
SELECT `id`, 30, 30, 180, 1, 1 FROM `users` 
WHERE NOT EXISTS (SELECT 1 FROM `notification_settings` WHERE `notification_settings`.`user_id` = `users`.`id`);

-- تحديث إعدادات النظام
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('notification_check_interval', '24', 'الفاصل الزمني بالساعات لفحص وإنشاء التنبيهات الجديدة'),
('notification_max_age_days', '30', 'عدد أيام الاحتفاظ بالتنبيهات المقروءة')
ON DUPLICATE KEY UPDATE 
  `setting_value` = VALUES(`setting_value`),
  `description` = VALUES(`description`);
