<?php
/**
 * Apply Law 103 Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and proper role
requireRole(['admin', 'hr']);

// Check if employee ID is provided
$employeeId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($employeeId <= 0) {
    flash('error_message', 'معرف الموظف غير صحيح', 'alert alert-danger');
    redirect('employees.php');
}

// Get employee details
try {
    $stmt = $pdo->prepare("
        SELECT e.*, d.name as department_name, el.name as education_level_name, el.max_grade
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        JOIN education_levels el ON e.education_level_id = el.id
        WHERE e.id = :id
    ");
    $stmt->execute([':id' => $employeeId]);
    $employee = $stmt->fetch();
    
    if (!$employee) {
        flash('error_message', 'الموظف غير موجود', 'alert alert-danger');
        redirect('employees.php');
    }
    
    // Check if Law 103 is already applied
    if ($employee['law_103_applied']) {
        flash('error_message', 'تم تطبيق قانون 103 على هذا الموظف بالفعل', 'alert alert-warning');
        redirect('employee_details.php?id=' . $employeeId);
    }
} catch (PDOException $e) {
    error_log("Get Employee Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الموظف', 'alert alert-danger');
    redirect('employees.php');
}

// Get education levels
try {
    $eduStmt = $pdo->query("
        SELECT id, name, max_grade, min_grade 
        FROM education_levels 
        WHERE min_grade < " . $employee['current_grade'] . " 
        ORDER BY id ASC
    ");
    $educationLevels = $eduStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Education Levels Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء تحميل المستويات التعليمية', 'alert alert-danger');
    $educationLevels = [];
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $educationLevelId = (int)$_POST['education_level_id'];
    $degreeDate = sanitize($_POST['degree_date']);
    $notes = sanitize($_POST['notes']);
    
    // Validate inputs
    $errors = [];
    
    if ($educationLevelId <= 0) {
        $errors[] = 'يرجى اختيار المستوى التعليمي الجديد';
    }
    
    if (empty($degreeDate)) {
        $errors[] = 'تاريخ الحصول على الشهادة مطلوب';
    }
    
    // Check if selected education level is valid
    $validEducation = false;
    foreach ($educationLevels as $level) {
        if ($level['id'] == $educationLevelId) {
            $validEducation = true;
            break;
        }
    }
    
    if (!$validEducation) {
        $errors[] = 'المستوى التعليمي المختار غير صالح';
    }
    
    // If no errors, apply Law 103
    if (empty($errors)) {
        $result = applyLaw103($employeeId, $educationLevelId, $degreeDate, $notes);
        
        if ($result) {
            flash('success_message', 'تم تطبيق قانون 103 بنجاح', 'alert alert-success');
            redirect('employee_details.php?id=' . $employeeId);
        } else {
            flash('error_message', 'حدث خطأ أثناء تطبيق قانون 103', 'alert alert-danger');
        }
    } else {
        // Display errors
        $errorMessage = '<ul class="mb-0">';
        foreach ($errors as $error) {
            $errorMessage .= '<li>' . $error . '</li>';
        }
        $errorMessage .= '</ul>';
        
        flash('error_message', $errorMessage, 'alert alert-danger');
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-graduation-cap me-2"></i> تطبيق قانون 103
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="employee_details.php?id=<?php echo $employeeId; ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى تفاصيل الموظف
        </a>
    </div>
</div>

<!-- Employee Information Card -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-user me-2"></i> معلومات الموظف
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الرقم الوظيفي:</div>
                <div><?php echo $employee['employee_number']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الاسم:</div>
                <div><?php echo $employee['full_name']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">القسم:</div>
                <div><?php echo $employee['department_name']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الدرجة الحالية:</div>
                <div>الدرجة <?php echo $employee['current_grade']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">المرحلة الحالية:</div>
                <div>المرحلة <?php echo $employee['current_stage']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">التحصيل الدراسي الحالي:</div>
                <div><?php echo $employee['education_level_name']; ?></div>
            </div>
        </div>
    </div>
</div>

<!-- Apply Law 103 Form -->
<div class="card">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">
            <i class="fas fa-graduation-cap me-2"></i> تطبيق قانون 103 للشهادات الأعلى
        </h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>معلومات عن قانون 103:</strong>
            <ul class="mb-0 mt-2">
                <li>عند حصول الموظف على شهادة أعلى، يتم إعادته إلى الدرجة المقابلة للشهادة الجديدة.</li>
                <li>يترفع الموظف كل <?php echo LAW_103_PROMOTION_YEARS; ?> سنوات حتى يصل إلى الدرجة السابقة.</li>
                <li>بعد العودة إلى الدرجة الأصلية، يستأنف الترفيع حسب النظام الطبيعي.</li>
            </ul>
        </div>
        
        <form action="<?php echo $_SERVER['PHP_SELF'] . '?id=' . $employeeId; ?>" method="post" class="needs-validation" novalidate>
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="education_level_id" class="form-label">المستوى التعليمي الجديد <span class="text-danger">*</span></label>
                    <select class="form-select" id="education_level_id" name="education_level_id" required>
                        <option value="">اختر المستوى التعليمي الجديد</option>
                        <?php foreach ($educationLevels as $level): ?>
                            <option value="<?php echo $level['id']; ?>"><?php echo $level['name']; ?> (الدرجة <?php echo $level['min_grade']; ?>)</option>
                        <?php endforeach; ?>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار المستوى التعليمي الجديد</div>
                </div>
                <div class="col-md-6">
                    <label for="degree_date" class="form-label">تاريخ الحصول على الشهادة <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="degree_date" name="degree_date" required>
                    <div class="invalid-feedback">يرجى إدخال تاريخ الحصول على الشهادة</div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="notes" class="form-label">ملاحظات</label>
                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
            </div>
            
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> تطبيق قانون 103 سيؤدي إلى تغيير درجة الموظف وراتبه الاسمي. هذه العملية لا يمكن التراجع عنها.
            </div>
            
            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary px-5">
                    <i class="fas fa-save me-1"></i> تطبيق قانون 103
                </button>
                <a href="employee_details.php?id=<?php echo $employeeId; ?>" class="btn btn-secondary px-5">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
