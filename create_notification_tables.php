<?php
/**
 * Create Notification Tables
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

// Function to check if table exists
function tableExists($pdo, $tableName) {
    $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
    return $stmt->rowCount() > 0;
}

// Function to create table
function createTable($pdo, $tableName, $sql) {
    try {
        $pdo->exec($sql);
        return true;
    } catch (PDOException $e) {
        echo "<p style='color: red;'>خطأ في إنشاء جدول $tableName: " . $e->getMessage() . "</p>";
        return false;
    }
}

// Start HTML output
echo "<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إنشاء جداول التنبيهات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #0066cc; margin-top: 30px; }
        p { margin: 10px 0; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .back-link { display: inline-block; margin-top: 20px; padding: 10px 15px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>إنشاء جداول التنبيهات</h1>";

// Create notifications table if it doesn't exist
if (!tableExists($pdo, 'notifications')) {
    echo "<h2>إنشاء جدول التنبيهات (notifications)</h2>";
    
    $sql = "
        CREATE TABLE `notifications` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) NOT NULL,
          `employee_id` int(11) DEFAULT NULL,
          `type` enum('promotion','allowance','retirement','system') NOT NULL,
          `title` varchar(255) NOT NULL,
          `message` text NOT NULL,
          `is_read` tinyint(1) NOT NULL DEFAULT 0,
          `read_at` datetime DEFAULT NULL,
          `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `due_date` date DEFAULT NULL,
          `priority` enum('low','normal','high') NOT NULL DEFAULT 'normal',
          PRIMARY KEY (`id`),
          KEY `user_id` (`user_id`),
          KEY `employee_id` (`employee_id`),
          KEY `type` (`type`),
          KEY `is_read` (`is_read`),
          KEY `due_date` (`due_date`),
          CONSTRAINT `notifications_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
          CONSTRAINT `notifications_employee_fk` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if (createTable($pdo, 'notifications', $sql)) {
        echo "<p class='success'>✓ تم إنشاء جدول التنبيهات بنجاح</p>";
    }
} else {
    echo "<h2>جدول التنبيهات (notifications)</h2>";
    echo "<p class='info'>ℹ️ جدول التنبيهات موجود بالفعل</p>";
    
    // Check if is_read column exists
    try {
        $columnsStmt = $pdo->query("DESCRIBE notifications");
        $columns = $columnsStmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('is_read', $columns)) {
            echo "<p class='warning'>⚠️ عمود is_read غير موجود في جدول التنبيهات</p>";
            
            // Add is_read column
            $pdo->exec("ALTER TABLE notifications ADD COLUMN is_read tinyint(1) NOT NULL DEFAULT 0 AFTER message");
            $pdo->exec("ALTER TABLE notifications ADD INDEX is_read (is_read)");
            
            echo "<p class='success'>✓ تم إضافة عمود is_read إلى جدول التنبيهات بنجاح</p>";
        } else {
            echo "<p class='success'>✓ عمود is_read موجود في جدول التنبيهات</p>";
        }
        
        // Check if read_at column exists
        if (!in_array('read_at', $columns)) {
            echo "<p class='warning'>⚠️ عمود read_at غير موجود في جدول التنبيهات</p>";
            
            // Add read_at column
            $pdo->exec("ALTER TABLE notifications ADD COLUMN read_at datetime DEFAULT NULL AFTER is_read");
            
            echo "<p class='success'>✓ تم إضافة عمود read_at إلى جدول التنبيهات بنجاح</p>";
        } else {
            echo "<p class='success'>✓ عمود read_at موجود في جدول التنبيهات</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='error'>خطأ في التحقق من أعمدة جدول التنبيهات: " . $e->getMessage() . "</p>";
    }
}

// Create notification_settings table if it doesn't exist
if (!tableExists($pdo, 'notification_settings')) {
    echo "<h2>إنشاء جدول إعدادات التنبيهات (notification_settings)</h2>";
    
    $sql = "
        CREATE TABLE `notification_settings` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) NOT NULL,
          `promotion_notify_days` int(11) NOT NULL DEFAULT 30,
          `allowance_notify_days` int(11) NOT NULL DEFAULT 30,
          `retirement_notify_days` int(11) NOT NULL DEFAULT 180,
          `email_notifications` tinyint(1) NOT NULL DEFAULT 1,
          `browser_notifications` tinyint(1) NOT NULL DEFAULT 1,
          `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          UNIQUE KEY `user_id` (`user_id`),
          CONSTRAINT `notification_settings_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if (createTable($pdo, 'notification_settings', $sql)) {
        echo "<p class='success'>✓ تم إنشاء جدول إعدادات التنبيهات بنجاح</p>";
        
        // Create default notification settings for all users
        try {
            $usersStmt = $pdo->query("SELECT id FROM users WHERE role IN ('admin', 'hr') AND status = 'active'");
            $users = $usersStmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($users) > 0) {
                $insertValues = [];
                foreach ($users as $user) {
                    $insertValues[] = "({$user['id']}, 30, 30, 180, 1, 1)";
                }
                
                $insertSql = "
                    INSERT INTO notification_settings 
                    (user_id, promotion_notify_days, allowance_notify_days, retirement_notify_days, email_notifications, browser_notifications) 
                    VALUES " . implode(", ", $insertValues);
                
                $pdo->exec($insertSql);
                
                echo "<p class='success'>✓ تم إنشاء إعدادات تنبيهات افتراضية لـ " . count($users) . " مستخدم</p>";
            }
        } catch (PDOException $e) {
            echo "<p class='error'>خطأ في إنشاء إعدادات التنبيهات الافتراضية: " . $e->getMessage() . "</p>";
        }
    }
} else {
    echo "<h2>جدول إعدادات التنبيهات (notification_settings)</h2>";
    echo "<p class='info'>ℹ️ جدول إعدادات التنبيهات موجود بالفعل</p>";
}

// Create system_settings table if it doesn't exist
if (!tableExists($pdo, 'system_settings')) {
    echo "<h2>إنشاء جدول إعدادات النظام (system_settings)</h2>";
    
    $sql = "
        CREATE TABLE `system_settings` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `setting_key` varchar(100) NOT NULL,
          `setting_value` text DEFAULT NULL,
          `setting_description` text DEFAULT NULL,
          `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          UNIQUE KEY `setting_key` (`setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if (createTable($pdo, 'system_settings', $sql)) {
        echo "<p class='success'>✓ تم إنشاء جدول إعدادات النظام بنجاح</p>";
        
        // Insert default system settings
        try {
            $defaultSettings = [
                ['notification_check_interval', '24', 'الفترة الزمنية بالساعات بين عمليات فحص التنبيهات'],
                ['notification_max_age_days', '30', 'عدد الأيام التي يتم الاحتفاظ بالتنبيهات المقروءة قبل حذفها'],
                ['enable_notifications', '1', 'تفعيل نظام التنبيهات'],
                ['system_name', 'نظام إدارة العلاوات والترفيع وكتب الشكر', 'اسم النظام'],
                ['system_version', '1.0', 'إصدار النظام'],
                ['retirement_age', '60', 'سن التقاعد']
            ];
            
            $insertSql = "INSERT INTO system_settings (setting_key, setting_value, setting_description) VALUES (?, ?, ?)";
            $stmt = $pdo->prepare($insertSql);
            
            foreach ($defaultSettings as $setting) {
                $stmt->execute($setting);
            }
            
            echo "<p class='success'>✓ تم إنشاء إعدادات النظام الافتراضية بنجاح</p>";
        } catch (PDOException $e) {
            echo "<p class='error'>خطأ في إنشاء إعدادات النظام الافتراضية: " . $e->getMessage() . "</p>";
        }
    }
} else {
    echo "<h2>جدول إعدادات النظام (system_settings)</h2>";
    echo "<p class='info'>ℹ️ جدول إعدادات النظام موجود بالفعل</p>";
}

// Add links to navigate
echo "
    <div style='margin-top: 30px;'>
        <a href='generate_notifications.php' class='back-link'>العودة إلى صفحة توليد التنبيهات</a>
    </div>
</body>
</html>";
?>
