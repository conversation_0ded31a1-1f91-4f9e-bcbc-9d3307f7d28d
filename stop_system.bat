@echo off
echo ======================================================
echo    نظام إدارة العلاوات والترقية وكتب الشكر
echo ======================================================
echo.
echo جاري إيقاف خدمات النظام...
echo.

REM تحديد المسار الحالي
set CURRENT_DIR=%~dp0
set XAMPP_DIR=%CURRENT_DIR%xampp

REM التحقق من وجود مجلد xampp
if not exist "%XAMPP_DIR%" (
    echo خطأ: مجلد xampp غير موجود في المسار %CURRENT_DIR%
    echo يرجى التأكد من وجود XAMPP في المسار الصحيح.
    echo.
    pause
    exit /b 1
)

REM إيقاف خدمات Apache و MySQL
cd /d "%XAMPP_DIR%"
echo جاري إيقاف خدمة MySQL...
call mysql_stop.bat
echo جاري إيقاف خدمة Apache...
call apache_stop.bat

echo.
echo ======================================================
echo تم إيقاف خدمات النظام بنجاح!
echo.
echo يمكنك الآن إغلاق هذه النافذة أو فصل وسيط التخزين المحمول.
echo ======================================================
echo.
pause
