-- ملف إنشاء قاعدة البيانات الأولية (معدل)
-- نظام إدارة العلاوات والترفيع وكتب الشكر

-- إدخال بيانات الأقسام
INSERT INTO `departments` (`name`) VALUES
('قسم الموارد البشرية'),
('قسم تكنولوجيا المعلومات'),
('قسم المالية'),
('قسم الإدارة');

-- إدخال بيانات المستويات التعليمية
INSERT INTO `education_levels` (`name`, `max_grade`) VALUES
('دكتوراه', 1),
('ماجستير', 2),
('بكالوريوس', 4),
('دبلوم', 6),
('ثانوية عامة', 8);

-- إن<PERSON><PERSON>ء جدول الإشعارات (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` enum('allowance','promotion','appreciation','employee','system') NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `link` varchar(255) DEFAULT NULL,
  `read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول إعدادات النظام (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدخال بيانات إعدادات النظام
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('allowances_per_grade', '11', 'عدد العلاوات في كل درجة'),
('promotion_years_lower_grades', '4', 'عدد سنوات الترفيع للدرجات الدنيا (10-5)'),
('promotion_years_upper_grades', '5', 'عدد سنوات الترفيع للدرجات العليا (5-1)'),
('regular_letter_months', '1', 'عدد أشهر تخفيض كتاب الشكر العادي'),
('pm_letter_months', '6', 'عدد أشهر تخفيض كتاب شكر رئيس الوزراء'),
('enable_notifications', '1', 'تفعيل نظام الإشعارات'),
('enable_reminders', '1', 'تفعيل نظام التذكيرات'),
('reminder_days', '30', 'عدد أيام التذكير المسبق');

-- إدخال بيانات الموظفين
INSERT INTO `employees` (`employee_number`, `full_name`, `department_id`, `education_level_id`, `hire_date`, `current_grade`, `years_of_service`, `allowances_in_current_grade`, `last_allowance_date`, `next_allowance_date`, `last_promotion_date`, `next_promotion_date`) VALUES
('1001', 'أحمد محمد علي', 1, 3, '2015-01-15', 5, 8, 3, '2022-01-15', '2023-01-15', '2019-01-15', '2023-01-15'),
('1002', 'سارة أحمد حسين', 1, 3, '2016-03-20', 6, 7, 5, '2022-03-20', '2023-03-20', '2020-03-20', '2024-03-20'),
('1003', 'محمد عبدالله محمود', 2, 2, '2014-05-10', 3, 9, 2, '2022-05-10', '2023-05-10', '2019-05-10', '2024-05-10'),
('1004', 'فاطمة علي حسن', 2, 3, '2017-07-05', 6, 6, 4, '2022-07-05', '2023-07-05', '2021-07-05', '2025-07-05'),
('1005', 'خالد محمد سعيد', 3, 3, '2018-02-25', 7, 5, 3, '2022-02-25', '2023-02-25', '2022-02-25', '2026-02-25'),
('1006', 'نور حسين أحمد', 3, 4, '2019-09-12', 8, 4, 2, '2022-09-12', '2023-09-12', '2022-09-12', '2026-09-12'),
('1007', 'عمر فاروق محمد', 4, 2, '2013-11-30', 3, 10, 6, '2022-11-30', '2023-11-30', '2018-11-30', '2023-11-30'),
('1008', 'ليلى كريم سالم', 4, 3, '2016-08-15', 5, 7, 4, '2022-08-15', '2023-08-15', '2020-08-15', '2024-08-15'),
('1009', 'يوسف طارق أحمد', 1, 4, '2020-04-01', 8, 3, 1, '2022-04-01', '2023-04-01', '2022-04-01', '2026-04-01'),
('1010', 'رنا سمير محمود', 2, 1, '2012-06-20', 2, 11, 5, '2022-06-20', '2023-06-20', '2017-06-20', '2022-06-20');

-- إدخال بيانات العلاوات
INSERT INTO `allowances` (`employee_id`, `allowance_date`, `allowance_number`, `notes`, `created_by`) VALUES
(1, '2022-01-15', 3, 'العلاوة السنوية', 1),
(2, '2022-03-20', 5, 'العلاوة السنوية', 1),
(3, '2022-05-10', 2, 'العلاوة السنوية', 1),
(4, '2022-07-05', 4, 'العلاوة السنوية', 1),
(5, '2022-02-25', 3, 'العلاوة السنوية', 1),
(6, '2022-09-12', 2, 'العلاوة السنوية', 1),
(7, '2022-11-30', 6, 'العلاوة السنوية', 1),
(8, '2022-08-15', 4, 'العلاوة السنوية', 1),
(9, '2022-04-01', 1, 'العلاوة السنوية', 1),
(10, '2022-06-20', 5, 'العلاوة السنوية', 1),
(1, '2021-01-15', 2, 'العلاوة السنوية', 1),
(2, '2021-03-20', 4, 'العلاوة السنوية', 1),
(3, '2021-05-10', 1, 'العلاوة السنوية', 1),
(4, '2021-07-05', 3, 'العلاوة السنوية', 1),
(5, '2021-02-25', 2, 'العلاوة السنوية', 1);

-- إدخال بيانات الترفيعات
INSERT INTO `promotions` (`employee_id`, `promotion_date`, `old_grade`, `new_grade`, `notes`, `created_by`) VALUES
(1, '2019-01-15', 6, 5, 'ترفيع وظيفي', 1),
(2, '2020-03-20', 7, 6, 'ترفيع وظيفي', 1),
(3, '2019-05-10', 4, 3, 'ترفيع وظيفي', 1),
(4, '2021-07-05', 7, 6, 'ترفيع وظيفي', 1),
(5, '2022-02-25', 8, 7, 'ترفيع وظيفي', 1),
(6, '2022-09-12', 9, 8, 'ترفيع وظيفي', 1),
(7, '2018-11-30', 4, 3, 'ترفيع وظيفي', 1),
(8, '2020-08-15', 6, 5, 'ترفيع وظيفي', 1),
(9, '2022-04-01', 9, 8, 'ترفيع وظيفي', 1),
(10, '2017-06-20', 3, 2, 'ترفيع وظيفي', 1);

-- إدخال بيانات كتب الشكر
INSERT INTO `appreciation_letters` (`employee_id`, `letter_date`, `letter_number`, `issuer`, `notes`, `created_by`) VALUES
(1, '2022-06-15', 'A-2022-001', 'regular', 'كتاب شكر للأداء المتميز', 1),
(3, '2022-07-20', 'A-2022-002', 'regular', 'كتاب شكر للأداء المتميز', 1),
(5, '2022-08-10', 'A-2022-003', 'regular', 'كتاب شكر للأداء المتميز', 1),
(7, '2022-09-05', 'A-2022-004', 'regular', 'كتاب شكر للأداء المتميز', 1),
(9, '2022-10-15', 'A-2022-005', 'regular', 'كتاب شكر للأداء المتميز', 1),
(2, '2022-05-25', 'PM-2022-001', 'prime_minister', 'كتاب شكر من رئيس الوزراء للإنجاز المتميز', 1),
(4, '2022-07-30', 'PM-2022-002', 'prime_minister', 'كتاب شكر من رئيس الوزراء للإنجاز المتميز', 1),
(6, '2022-09-20', 'PM-2022-003', 'prime_minister', 'كتاب شكر من رئيس الوزراء للإنجاز المتميز', 1),
(8, '2022-11-10', 'PM-2022-004', 'prime_minister', 'كتاب شكر من رئيس الوزراء للإنجاز المتميز', 1),
(10, '2022-12-05', 'PM-2022-005', 'prime_minister', 'كتاب شكر من رئيس الوزراء للإنجاز المتميز', 1);
