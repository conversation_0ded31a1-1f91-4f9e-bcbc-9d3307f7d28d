<?php
/**
 * Fix Arabic Text Issues
 * إصلاح مشاكل النصوص العربية
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// CSS styles
$styles = '
<style>
    body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 20px;
        direction: rtl;
    }
    .container {
        max-width: 800px;
        margin: 0 auto;
        background-color: #fff;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    h1, h2 {
        color: #007bff;
    }
    .alert-success {
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .alert-warning {
        color: #856404;
        background-color: #fff3cd;
        border-color: #ffeeba;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .btn {
        display: inline-block;
        padding: 8px 16px;
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.5;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        cursor: pointer;
        border: 1px solid transparent;
        border-radius: 4px;
        text-decoration: none;
    }
    .btn-primary {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }
    .btn-success {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    pre {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        overflow: auto;
        text-align: left;
        direction: ltr;
    }
    .step {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border-right: 4px solid #007bff;
    }
    .step h3 {
        margin-top: 0;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
    table, th, td {
        border: 1px solid #ddd;
    }
    th, td {
        padding: 10px;
        text-align: right;
    }
    th {
        background-color: #f2f2f2;
    }
    .arabic-test {
        font-size: 18px;
        padding: 10px;
        margin: 10px 0;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
</style>
';

// Start HTML output
echo '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل النصوص العربية - نظام إدارة العلاوات والترقية</title>
    ' . $styles . '
</head>
<body>
    <div class="container">
        <h1>إصلاح مشاكل النصوص العربية</h1>
        <p>هذه الأداة تساعد في إصلاح مشاكل عرض النصوص العربية في النظام.</p>';

// Test Arabic text display
echo '<div class="step">
    <h3>اختبار عرض النصوص العربية</h3>
    <p>إذا كنت تستطيع قراءة هذا النص بشكل صحيح، فهذا يعني أن المتصفح يدعم اللغة العربية بشكل صحيح.</p>
    <div class="arabic-test">هذا نص عربي للاختبار: أبجد هوز حطي كلمن سعفص قرشت ثخذ ضظغ.</div>
    <p>إذا ظهرت الأحرف العربية بشكل غير صحيح أو كرموز غريبة، فهذا يعني وجود مشكلة في ترميز النصوص.</p>
</div>';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['fix_arabic'])) {
        echo '<h2>جاري إصلاح مشاكل النصوص العربية...</h2>';
        
        $success = true;
        $messages = [];
        
        // Step 1: Check PHP configuration
        echo '<div class="step">
            <h3>الخطوة 1: التحقق من إعدادات PHP</h3>';
        
        // Check mbstring extension
        if (extension_loaded('mbstring')) {
            echo '<p>✓ وحدة mbstring مفعلة.</p>';
        } else {
            $success = false;
            echo '<p>✗ وحدة mbstring غير مفعلة. يرجى تفعيلها في إعدادات PHP.</p>';
            echo '<pre>
في ملف php.ini، قم بإزالة التعليق (;) من السطر التالي:
extension=mbstring
</pre>';
        }
        
        // Check default charset
        $defaultCharset = ini_get('default_charset');
        if ($defaultCharset == 'UTF-8') {
            echo '<p>✓ الترميز الافتراضي في PHP هو UTF-8.</p>';
        } else {
            $success = false;
            echo '<p>✗ الترميز الافتراضي في PHP ليس UTF-8. الترميز الحالي: ' . $defaultCharset . '</p>';
            echo '<pre>
في ملف php.ini، قم بتعديل السطر التالي:
default_charset = "UTF-8"
</pre>';
        }
        
        echo '</div>';
        
        // Step 2: Fix HTML files
        echo '<div class="step">
            <h3>الخطوة 2: إصلاح ملفات HTML</h3>';
        
        // Get all PHP files
        $rootDir = dirname(__FILE__);
        $phpFiles = glob($rootDir . '/*.php');
        $phpFiles = array_merge($phpFiles, glob($rootDir . '/includes/*.php'));
        
        $fixedFiles = 0;
        $totalFiles = count($phpFiles);
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file);
            $modified = false;
            
            // Check for charset meta tag
            if (strpos($content, '<meta charset="utf-8"') === false && 
                strpos($content, '<meta charset="UTF-8"') === false &&
                strpos($content, 'Content-Type: text/html; charset=utf-8') === false) {
                
                // Add charset meta tag if not found
                $content = preg_replace('/<head>/', '<head>' . PHP_EOL . '    <meta charset="UTF-8">', $content);
                $modified = true;
            }
            
            // Check for dir="rtl" attribute
            if (strpos($content, 'dir="rtl"') === false) {
                // Add dir="rtl" attribute if not found
                $content = preg_replace('/<html[^>]*>/', '<html lang="ar" dir="rtl">', $content);
                $modified = true;
            }
            
            // Save modified file
            if ($modified) {
                if (file_put_contents($file, $content)) {
                    $fixedFiles++;
                } else {
                    $success = false;
                    $messages[] = "فشل في تحديث ملف: " . basename($file);
                }
            }
        }
        
        echo '<p>تم فحص ' . $totalFiles . ' ملف، وتم إصلاح ' . $fixedFiles . ' ملف.</p>';
        
        echo '</div>';
        
        // Step 3: Add CSS fixes
        echo '<div class="step">
            <h3>الخطوة 3: إضافة إصلاحات CSS</h3>';
        
        // Check for CSS directory
        $cssDir = $rootDir . '/assets/css';
        if (file_exists($cssDir)) {
            // Find main CSS file
            $cssFiles = glob($cssDir . '/*.css');
            $mainCssFile = '';
            
            foreach ($cssFiles as $cssFile) {
                if (strpos(basename($cssFile), 'style') !== false || 
                    strpos(basename($cssFile), 'main') !== false || 
                    strpos(basename($cssFile), 'custom') !== false) {
                    $mainCssFile = $cssFile;
                    break;
                }
            }
            
            if (empty($mainCssFile) && !empty($cssFiles)) {
                $mainCssFile = $cssFiles[0]; // Use first CSS file if no main file found
            }
            
            if (!empty($mainCssFile)) {
                echo '<p>تم العثور على ملف CSS الرئيسي: ' . basename($mainCssFile) . '</p>';
                
                $cssContent = file_get_contents($mainCssFile);
                
                // Add RTL CSS fixes if not already present
                if (strpos($cssContent, 'direction: rtl') === false) {
                    $rtlCss = "
/* RTL Fixes for Arabic text */
body, .container, .row, .col, .form-group, .table {
    direction: rtl;
    text-align: right;
}

.dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

.input-group-text {
    border-radius: 0 0.25rem 0.25rem 0 !important;
}

.form-control {
    border-radius: 0.25rem 0 0 0.25rem !important;
}

/* Fix for Arabic fonts */
body, h1, h2, h3, h4, h5, h6, p, a, span, div, table, th, td, input, select, textarea, button {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
";
                    
                    if (file_put_contents($mainCssFile, $cssContent . $rtlCss)) {
                        echo '<p>✓ تم إضافة إصلاحات CSS للغة العربية بنجاح.</p>';
                    } else {
                        $success = false;
                        echo '<p>✗ فشل في إضافة إصلاحات CSS للغة العربية.</p>';
                    }
                } else {
                    echo '<p>✓ إصلاحات CSS للغة العربية موجودة بالفعل.</p>';
                }
            } else {
                echo '<p>✗ لم يتم العثور على ملف CSS رئيسي.</p>';
                
                // Create new CSS file
                $newCssFile = $cssDir . '/rtl-fixes.css';
                $rtlCss = "
/* RTL Fixes for Arabic text */
body, .container, .row, .col, .form-group, .table {
    direction: rtl;
    text-align: right;
}

.dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

.input-group-text {
    border-radius: 0 0.25rem 0.25rem 0 !important;
}

.form-control {
    border-radius: 0.25rem 0 0 0.25rem !important;
}

/* Fix for Arabic fonts */
body, h1, h2, h3, h4, h5, h6, p, a, span, div, table, th, td, input, select, textarea, button {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
";
                
                if (file_put_contents($newCssFile, $rtlCss)) {
                    echo '<p>✓ تم إنشاء ملف CSS جديد للإصلاحات: rtl-fixes.css</p>';
                    
                    // Add link to header file
                    $headerFile = $rootDir . '/includes/header.php';
                    if (file_exists($headerFile)) {
                        $headerContent = file_get_contents($headerFile);
                        
                        if (strpos($headerContent, 'rtl-fixes.css') === false) {
                            $cssLink = '<link rel="stylesheet" href="<?php echo APP_URL; ?>/assets/css/rtl-fixes.css">';
                            $headerContent = preg_replace('/<\/head>/', $cssLink . PHP_EOL . '</head>', $headerContent);
                            
                            if (file_put_contents($headerFile, $headerContent)) {
                                echo '<p>✓ تم إضافة رابط ملف CSS الجديد إلى ملف الهيدر.</p>';
                            } else {
                                $success = false;
                                echo '<p>✗ فشل في إضافة رابط ملف CSS الجديد إلى ملف الهيدر.</p>';
                            }
                        }
                    }
                } else {
                    $success = false;
                    echo '<p>✗ فشل في إنشاء ملف CSS جديد للإصلاحات.</p>';
                }
            }
        } else {
            echo '<p>✗ مجلد CSS غير موجود.</p>';
            
            // Create CSS directory and file
            if (mkdir($cssDir, 0755, true)) {
                echo '<p>✓ تم إنشاء مجلد CSS.</p>';
                
                $newCssFile = $cssDir . '/rtl-fixes.css';
                $rtlCss = "
/* RTL Fixes for Arabic text */
body, .container, .row, .col, .form-group, .table {
    direction: rtl;
    text-align: right;
}

.dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

.input-group-text {
    border-radius: 0 0.25rem 0.25rem 0 !important;
}

.form-control {
    border-radius: 0.25rem 0 0 0.25rem !important;
}

/* Fix for Arabic fonts */
body, h1, h2, h3, h4, h5, h6, p, a, span, div, table, th, td, input, select, textarea, button {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
";
                
                if (file_put_contents($newCssFile, $rtlCss)) {
                    echo '<p>✓ تم إنشاء ملف CSS جديد للإصلاحات: rtl-fixes.css</p>';
                } else {
                    $success = false;
                    echo '<p>✗ فشل في إنشاء ملف CSS جديد للإصلاحات.</p>';
                }
            } else {
                $success = false;
                echo '<p>✗ فشل في إنشاء مجلد CSS.</p>';
            }
        }
        
        echo '</div>';
        
        // Final result
        if ($success) {
            echo '<div class="alert-success">
                <h2>تم إصلاح مشاكل النصوص العربية بنجاح!</h2>
                <p>يجب أن تظهر النصوص العربية الآن بشكل صحيح في جميع أنحاء النظام.</p>
                <p><a href="index.php" class="btn btn-primary">الذهاب إلى الصفحة الرئيسية</a></p>
            </div>';
        } else {
            echo '<div class="alert-warning">
                <h2>تم إصلاح بعض مشاكل النصوص العربية، لكن قد تكون هناك مشاكل أخرى!</h2>
                <p>يرجى مراجعة الرسائل أعلاه ومحاولة إصلاح المشاكل المتبقية يدويًا.</p>
                <p><a href="index.php" class="btn btn-primary">الذهاب إلى الصفحة الرئيسية</a></p>
            </div>';
        }
    }
}

// Display fix form
echo '<h2>إصلاح مشاكل النصوص العربية</h2>
<p>انقر على الزر أدناه لإصلاح مشاكل عرض النصوص العربية:</p>
<form method="post">
    <button type="submit" name="fix_arabic" class="btn btn-primary">إصلاح مشاكل النصوص العربية</button>
</form>

<h2>المشاكل الشائعة وحلولها</h2>
<table>
    <tr>
        <th>المشكلة</th>
        <th>الحل</th>
    </tr>
    <tr>
        <td>النصوص العربية تظهر كرموز غريبة</td>
        <td>تأكد من أن ترميز الصفحة هو UTF-8</td>
    </tr>
    <tr>
        <td>النصوص العربية تظهر بشكل معكوس</td>
        <td>تأكد من أن اتجاه الصفحة هو RTL (من اليمين إلى اليسار)</td>
    </tr>
    <tr>
        <td>الخط العربي غير واضح أو غير مناسب</td>
        <td>استخدم خطوط مناسبة للغة العربية مثل Tahoma أو Segoe UI</td>
    </tr>
    <tr>
        <td>مشاكل في ترتيب الحقول والأزرار</td>
        <td>تأكد من تطبيق إصلاحات CSS للغة العربية</td>
    </tr>
</table>

<h2>روابط مفيدة</h2>
<ul>
    <li><a href="fix_paths.php">إصلاح المسارات والإعدادات</a></li>
    <li><a href="fix_ui.php">إصلاح مشاكل الواجهة الرسومية</a></li>
    <li><a href="index.php">الصفحة الرئيسية</a></li>
</ul>

</div>
</body>
</html>';
?>
