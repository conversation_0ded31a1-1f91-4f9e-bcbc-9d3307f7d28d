<?php
/**
 * Update Database Page
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include header
require_once 'includes/header.php';

// Require login and admin role
requireRole(['admin']);

// Set page title
$pageTitle = "تحديث قاعدة البيانات";

// Get SQL file content
$sqlFile = 'sql/update_notifications.sql';
$sqlContent = file_exists($sqlFile) ? file_get_contents($sqlFile) : '';

// Process update request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_database'])) {
    try {
        // Split SQL file into individual queries
        $queries = explode(';', $sqlContent);
        $executedQueries = 0;
        $errors = [];

        // Execute each query
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                try {
                    $pdo->exec($query);
                    $executedQueries++;
                } catch (PDOException $e) {
                    $errors[] = "خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "<br>الاستعلام: " . $query;
                }
            }
        }

        // Log activity
        logActivity(
            $_SESSION['user_id'],
            'تحديث قاعدة البيانات',
            'system',
            0,
            'تم تنفيذ ' . $executedQueries . ' استعلام لتحديث قاعدة البيانات'
        );

        if (empty($errors)) {
            flash('success_message', 'تم تحديث قاعدة البيانات بنجاح. تم تنفيذ ' . $executedQueries . ' استعلام.', 'alert alert-success');
        } else {
            flash('error_message', 'حدث خطأ أثناء تحديث قاعدة البيانات:<br>' . implode('<br>', $errors), 'alert alert-danger');
        }
    } catch (Exception $e) {
        error_log("Database Update Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء تحديث قاعدة البيانات: ' . $e->getMessage(), 'alert alert-danger');
    }
}
?>

<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="display-5 mb-0">
                <i class="fas fa-database me-2"></i> تحديث قاعدة البيانات
            </h1>
        </div>
        <div class="col-md-6 text-md-end">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة إلى الرئيسية
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-code me-2"></i> استعلامات التحديث
            </h5>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> سيقوم هذا الإجراء بتحديث هيكل قاعدة البيانات. يرجى التأكد من عمل نسخة احتياطية قبل المتابعة.
            </div>

            <div class="mb-3">
                <label for="sqlContent" class="form-label">استعلامات SQL للتنفيذ:</label>
                <textarea class="form-control" id="sqlContent" rows="15" readonly><?php echo htmlspecialchars($sqlContent); ?></textarea>
            </div>

            <form method="post" onsubmit="return confirm('هل أنت متأكد من رغبتك في تنفيذ هذه الاستعلامات؟');">
                <button type="submit" name="update_database" class="btn btn-primary">
                    <i class="fas fa-play me-1"></i> تنفيذ التحديث
                </button>
            </form>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">
                <i class="fas fa-info-circle me-2"></i> معلومات التحديث
            </h5>
        </div>
        <div class="card-body">
            <p>سيقوم هذا التحديث بإجراء التغييرات التالية على قاعدة البيانات:</p>
            <ol>
                <li>تحديث جدول <code>notifications</code> لإضافة حقول جديدة مثل <code>employee_id</code> و <code>due_date</code> و <code>priority</code>.</li>
                <li>تغيير اسم الحقل <code>read</code> إلى <code>is_read</code> وإضافة حقل <code>read_at</code>.</li>
                <li>إنشاء جدول <code>notification_settings</code> لتخزين إعدادات التنبيهات لكل مستخدم.</li>
                <li>إضافة إعدادات افتراضية للمستخدمين الحاليين.</li>
                <li>تحديث إعدادات النظام لإضافة خيارات جديدة متعلقة بالتنبيهات.</li>
            </ol>
            <p>بعد تنفيذ هذا التحديث، ستتمكن من استخدام نظام التنبيهات المتقدم الذي يوفر:</p>
            <ul>
                <li>تنبيهات للموظفين المستحقين للترقية.</li>
                <li>تنبيهات للموظفين المستحقين للعلاوة.</li>
                <li>تنبيهات للموظفين المقتربين من سن التقاعد.</li>
                <li>إمكانية تخصيص إعدادات التنبيهات لكل مستخدم.</li>
                <li>تنبيهات ذات أولوية عالية للحالات العاجلة.</li>
            </ul>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
