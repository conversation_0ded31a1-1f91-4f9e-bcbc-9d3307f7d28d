<?php
/**
 * Full Backup and Restore Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include header
require_once 'includes/header.php';

// Require login and admin role
requireRole(['admin']);

// Create backup directory if it doesn't exist
$backupDir = 'backups';
if (!file_exists($backupDir)) {
    $mkdirResult = mkdir($backupDir, 0755, true);
    if (!$mkdirResult) {
        error_log("Failed to create backup directory: $backupDir");
        flash('error_message', 'فشل في إنشاء مجلد النسخ الاحتياطية. تأكد من صلاحيات الكتابة.', 'alert alert-danger');
    }
}

// Check if ZipArchive class is available
if (!class_exists('ZipArchive')) {
    error_log("ZipArchive class is not available");
    flash('error_message', 'مكتبة ZipArchive غير متوفرة. يرجى تثبيتها لاستخدام خاصية النسخ الاحتياطي.', 'alert alert-danger');
}

// Set maximum execution time to 5 minutes
ini_set('max_execution_time', 300);
// Set memory limit to 512MB
ini_set('memory_limit', '512M');

// Process full backup request
if (isset($_POST['create_full_backup'])) {
    try {
        error_log("Starting backup process");

        // Generate backup filename with timestamp
        $timestamp = date('Y-m-d_H-i-s');
        $backupFilename = 'full_backup_' . $timestamp . '.zip';
        $backupPath = $backupDir . '/' . $backupFilename;

        error_log("Backup path: $backupPath");

        // Create a temporary directory for backup files
        $tempBackupDir = $backupDir . '/temp_' . $timestamp;
        if (!file_exists($tempBackupDir)) {
            $mkdirResult = mkdir($tempBackupDir, 0755, true);
            if (!$mkdirResult) {
                error_log("Failed to create temporary directory: $tempBackupDir");
                throw new Exception('فشل في إنشاء المجلد المؤقت للنسخة الاحتياطية');
            }
        }

        error_log("Temporary directory created: $tempBackupDir");

        // Step 1: Export database
        $dbBackupFile = $tempBackupDir . '/database.sql';
        error_log("Exporting database to: $dbBackupFile");
        $exportResult = exportDatabase($dbBackupFile);

        if (!$exportResult) {
            error_log("Failed to export database");
            throw new Exception('فشل في تصدير قاعدة البيانات');
        }

        error_log("Database exported successfully");

        // Step 2: Create config info file
        $configInfo = [
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'db_name' => DB_NAME,
            'db_host' => DB_HOST,
            'db_user' => DB_USER,
            'server_info' => $_SERVER['SERVER_SOFTWARE'],
            'php_version' => PHP_VERSION
        ];

        file_put_contents($tempBackupDir . '/config_info.json', json_encode($configInfo, JSON_PRETTY_PRINT));

        // Step 3: Create a backup file (just copy the SQL file for now)
        error_log("Creating backup file: $backupPath");

        // Just copy the SQL file as the backup for now
        if (copy($dbBackupFile, $backupPath . '.sql')) {
            error_log("Backup file created successfully: " . $backupPath . '.sql');
            $backupFilename = $backupFilename . '.sql';
            $backupPath = $backupPath . '.sql';
        } else {
            error_log("Failed to create backup file");
            throw new Exception('فشل في إنشاء ملف النسخة الاحتياطية');
        }

        // Log activity
        logActivity(
            $_SESSION['user_id'],
            'إنشاء نسخة احتياطية كاملة',
            'backup',
            0,
            'تم إنشاء نسخة احتياطية كاملة: ' . $backupFilename
        );

        flash('success_message', 'تم إنشاء النسخة الاحتياطية الكاملة بنجاح. <a href="' . $backupPath . '" class="alert-link" download>انقر هنا لتنزيل النسخة الاحتياطية</a>', 'alert alert-success');

        // Clean up temporary files
        deleteDirectory($tempBackupDir);
    } catch (Exception $e) {
        error_log("Full Backup Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء إنشاء النسخة الاحتياطية الكاملة: ' . $e->getMessage(), 'alert alert-danger');

        // Clean up temporary files
        if (isset($tempBackupDir) && file_exists($tempBackupDir)) {
            deleteDirectory($tempBackupDir);
        }
    }
}

// Process delete backup request
if (isset($_POST['delete_backup']) && isset($_POST['backup_file'])) {
    try {
        $backupFile = $backupDir . '/' . basename($_POST['backup_file']);

        // Check if file exists and is within the backup directory
        if (file_exists($backupFile) && strpos($backupFile, $backupDir) === 0) {
            // Delete the file
            if (unlink($backupFile)) {
                // Log activity
                logActivity(
                    $_SESSION['user_id'],
                    'حذف نسخة احتياطية',
                    'backup',
                    0,
                    'تم حذف النسخة الاحتياطية: ' . basename($backupFile)
                );

                flash('success_message', 'تم حذف النسخة الاحتياطية بنجاح', 'alert alert-success');
            } else {
                throw new Exception('فشل في حذف ملف النسخة الاحتياطية');
            }
        } else {
            throw new Exception('ملف النسخة الاحتياطية غير موجود أو غير صالح');
        }
    } catch (Exception $e) {
        error_log("Delete Backup Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء حذف النسخة الاحتياطية: ' . $e->getMessage(), 'alert alert-danger');
    }
}

// Process restore request
if (isset($_POST['restore_backup']) && isset($_FILES['backup_file'])) {
    try {
        $uploadedFile = $_FILES['backup_file'];

        // Check for upload errors
        if ($uploadedFile['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('حدث خطأ أثناء رفع الملف. رمز الخطأ: ' . $uploadedFile['error']);
        }

        // Check file type
        $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));
        if ($fileExtension !== 'zip' && $fileExtension !== 'sql') {
            throw new Exception('نوع الملف غير صالح. يجب أن يكون الملف بتنسيق ZIP أو SQL.');
        }

        // Create a temporary directory for extraction
        $timestamp = date('Y-m-d_H-i-s');
        $extractDir = $backupDir . '/extract_' . $timestamp;
        if (!file_exists($extractDir)) {
            mkdir($extractDir, 0755, true);
        }

        // Process the backup file
        if ($fileExtension === 'zip') {
            // Extract the zip file
            if (!class_exists('ZipArchive')) {
                throw new Exception('مكتبة ZipArchive غير متوفرة. يرجى تثبيتها لاستخدام خاصية استعادة النسخة الاحتياطية.');
            }

            $zip = new ZipArchive();
            if ($zip->open($uploadedFile['tmp_name']) === TRUE) {
                $zip->extractTo($extractDir);
                $zip->close();

                // Check if database file exists
                if (file_exists($extractDir . '/database.sql')) {
                    // Restore database
                    restoreDatabase($extractDir . '/database.sql');
                } else {
                    throw new Exception('ملف قاعدة البيانات غير موجود في النسخة الاحتياطية');
                }
            } else {
                throw new Exception('حدث خطأ أثناء فك ضغط النسخة الاحتياطية');
            }
        } else if ($fileExtension === 'sql') {
            // Copy the SQL file to the extract directory
            $sqlFile = $extractDir . '/database.sql';
            if (move_uploaded_file($uploadedFile['tmp_name'], $sqlFile)) {
                // Restore database
                restoreDatabase($sqlFile);
            } else {
                throw new Exception('حدث خطأ أثناء نقل ملف النسخة الاحتياطية');
            }
        }

        // Log activity
        logActivity(
            $_SESSION['user_id'],
            'استعادة نسخة احتياطية كاملة',
            'backup',
            0,
            'تم استعادة النسخة الاحتياطية الكاملة: ' . $uploadedFile['name']
        );

        flash('success_message', 'تم استعادة النسخة الاحتياطية الكاملة بنجاح', 'alert alert-success');

        // Clean up extracted files
        deleteDirectory($extractDir);
    } catch (Exception $e) {
        error_log("Full Restore Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء استعادة النسخة الاحتياطية الكاملة: ' . $e->getMessage(), 'alert alert-danger');

        // Clean up extracted files
        if (isset($extractDir) && file_exists($extractDir)) {
            deleteDirectory($extractDir);
        }
    }
}

// Get list of backup files
$backupFiles = [];
if (file_exists($backupDir)) {
    // Get both ZIP and SQL backup files
    $backupPatterns = [
        $backupDir . '/full_backup_*.zip',
        $backupDir . '/full_backup_*.sql'
    ];

    foreach ($backupPatterns as $pattern) {
        foreach (glob($pattern) as $file) {
            $backupFiles[] = [
                'name' => basename($file),
                'size' => filesize($file),
                'date' => filemtime($file),
                'path' => $file
            ];
        }
    }

    // Sort by date (newest first)
    usort($backupFiles, function($a, $b) {
        return $b['date'] - $a['date'];
    });
}

/**
 * Export database to SQL file
 *
 * @param string $outputFile Output file path
 * @return bool Success status
 */
function exportDatabase($outputFile) {
    global $pdo;

    try {
        error_log("Starting database export to: $outputFile");

        // Start output buffering to capture the SQL
        ob_start();

        echo "-- نظام إدارة العلاوات والترفيع وكتب الشكر - نسخة احتياطية\n";
        echo "-- تاريخ النسخ الاحتياطي: " . date('Y-m-d H:i:s') . "\n\n";
        echo "SET FOREIGN_KEY_CHECKS=0;\n";
        echo "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
        echo "SET AUTOCOMMIT = 0;\n";
        echo "START TRANSACTION;\n";
        echo "SET time_zone = \"+00:00\";\n\n";

        // Get all tables
        $tables = [];
        $result = $pdo->query("SHOW TABLES");
        while ($row = $result->fetch(PDO::FETCH_NUM)) {
            $tables[] = $row[0];
        }

        // Export each table structure and data
        foreach ($tables as $table) {
            // Get table structure
            $result = $pdo->query("SHOW CREATE TABLE `$table`");
            $row = $result->fetch(PDO::FETCH_NUM);
            echo "DROP TABLE IF EXISTS `$table`;\n";
            echo $row[1] . ";\n\n";

            // Get table data
            $result = $pdo->query("SELECT * FROM `$table`");
            $rows = $result->fetchAll(PDO::FETCH_ASSOC);

            if (count($rows) > 0) {
                // Get column names
                $columns = array_keys($rows[0]);
                $columnList = '`' . implode('`, `', $columns) . '`';

                // Insert data in batches
                $batchSize = 100;
                $batchCount = ceil(count($rows) / $batchSize);

                for ($i = 0; $i < $batchCount; $i++) {
                    $batchRows = array_slice($rows, $i * $batchSize, $batchSize);

                    if (!empty($batchRows)) {
                        echo "INSERT INTO `$table` ($columnList) VALUES\n";

                        $rowCount = count($batchRows);
                        foreach ($batchRows as $j => $row) {
                            $values = [];
                            foreach ($row as $value) {
                                if ($value === null) {
                                    $values[] = 'NULL';
                                } else {
                                    $values[] = $pdo->quote($value);
                                }
                            }

                            echo "(" . implode(', ', $values) . ")";
                            if ($j < $rowCount - 1) {
                                echo ",\n";
                            } else {
                                echo ";\n\n";
                            }
                        }
                    }
                }
            }
        }

        echo "SET FOREIGN_KEY_CHECKS=1;\n";
        echo "COMMIT;\n";

        // Get the buffer contents and end buffering
        $sql = ob_get_clean();

        error_log("SQL generated successfully, size: " . strlen($sql) . " bytes");

        // Write to file
        $writeResult = file_put_contents($outputFile, $sql);

        if ($writeResult === false) {
            error_log("Failed to write SQL to file: $outputFile");
            return false;
        }

        error_log("SQL written to file successfully: $outputFile");
        return true;
    } catch (Exception $e) {
        error_log("Database Export Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Restore database from SQL file
 *
 * @param string $inputFile Input file path
 * @return bool Success status
 */
function restoreDatabase($inputFile) {
    global $pdo;

    try {
        // Read the SQL file
        $sql = file_get_contents($inputFile);

        // Make sure we're using UTF-8
        $pdo->exec("SET NAMES utf8mb4");
        $pdo->exec("SET CHARACTER SET utf8mb4");
        $pdo->exec("SET COLLATION_CONNECTION = utf8mb4_unicode_ci");

        // Disable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS=0");

        // Split SQL file into individual queries
        $queries = preg_split('/;\s*$/m', $sql);

        // Execute each query
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                $pdo->exec($query);
            }
        }

        // Enable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS=1");

        return true;
    } catch (Exception $e) {
        error_log("Database Restore Error: " . $e->getMessage());
        throw new Exception('حدث خطأ أثناء استعادة قاعدة البيانات: ' . $e->getMessage());
    }
}

/**
 * Format file size to human-readable format
 *
 * @param int $bytes File size in bytes
 * @return string Formatted file size
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $i = 0;
    while ($bytes >= 1024 && $i < count($units) - 1) {
        $bytes /= 1024;
        $i++;
    }
    return round($bytes, 2) . ' ' . $units[$i];
}

/**
 * Delete a directory and all its contents recursively
 *
 * @param string $dir Directory path
 * @return bool True on success, false on failure
 */
function deleteDirectory($dir) {
    if (!file_exists($dir)) {
        return true;
    }

    if (!is_dir($dir)) {
        return unlink($dir);
    }

    foreach (scandir($dir) as $item) {
        if ($item == '.' || $item == '..') {
            continue;
        }

        if (!deleteDirectory($dir . DIRECTORY_SEPARATOR . $item)) {
            return false;
        }
    }

    return rmdir($dir);
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-database me-2"></i> النسخ الاحتياطي الشامل
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى الرئيسية
        </a>
    </div>
</div>

<div class="row">
    <!-- Create Backup Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2"></i> إنشاء نسخة احتياطية كاملة
                </h5>
            </div>
            <div class="card-body">
                <p>قم بإنشاء نسخة احتياطية كاملة من قاعدة البيانات. يمكنك استخدام هذه النسخة لاستعادة البيانات في حالة حدوث مشكلة أو عند نقل البرنامج إلى حاسوب آخر.</p>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> تحتوي النسخة الاحتياطية الكاملة على قاعدة البيانات بالكامل. يمكنك استخدام هذه النسخة لاستعادة البيانات أو نقلها إلى حاسوب آخر.
                </div>

                <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" id="backupForm">
                    <button type="submit" name="create_full_backup" class="btn btn-primary w-100" id="createBackupBtn">
                        <i class="fas fa-download me-1"></i> إنشاء نسخة احتياطية كاملة
                    </button>
                </form>

                <!-- Progress bar (hidden by default) -->
                <div class="progress mt-3" id="backupProgress" style="display: none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary" role="progressbar" style="width: 0%"></div>
                </div>
                <small class="text-muted mt-2" id="backupStatus" style="display: none;">جاري إنشاء النسخة الاحتياطية...</small>
            </div>
        </div>
    </div>

    <!-- Restore Backup Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-upload me-2"></i> استعادة نسخة احتياطية
                </h5>
            </div>
            <div class="card-body">
                <p>قم باستعادة قاعدة البيانات من نسخة احتياطية سابقة. سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.</p>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> استعادة النسخة الاحتياطية ستؤدي إلى حذف جميع البيانات الحالية واستبدالها بالبيانات من النسخة الاحتياطية.
                </div>

                <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" enctype="multipart/form-data" id="restoreForm">
                    <div class="mb-3">
                        <label for="backup_file" class="form-label">اختر ملف النسخة الاحتياطية (SQL أو ZIP)</label>
                        <input type="file" class="form-control" id="backup_file" name="backup_file" accept=".sql,.zip" required>
                    </div>
                    <button type="submit" name="restore_backup" class="btn btn-warning w-100" id="restoreBackupBtn">
                        <i class="fas fa-upload me-1"></i> استعادة النسخة الاحتياطية
                    </button>
                </form>

                <!-- Progress bar (hidden by default) -->
                <div class="progress mt-3" id="restoreProgress" style="display: none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" role="progressbar" style="width: 0%"></div>
                </div>
                <small class="text-muted mt-2" id="restoreStatus" style="display: none;">جاري استعادة النسخة الاحتياطية...</small>
            </div>
        </div>
    </div>
</div>

<!-- Backup Files List -->
<div class="card mb-4">
    <div class="card-header bg-secondary text-white">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i> النسخ الاحتياطية المتوفرة
        </h5>
    </div>
    <div class="card-body">
        <?php if (count($backupFiles) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم الملف</th>
                            <th>تاريخ الإنشاء</th>
                            <th>حجم الملف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($backupFiles as $file): ?>
                            <tr>
                                <td><?php echo $file['name']; ?></td>
                                <td><?php echo date('Y-m-d H:i:s', $file['date']); ?></td>
                                <td><?php echo formatFileSize($file['size']); ?></td>
                                <td>
                                    <a href="<?php echo $file['path']; ?>" class="btn btn-sm btn-info" download data-bs-toggle="tooltip" title="تنزيل">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" class="d-inline" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذه النسخة الاحتياطية؟')">
                                        <input type="hidden" name="delete_backup" value="1">
                                        <input type="hidden" name="backup_file" value="<?php echo $file['name']; ?>">
                                        <button type="submit" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد نسخ احتياطية متوفرة.
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Backup and restore progress simulation
document.addEventListener('DOMContentLoaded', function() {
    // Backup form elements
    const backupForm = document.getElementById('backupForm');
    const createBackupBtn = document.getElementById('createBackupBtn');
    const backupProgress = document.getElementById('backupProgress');
    const backupProgressBar = backupProgress.querySelector('.progress-bar');
    const backupStatus = document.getElementById('backupStatus');

    // Restore form elements
    const restoreForm = document.getElementById('restoreForm');
    const restoreBackupBtn = document.getElementById('restoreBackupBtn');
    const restoreProgress = document.getElementById('restoreProgress');
    const restoreProgressBar = restoreProgress.querySelector('.progress-bar');
    const restoreStatus = document.getElementById('restoreStatus');

    // Backup form submission
    if (backupForm) {
        backupForm.addEventListener('submit', function(e) {
            // Show progress bar and disable button
            backupProgress.style.display = 'flex';
            backupStatus.style.display = 'block';
            createBackupBtn.disabled = true;
            createBackupBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري إنشاء النسخة الاحتياطية...';

            // Simulate progress
            let progress = 0;
            const interval = setInterval(function() {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;

                backupProgressBar.style.width = progress + '%';
                backupProgressBar.setAttribute('aria-valuenow', progress);

                if (progress < 30) {
                    backupStatus.textContent = 'جاري استخراج هيكل الجداول...';
                } else if (progress < 60) {
                    backupStatus.textContent = 'جاري استخراج البيانات من الجداول...';
                } else if (progress < 90) {
                    backupStatus.textContent = 'جاري إنشاء ملف النسخة الاحتياطية...';
                } else {
                    backupStatus.textContent = 'جاري حفظ النسخة الاحتياطية...';
                }

                if (progress === 100) {
                    clearInterval(interval);
                    // Form will submit normally and page will refresh
                }
            }, 500);
        });
    }

    // Restore form submission
    if (restoreForm) {
        restoreForm.addEventListener('submit', function(e) {
            // Show confirmation dialog
            if (!confirm('هل أنت متأكد من رغبتك في استعادة هذه النسخة الاحتياطية؟ سيتم حذف جميع البيانات الحالية.')) {
                e.preventDefault();
                return false;
            }

            // Show progress bar and disable button
            restoreProgress.style.display = 'flex';
            restoreStatus.style.display = 'block';
            restoreBackupBtn.disabled = true;
            restoreBackupBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري استعادة النسخة الاحتياطية...';

            // Simulate progress
            let progress = 0;
            const interval = setInterval(function() {
                progress += Math.random() * 10;
                if (progress > 100) progress = 100;

                restoreProgressBar.style.width = progress + '%';
                restoreProgressBar.setAttribute('aria-valuenow', progress);

                if (progress < 30) {
                    restoreStatus.textContent = 'جاري قراءة ملف النسخة الاحتياطية...';
                } else if (progress < 60) {
                    restoreStatus.textContent = 'جاري إعادة إنشاء هيكل الجداول...';
                } else if (progress < 90) {
                    restoreStatus.textContent = 'جاري استيراد البيانات...';
                } else {
                    restoreStatus.textContent = 'جاري إكمال عملية الاستعادة...';
                }

                if (progress === 100) {
                    clearInterval(interval);
                    // Form will submit normally and page will refresh
                }
            }, 500);
        });
    }

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

/**
 * Format file size to human-readable format
 *
 * @param int $bytes File size in bytes
 * @return string Formatted file size
 */
function formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let i = 0;
    while (bytes >= 1024 && i < units.length - 1) {
        bytes /= 1024;
        i++;
    }
    return Math.round(bytes * 100) / 100 + ' ' + units[i];
}
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
