<?php
/**
 * Allowances List Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();

// Get filter parameters
$status = isset($_GET['status']) ? sanitize($_GET['status']) : 'all';
$department = isset($_GET['department']) ? (int)$_GET['department'] : 0;
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build query
$query = "
    SELECT e.*, d.name as department_name
    FROM employees e
    JOIN departments d ON e.department_id = d.id
    WHERE 1=1
";

$params = [];

if ($status === 'eligible') {
    $query .= " AND e.allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . " AND e.next_allowance_date <= CURDATE()";
} elseif ($status === 'upcoming') {
    $query .= " AND e.allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . " AND e.next_allowance_date > CURDATE() AND e.next_allowance_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)";
} elseif ($status === 'maxed') {
    $query .= " AND e.allowances_in_current_grade >= " . ALLOWANCES_PER_GRADE;
}

if ($department > 0) {
    $query .= " AND e.department_id = :department";
    $params[':department'] = $department;
}

if (!empty($search)) {
    $query .= " AND (e.full_name LIKE :search OR e.employee_number LIKE :search)";
    $params[':search'] = "%$search%";
}

$query .= " ORDER BY e.next_allowance_date ASC, e.full_name ASC";

// Get departments for filter
try {
    $deptStmt = $pdo->query("SELECT id, name FROM departments ORDER BY name ASC");
    $departments = $deptStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Departments Error: " . $e->getMessage());
    $departments = [];
}

// Get employees
try {
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $employees = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Employees Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الموظفين', 'alert alert-danger');
    $employees = [];
}

// Get statistics
try {
    // Eligible for allowance
    $eligibleStmt = $pdo->query("
        SELECT COUNT(*) as count FROM employees 
        WHERE allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
        AND next_allowance_date <= CURDATE()
    ");
    $eligibleCount = $eligibleStmt->fetch()['count'];
    
    // Upcoming allowances
    $upcomingStmt = $pdo->query("
        SELECT COUNT(*) as count FROM employees 
        WHERE allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
        AND next_allowance_date > CURDATE() 
        AND next_allowance_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)
    ");
    $upcomingCount = $upcomingStmt->fetch()['count'];
    
    // Maxed out allowances
    $maxedStmt = $pdo->query("
        SELECT COUNT(*) as count FROM employees 
        WHERE allowances_in_current_grade >= " . ALLOWANCES_PER_GRADE . "
    ");
    $maxedCount = $maxedStmt->fetch()['count'];
    
} catch (PDOException $e) {
    error_log("Get Statistics Error: " . $e->getMessage());
    $eligibleCount = $upcomingCount = $maxedCount = 0;
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-money-bill-wave me-2"></i> إدارة العلاوات
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <button class="btn btn-outline-secondary btn-print">
            <i class="fas fa-print me-1"></i> طباعة
        </button>
        <button class="btn btn-outline-success btn-export" data-table-target="#allowancesTable" data-filename="allowances">
            <i class="fas fa-file-excel me-1"></i> تصدير
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card dashboard-card">
            <div class="icon text-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="count"><?php echo $eligibleCount; ?></div>
            <div class="title">مستحقي العلاوة حالياً</div>
            <a href="?status=eligible" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card dashboard-card">
            <div class="icon text-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="count"><?php echo $upcomingCount; ?></div>
            <div class="title">مستحقي العلاوة خلال 30 يوم</div>
            <a href="?status=upcoming" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card dashboard-card">
            <div class="icon text-danger">
                <i class="fas fa-ban"></i>
            </div>
            <div class="count"><?php echo $maxedCount; ?></div>
            <div class="title">اكتملت علاواتهم</div>
            <a href="?status=maxed" class="stretched-link"></a>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="get" class="row g-3">
            <div class="col-md-3">
                <select class="form-select" name="status">
                    <option value="all" <?php echo ($status === 'all') ? 'selected' : ''; ?>>جميع الموظفين</option>
                    <option value="eligible" <?php echo ($status === 'eligible') ? 'selected' : ''; ?>>المستحقين حالياً</option>
                    <option value="upcoming" <?php echo ($status === 'upcoming') ? 'selected' : ''; ?>>المستحقين خلال 30 يوم</option>
                    <option value="maxed" <?php echo ($status === 'maxed') ? 'selected' : ''; ?>>اكتملت علاواتهم</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="department">
                    <option value="0">جميع الأقسام</option>
                    <?php foreach ($departments as $dept): ?>
                        <option value="<?php echo $dept['id']; ?>" <?php echo ($department == $dept['id']) ? 'selected' : ''; ?>>
                            <?php echo $dept['name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" name="search" placeholder="بحث بالاسم أو الرقم الوظيفي" value="<?php echo $search; ?>">
                </div>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">تطبيق</button>
            </div>
        </form>
    </div>
</div>

<!-- Employees Table -->
<div class="card">
    <div class="card-body">
        <?php if (count($employees) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover" id="allowancesTable">
                    <thead>
                        <tr>
                            <th>الرقم الوظيفي</th>
                            <th>الاسم</th>
                            <th>الدرجة</th>
                            <th>القسم</th>
                            <th>العلاوات المستلمة</th>
                            <th>تاريخ آخر علاوة</th>
                            <th>تاريخ العلاوة القادمة</th>
                            <th>الحالة</th>
                            <th class="no-print">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($employees as $employee): ?>
                            <tr>
                                <td><?php echo $employee['employee_number']; ?></td>
                                <td><?php echo $employee['full_name']; ?></td>
                                <td><?php echo $employee['current_grade']; ?></td>
                                <td><?php echo $employee['department_name']; ?></td>
                                <td><?php echo $employee['allowances_in_current_grade']; ?> من <?php echo ALLOWANCES_PER_GRADE; ?></td>
                                <td><?php echo $employee['last_allowance_date'] ? formatArabicDate($employee['last_allowance_date']) : '-'; ?></td>
                                <td><?php echo formatArabicDate($employee['next_allowance_date']); ?></td>
                                <td>
                                    <?php if ($employee['allowances_in_current_grade'] >= ALLOWANCES_PER_GRADE): ?>
                                        <span class="badge bg-danger">اكتملت العلاوات</span>
                                    <?php elseif (isEligibleForAllowance($employee)): ?>
                                        <span class="badge bg-success">مستحق</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                    <?php endif; ?>
                                </td>
                                <td class="no-print">
                                    <a href="employee_details.php?id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if (hasRole(['admin', 'hr']) && $employee['allowances_in_current_grade'] < ALLOWANCES_PER_GRADE): ?>
                                        <a href="add_allowance.php?employee_id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="إضافة علاوة">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد بيانات للعرض
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
