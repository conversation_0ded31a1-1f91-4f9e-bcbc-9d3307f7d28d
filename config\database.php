<?php
/**
 * Database Configuration
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Database credentials
define('DB_HOST', 'localhost');
define('DB_NAME', 'employee_promotions_db');
define('DB_USER', 'root');
define('DB_PASS', ''); // تم تعديل كلمة المرور بناءً على نتائج الاختبار
define('DB_CHARSET', 'utf8mb4');

// PDO connection options
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

// Create PDO instance
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        $options
    );

    // Ensure UTF-8 is used for all database connections
    $pdo->exec("SET NAMES utf8mb4");
    $pdo->exec("SET CHARACTER SET utf8mb4");
    $pdo->exec("SET COLLATION_CONNECTION = utf8mb4_unicode_ci");
} catch (PDOException $e) {
    // Log error and display user-friendly message
    error_log("Database Connection Error: " . $e->getMessage());
    die("خطأ في الاتصال بقاعدة البيانات. يرجى الاتصال بمسؤول النظام.");
}
