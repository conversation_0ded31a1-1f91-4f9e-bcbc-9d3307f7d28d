-- ملف إن<PERSON><PERSON><PERSON> قاعدة البيانات الأولية
-- نظام إدارة العلاوات والترقية وكتب الشكر

-- إن<PERSON><PERSON><PERSON> جدول الأقسام
CREATE TABLE IF NOT EXISTS `departments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إ<PERSON><PERSON><PERSON><PERSON> جدول المستويات التعليمية
CREATE TABLE IF NOT EXISTS `education_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `max_grade` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول الموظفين
CREATE TABLE IF NOT EXISTS `employees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_number` varchar(20) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `department_id` int(11) NOT NULL,
  `education_level_id` int(11) NOT NULL,
  `hire_date` date NOT NULL,
  `current_grade` int(11) NOT NULL,
  `years_of_service` int(11) NOT NULL,
  `allowances_in_current_grade` int(11) NOT NULL DEFAULT 0,
  `last_allowance_date` date DEFAULT NULL,
  `next_allowance_date` date NOT NULL,
  `last_promotion_date` date DEFAULT NULL,
  `next_promotion_date` date NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `employee_number` (`employee_number`),
  KEY `department_id` (`department_id`),
  KEY `education_level_id` (`education_level_id`),
  CONSTRAINT `employees_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`),
  CONSTRAINT `employees_ibfk_2` FOREIGN KEY (`education_level_id`) REFERENCES `education_levels` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول العلاوات
CREATE TABLE IF NOT EXISTS `allowances` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `allowance_date` date NOT NULL,
  `allowance_number` int(11) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `allowances_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `allowances_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول الترقيات
CREATE TABLE IF NOT EXISTS `promotions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `promotion_date` date NOT NULL,
  `old_grade` int(11) NOT NULL,
  `new_grade` int(11) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `promotions_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `promotions_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول كتب الشكر
CREATE TABLE IF NOT EXISTS `appreciation_letters` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `letter_date` date NOT NULL,
  `letter_number` varchar(50) NOT NULL,
  `issuer` enum('regular','prime_minister') NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `appreciation_letters_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `appreciation_letters_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول الإشعارات
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` enum('allowance','promotion','appreciation','employee','system') NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `link` varchar(255) DEFAULT NULL,
  `read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدخال بيانات الأقسام
INSERT INTO `departments` (`name`, `description`) VALUES
('قسم الموارد البشرية', 'إدارة شؤون الموظفين والتوظيف والتدريب'),
('قسم تكنولوجيا المعلومات', 'إدارة أنظمة المعلومات والبنية التحتية التقنية'),
('قسم المالية', 'إدارة الشؤون المالية والمحاسبية'),
('قسم الإدارة', 'الإدارة العامة والشؤون الإدارية');

-- إدخال بيانات المستويات التعليمية
INSERT INTO `education_levels` (`name`, `description`, `max_grade`) VALUES
('دكتوراه', 'شهادة الدكتوراه', 1),
('ماجستير', 'شهادة الماجستير', 2),
('بكالوريوس', 'شهادة البكالوريوس', 4),
('دبلوم', 'شهادة الدبلوم', 6),
('ثانوية عامة', 'شهادة الثانوية العامة', 8);

-- إدخال بيانات إعدادات النظام
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('allowances_per_grade', '11', 'عدد العلاوات في كل درجة'),
('promotion_years_lower_grades', '4', 'عدد سنوات الترقية للدرجات الدنيا (10-5)'),
('promotion_years_upper_grades', '5', 'عدد سنوات الترقية للدرجات العليا (5-1)'),
('regular_letter_months', '1', 'عدد أشهر تخفيض كتاب الشكر العادي'),
('pm_letter_months', '6', 'عدد أشهر تخفيض كتاب شكر رئيس الوزراء'),
('enable_notifications', '1', 'تفعيل نظام الإشعارات'),
('enable_reminders', '1', 'تفعيل نظام التذكيرات'),
('reminder_days', '30', 'عدد أيام التذكير المسبق');

-- إدخال بيانات الموظفين
INSERT INTO `employees` (`employee_number`, `full_name`, `department_id`, `education_level_id`, `hire_date`, `current_grade`, `years_of_service`, `allowances_in_current_grade`, `last_allowance_date`, `next_allowance_date`, `last_promotion_date`, `next_promotion_date`) VALUES
('1001', 'أحمد محمد علي', 1, 3, '2015-01-15', 5, 8, 3, '2022-01-15', '2023-01-15', '2019-01-15', '2023-01-15'),
('1002', 'سارة أحمد حسين', 1, 3, '2016-03-20', 6, 7, 5, '2022-03-20', '2023-03-20', '2020-03-20', '2024-03-20'),
('1003', 'محمد عبدالله محمود', 2, 2, '2014-05-10', 3, 9, 2, '2022-05-10', '2023-05-10', '2019-05-10', '2024-05-10'),
('1004', 'فاطمة علي حسن', 2, 3, '2017-07-05', 6, 6, 4, '2022-07-05', '2023-07-05', '2021-07-05', '2025-07-05'),
('1005', 'خالد محمد سعيد', 3, 3, '2018-02-25', 7, 5, 3, '2022-02-25', '2023-02-25', '2022-02-25', '2026-02-25'),
('1006', 'نور حسين أحمد', 3, 4, '2019-09-12', 8, 4, 2, '2022-09-12', '2023-09-12', '2022-09-12', '2026-09-12'),
('1007', 'عمر فاروق محمد', 4, 2, '2013-11-30', 3, 10, 6, '2022-11-30', '2023-11-30', '2018-11-30', '2023-11-30'),
('1008', 'ليلى كريم سالم', 4, 3, '2016-08-15', 5, 7, 4, '2022-08-15', '2023-08-15', '2020-08-15', '2024-08-15'),
('1009', 'يوسف طارق أحمد', 1, 4, '2020-04-01', 8, 3, 1, '2022-04-01', '2023-04-01', '2022-04-01', '2026-04-01'),
('1010', 'رنا سمير محمود', 2, 1, '2012-06-20', 2, 11, 5, '2022-06-20', '2023-06-20', '2017-06-20', '2022-06-20');

-- إدخال بيانات العلاوات
INSERT INTO `allowances` (`employee_id`, `allowance_date`, `allowance_number`, `notes`, `created_by`) VALUES
(1, '2022-01-15', 3, 'العلاوة السنوية', 1),
(2, '2022-03-20', 5, 'العلاوة السنوية', 1),
(3, '2022-05-10', 2, 'العلاوة السنوية', 1),
(4, '2022-07-05', 4, 'العلاوة السنوية', 1),
(5, '2022-02-25', 3, 'العلاوة السنوية', 1),
(6, '2022-09-12', 2, 'العلاوة السنوية', 1),
(7, '2022-11-30', 6, 'العلاوة السنوية', 1),
(8, '2022-08-15', 4, 'العلاوة السنوية', 1),
(9, '2022-04-01', 1, 'العلاوة السنوية', 1),
(10, '2022-06-20', 5, 'العلاوة السنوية', 1),
(1, '2021-01-15', 2, 'العلاوة السنوية', 1),
(2, '2021-03-20', 4, 'العلاوة السنوية', 1),
(3, '2021-05-10', 1, 'العلاوة السنوية', 1),
(4, '2021-07-05', 3, 'العلاوة السنوية', 1),
(5, '2021-02-25', 2, 'العلاوة السنوية', 1);

-- إدخال بيانات الترقيات
INSERT INTO `promotions` (`employee_id`, `promotion_date`, `old_grade`, `new_grade`, `notes`, `created_by`) VALUES
(1, '2019-01-15', 6, 5, 'ترقية وظيفية', 1),
(2, '2020-03-20', 7, 6, 'ترقية وظيفية', 1),
(3, '2019-05-10', 4, 3, 'ترقية وظيفية', 1),
(4, '2021-07-05', 7, 6, 'ترقية وظيفية', 1),
(5, '2022-02-25', 8, 7, 'ترقية وظيفية', 1),
(6, '2022-09-12', 9, 8, 'ترقية وظيفية', 1),
(7, '2018-11-30', 4, 3, 'ترقية وظيفية', 1),
(8, '2020-08-15', 6, 5, 'ترقية وظيفية', 1),
(9, '2022-04-01', 9, 8, 'ترقية وظيفية', 1),
(10, '2017-06-20', 3, 2, 'ترقية وظيفية', 1);

-- إدخال بيانات كتب الشكر
INSERT INTO `appreciation_letters` (`employee_id`, `letter_date`, `letter_number`, `issuer`, `notes`, `created_by`) VALUES
(1, '2022-06-15', 'A-2022-001', 'regular', 'كتاب شكر للأداء المتميز', 1),
(3, '2022-07-20', 'A-2022-002', 'regular', 'كتاب شكر للأداء المتميز', 1),
(5, '2022-08-10', 'A-2022-003', 'regular', 'كتاب شكر للأداء المتميز', 1),
(7, '2022-09-05', 'A-2022-004', 'regular', 'كتاب شكر للأداء المتميز', 1),
(9, '2022-10-15', 'A-2022-005', 'regular', 'كتاب شكر للأداء المتميز', 1),
(2, '2022-05-25', 'PM-2022-001', 'prime_minister', 'كتاب شكر من رئيس الوزراء للإنجاز المتميز', 1),
(4, '2022-07-30', 'PM-2022-002', 'prime_minister', 'كتاب شكر من رئيس الوزراء للإنجاز المتميز', 1),
(6, '2022-09-20', 'PM-2022-003', 'prime_minister', 'كتاب شكر من رئيس الوزراء للإنجاز المتميز', 1),
(8, '2022-11-10', 'PM-2022-004', 'prime_minister', 'كتاب شكر من رئيس الوزراء للإنجاز المتميز', 1),
(10, '2022-12-05', 'PM-2022-005', 'prime_minister', 'كتاب شكر من رئيس الوزراء للإنجاز المتميز', 1);
