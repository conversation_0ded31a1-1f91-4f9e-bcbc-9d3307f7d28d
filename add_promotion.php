<?php
/**
 * Add Promotion Page
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and proper role
requireRole(['admin', 'hr']);

// Check if employee ID is provided
if (!isset($_GET['employee_id']) || empty($_GET['employee_id'])) {
    flash('error_message', 'معرف الموظف غير صالح', 'alert alert-danger');
    redirect('promotions.php');
}

$employeeId = (int)$_GET['employee_id'];

// Get employee details
try {
    $stmt = $pdo->prepare("
        SELECT e.*, d.name as department_name, el.name as education_level_name, el.max_grade
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        JOIN education_levels el ON e.education_level_id = el.id
        WHERE e.id = :id
    ");
    $stmt->execute([':id' => $employeeId]);
    $employee = $stmt->fetch();

    if (!$employee) {
        flash('error_message', 'الموظف غير موجود', 'alert alert-danger');
        redirect('promotions.php');
    }

    // Check if employee is eligible for promotion
    if (!isEligibleForPromotion($employee)) {
        flash('error_message', 'الموظف غير مستحق للترفيع حالياً', 'alert alert-danger');
        redirect('employee_details.php?id=' . $employeeId);
    }

    // Check if employee has reached maximum grade for education level
    if ($employee['current_grade'] <= $employee['max_grade']) {
        flash('error_message', 'لقد وصل الموظف إلى الحد الأقصى للدرجة حسب تحصيله الدراسي', 'alert alert-danger');
        redirect('employee_details.php?id=' . $employeeId);
    }

} catch (PDOException $e) {
    error_log("Get Employee Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الموظف', 'alert alert-danger');
    redirect('promotions.php');
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $promotionDate = sanitize($_POST['promotion_date']);
    $yearsInPreviousGrade = (int)$_POST['years_in_previous_grade'];
    $notes = sanitize($_POST['notes']);

    // Validate inputs
    $errors = [];

    if (empty($promotionDate)) {
        $errors[] = 'تاريخ الترقية مطلوب';
    }

    if ($yearsInPreviousGrade <= 0) {
        $errors[] = 'سنوات الخدمة في الدرجة السابقة يجب أن تكون أكبر من صفر';
    }

    // If no errors, add promotion
    if (empty($errors)) {
        try {
            // Start transaction
            $pdo->beginTransaction();

            // Calculate new grade
            $fromGrade = $employee['current_grade'];
            $toGrade = $fromGrade - 1;

            // Reset stage to 1 for the new grade and get new nominal salary
            $newStage = 1;
            $nominalSalary = getNominalSalary($toGrade, $newStage);

            // Insert promotion record
            $stmt = $pdo->prepare("
                INSERT INTO promotions (
                    employee_id, promotion_date, from_grade, to_grade, years_in_previous_grade, notes, created_by
                ) VALUES (
                    :employee_id, :promotion_date, :from_grade, :to_grade, :years_in_previous_grade, :notes, :created_by
                )
            ");

            $stmt->execute([
                ':employee_id' => $employeeId,
                ':promotion_date' => $promotionDate,
                ':from_grade' => $fromGrade,
                ':to_grade' => $toGrade,
                ':years_in_previous_grade' => $yearsInPreviousGrade,
                ':notes' => $notes,
                ':created_by' => $_SESSION['user_id']
            ]);

            // Add salary history record
            addSalaryHistory(
                $employeeId,
                $toGrade,
                $newStage,
                $nominalSalary,
                $promotionDate,
                'promotion',
                'ترقية من الدرجة ' . $fromGrade . ' إلى الدرجة ' . $toGrade
            );

            // Update employee record
            $nextPromotionDate = calculateNextPromotionDate($promotionDate, $toGrade);

            $stmt = $pdo->prepare("
                UPDATE employees SET
                    current_grade = :current_grade,
                    current_stage = :current_stage,
                    nominal_salary = :nominal_salary,
                    years_in_current_grade = 0,
                    allowances_in_current_grade = 0,
                    last_promotion_date = :last_promotion_date,
                    next_promotion_date = :next_promotion_date
                WHERE id = :id
            ");

            $stmt->execute([
                ':current_grade' => $toGrade,
                ':current_stage' => $newStage,
                ':nominal_salary' => $nominalSalary,
                ':last_promotion_date' => $promotionDate,
                ':next_promotion_date' => $nextPromotionDate,
                ':id' => $employeeId
            ]);

            // Log activity
            logActivity(
                $_SESSION['user_id'],
                'إضافة ترفيع',
                'promotions',
                $pdo->lastInsertId(),
                'تمت إضافة ترفيع للموظف: ' . $employee['full_name'] . ' من الدرجة ' . $fromGrade . ' إلى الدرجة ' . $toGrade
            );

            // Commit transaction
            $pdo->commit();

            // Redirect to employee details
            flash('success_message', 'تمت إضافة الترفيع بنجاح', 'alert alert-success');
            redirect('employee_details.php?id=' . $employeeId);

        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();

            error_log("Add Promotion Error: " . $e->getMessage());
            flash('error_message', 'حدث خطأ أثناء إضافة الترفيع', 'alert alert-danger');
        }
    } else {
        // Display errors
        $errorMessage = '<ul class="mb-0">';
        foreach ($errors as $error) {
            $errorMessage .= '<li>' . $error . '</li>';
        }
        $errorMessage .= '</ul>';

        flash('error_message', $errorMessage, 'alert alert-danger');
    }
}

// Set default promotion date to today
$defaultPromotionDate = date('Y-m-d');
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-level-up-alt me-2"></i> إضافة ترفيع
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="employee_details.php?id=<?php echo $employeeId; ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى تفاصيل الموظف
        </a>
    </div>
</div>

<!-- Employee Information Card -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-user me-2"></i> معلومات الموظف
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الرقم الوظيفي:</div>
                <div><?php echo $employee['employee_number']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الاسم:</div>
                <div><?php echo $employee['full_name']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">القسم:</div>
                <div><?php echo $employee['department_name']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الدرجة الحالية:</div>
                <div>الدرجة <?php echo $employee['current_grade']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">التحصيل الدراسي:</div>
                <div><?php echo $employee['education_level_name']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الحد الأقصى للدرجة:</div>
                <div>الدرجة <?php echo $employee['max_grade']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">سنوات الخدمة في الدرجة الحالية:</div>
                <div><?php echo $employee['years_in_current_grade']; ?> سنة</div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">تاريخ آخر ترفيع:</div>
                <div><?php echo $employee['last_promotion_date'] ? formatArabicDate($employee['last_promotion_date']) : 'لا يوجد'; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">تاريخ الترفيع المستحق:</div>
                <div><?php echo formatArabicDate($employee['next_promotion_date']); ?></div>
            </div>
        </div>
    </div>
</div>

<!-- Add Promotion Form -->
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-plus-circle me-2"></i> إضافة ترقية جديدة
        </h5>
    </div>
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF'] . '?employee_id=' . $employeeId; ?>" method="post" class="needs-validation" novalidate>
            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="promotion_date" class="form-label">تاريخ الترقية <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="promotion_date" name="promotion_date" value="<?php echo $defaultPromotionDate; ?>" required>
                    <div class="invalid-feedback">يرجى إدخال تاريخ الترقية</div>
                </div>
                <div class="col-md-4">
                    <label for="from_grade" class="form-label">من الدرجة</label>
                    <input type="text" class="form-control" id="from_grade" value="<?php echo $employee['current_grade']; ?>" readonly>
                </div>
                <div class="col-md-4">
                    <label for="to_grade" class="form-label">إلى الدرجة</label>
                    <input type="text" class="form-control" id="to_grade" value="<?php echo $employee['current_grade'] - 1; ?>" readonly>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="years_in_previous_grade" class="form-label">سنوات الخدمة في الدرجة السابقة <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="years_in_previous_grade" name="years_in_previous_grade" min="1" value="<?php echo $employee['years_in_current_grade']; ?>" required>
                    <div class="invalid-feedback">يرجى إدخال سنوات الخدمة في الدرجة السابقة</div>
                </div>
                <div class="col-md-6">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                </div>
            </div>

            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                سيتم تحديث بيانات الموظف تلقائياً بعد إضافة الترقية، بما في ذلك:
                <ul class="mb-0 mt-2">
                    <li>تحديث الدرجة الحالية</li>
                    <li>تصفير عدد سنوات الخدمة في الدرجة الحالية</li>
                    <li>تصفير عدد العلاوات المستلمة في الدرجة الحالية</li>
                    <li>تحديث تاريخ آخر ترقية وتاريخ الترقية القادم</li>
                </ul>
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary px-5">
                    <i class="fas fa-save me-1"></i> حفظ الترقية
                </button>
                <a href="employee_details.php?id=<?php echo $employeeId; ?>" class="btn btn-secondary px-5">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
