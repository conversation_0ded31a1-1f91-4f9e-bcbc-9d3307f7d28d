@echo off
echo ======================================================
echo    نظام إدارة العلاوات والترقية وكتب الشكر
echo ======================================================
echo.
echo جاري تشغيل خدمات النظام...
echo.

REM تحديد المسار الحالي
set CURRENT_DIR=%~dp0
set XAMPP_DIR=%CURRENT_DIR%xampp

REM التحقق من وجود مجلد xampp
if not exist "%XAMPP_DIR%" (
    echo خطأ: مجلد xampp غير موجود في المسار %CURRENT_DIR%
    echo يرجى التأكد من وجود XAMPP في المسار الصحيح.
    echo.
    pause
    exit /b 1
)

REM تشغيل خدمات Apache و MySQL
cd /d "%XAMPP_DIR%"
echo جاري تشغيل خدمة Apache...
start /b apache_start.bat
echo جاري تشغيل خدمة MySQL...
start /b mysql_start.bat

echo.
echo جاري انتظار بدء تشغيل الخدمات...
timeout /t 5 /nobreak > nul

REM التحقق من تشغيل الخدمات
echo.
echo التحقق من حالة الخدمات:
echo ------------------------

REM التحقق من خدمة Apache
netstat -an | find ":80 " > nul
if %ERRORLEVEL% == 0 (
    echo Apache: تعمل
) else (
    echo Apache: لا تعمل - محاولة إعادة التشغيل...
    start /b apache_start.bat
    timeout /t 3 /nobreak > nul
)

REM التحقق من خدمة MySQL
netstat -an | find ":3306 " > nul
if %ERRORLEVEL% == 0 (
    echo MySQL: تعمل
) else (
    echo MySQL: لا تعمل - محاولة إعادة التشغيل...
    start /b mysql_start.bat
    timeout /t 3 /nobreak > nul
)

echo.
echo ======================================================
echo تم تشغيل خدمات النظام بنجاح!
echo.
echo جاري فتح النظام في المتصفح...
start http://localhost/ترقيات/
echo.
echo ملاحظات هامة:
echo - إذا لم يفتح المتصفح تلقائيًا، افتح المتصفح يدويًا وانتقل إلى:
echo   http://localhost/ترقيات/
echo.
echo - إذا كانت هذه هي المرة الأولى لتشغيل النظام، قم بزيارة:
echo   http://localhost/ترقيات/create_database.php
echo   لإنشاء قاعدة البيانات.
echo.
echo - لإيقاف تشغيل النظام، استخدم ملف stop_system.bat
echo ======================================================
echo.
