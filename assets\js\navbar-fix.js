/**
 * Navbar Dropdown Fix
 * إصلاح وظائف القوائم المنسدلة في شريط التنقل
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Navbar Fix Script Loaded');

    // إصلاح القوائم المنسدلة
    initializeDropdowns();

    // إصلاح قائمة الإدارة
    fixAdminDropdown();

    // إصلاح قائمة المستخدم
    fixUserDropdown();

    // إصلاح قائمة الإشعارات
    fixNotificationsDropdown();
});

/**
 * تهيئة جميع القوائم المنسدلة
 */
function initializeDropdowns() {
    // البحث عن جميع عناصر القوائم المنسدلة
    const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');

    dropdownToggles.forEach(function(toggle) {
        // إضافة مستمع الأحداث للنقر
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const dropdownMenu = this.nextElementSibling;
            if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
                toggleDropdown(dropdownMenu);
            }
        });

        // إضافة مستمع الأحداث للتمرير
        toggle.addEventListener('mouseenter', function() {
            const dropdownMenu = this.nextElementSibling;
            if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
                showDropdown(dropdownMenu);
            }
        });
    });

    // إغلاق القوائم عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            hideAllDropdowns();
        }
    });
}

/**
 * إصلاح قائمة الإدارة المنسدلة
 */
function fixAdminDropdown() {
    const adminDropdown = document.getElementById('adminDropdown');
    if (adminDropdown) {
        console.log('Admin dropdown found');

        adminDropdown.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const dropdownMenu = this.parentElement.querySelector('.dropdown-menu');
            if (dropdownMenu) {
                console.log('Admin dropdown menu found');
                toggleDropdown(dropdownMenu);
            }
        });

        // إضافة تأثير التمرير
        adminDropdown.addEventListener('mouseenter', function() {
            const dropdownMenu = this.parentElement.querySelector('.dropdown-menu');
            if (dropdownMenu) {
                showDropdown(dropdownMenu);
            }
        });

        // إخفاء القائمة عند مغادرة المنطقة
        const adminDropdownParent = adminDropdown.closest('.dropdown');
        if (adminDropdownParent) {
            adminDropdownParent.addEventListener('mouseleave', function() {
                const dropdownMenu = this.querySelector('.dropdown-menu');
                if (dropdownMenu) {
                    setTimeout(() => {
                        if (!dropdownMenu.matches(':hover') && !adminDropdown.matches(':hover')) {
                            hideDropdown(dropdownMenu);
                        }
                    }, 300);
                }
            });
        }
    }
}

/**
 * إصلاح قائمة المستخدم المنسدلة
 */
function fixUserDropdown() {
    const userDropdown = document.getElementById('userDropdown');
    if (userDropdown) {
        console.log('User dropdown found');

        userDropdown.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const dropdownMenu = this.parentElement.querySelector('.dropdown-menu');
            if (dropdownMenu) {
                console.log('User dropdown menu found');
                toggleDropdown(dropdownMenu);
            }
        });
    }
}

/**
 * إصلاح قائمة الإشعارات
 */
function fixNotificationsDropdown() {
    const notificationsDropdown = document.getElementById('notificationsDropdown');
    if (notificationsDropdown) {
        console.log('Notifications dropdown found');

        notificationsDropdown.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const notificationsContainer = document.getElementById('notificationsContainer');
            if (notificationsContainer) {
                console.log('Notifications container found');
                toggleDropdown(notificationsContainer);
            }
        });
    }
}

/**
 * تبديل حالة القائمة المنسدلة
 */
function toggleDropdown(dropdownMenu) {
    if (dropdownMenu.classList.contains('show')) {
        hideDropdown(dropdownMenu);
    } else {
        hideAllDropdowns();
        showDropdown(dropdownMenu);
    }
}

/**
 * إظهار القائمة المنسدلة
 */
function showDropdown(dropdownMenu) {
    // إخفاء جميع القوائم الأخرى أولاً
    hideAllDropdowns();

    // تأكيد الموضع الصحيح
    dropdownMenu.style.position = 'absolute';
    dropdownMenu.style.top = 'calc(100% + 0.5rem)';
    dropdownMenu.style.zIndex = '9999';
    dropdownMenu.style.transform = 'none';

    // إظهار القائمة المحددة
    dropdownMenu.classList.add('show');
    dropdownMenu.style.display = 'block';
    dropdownMenu.style.opacity = '0';
    dropdownMenu.style.visibility = 'visible';

    // تأثير الظهور التدريجي
    setTimeout(() => {
        dropdownMenu.style.transition = 'all 0.3s ease';
        dropdownMenu.style.opacity = '1';
    }, 10);

    console.log('Dropdown shown');
}

/**
 * إخفاء القائمة المنسدلة
 */
function hideDropdown(dropdownMenu) {
    dropdownMenu.style.transition = 'all 0.3s ease';
    dropdownMenu.style.opacity = '0';
    dropdownMenu.style.transform = 'translateY(-10px)';

    setTimeout(() => {
        dropdownMenu.classList.remove('show');
        dropdownMenu.style.display = 'none';
    }, 300);

    console.log('Dropdown hidden');
}

/**
 * إخفاء جميع القوائم المنسدلة
 */
function hideAllDropdowns() {
    const allDropdowns = document.querySelectorAll('.dropdown-menu.show');
    allDropdowns.forEach(dropdown => {
        hideDropdown(dropdown);
    });
}

/**
 * إصلاح مشكلة z-index للقوائم المنسدلة
 */
function fixDropdownZIndex() {
    const dropdownMenus = document.querySelectorAll('.dropdown-menu');
    dropdownMenus.forEach(menu => {
        menu.style.zIndex = '9999';
        menu.style.position = 'absolute';
    });
}

/**
 * إضافة تأثيرات بصرية للقوائم المنسدلة
 */
function enhanceDropdownVisuals() {
    const dropdownItems = document.querySelectorAll('.dropdown-item');

    dropdownItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(4px)';
            this.style.transition = 'all 0.3s ease';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
}

/**
 * إصلاح موضع القوائم المنسدلة
 */
function fixDropdownPositioning() {
    const dropdowns = document.querySelectorAll('.navbar .dropdown');

    dropdowns.forEach(dropdown => {
        const menu = dropdown.querySelector('.dropdown-menu');
        if (menu) {
            // تأكيد الموضع الصحيح
            menu.style.position = 'absolute';
            menu.style.top = 'calc(100% + 0.5rem)';
            menu.style.zIndex = '9999';
            menu.style.transform = 'none';
            menu.style.left = 'auto';
            menu.style.right = '0';

            // إصلاح خاص لقائمة الإدارة
            const adminDropdown = dropdown.querySelector('#adminDropdown');
            if (adminDropdown) {
                menu.style.left = '0';
                menu.style.right = 'auto';
            }

            // إصلاح خاص لقائمة الإشعارات
            const notificationsContainer = dropdown.querySelector('#notificationsContainer');
            if (notificationsContainer) {
                notificationsContainer.style.width = '320px';
                notificationsContainer.style.maxHeight = '400px';
                notificationsContainer.style.overflowY = 'auto';
            }
        }
    });
}

// تشغيل الإصلاحات عند تحميل الصفحة
window.addEventListener('load', function() {
    fixDropdownZIndex();
    enhanceDropdownVisuals();
    fixDropdownPositioning();

    // إعادة تهيئة Bootstrap dropdowns
    if (typeof bootstrap !== 'undefined') {
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl, {
                autoClose: true,
                boundary: 'viewport',
                popperConfig: {
                    placement: 'bottom-end',
                    strategy: 'absolute'
                }
            });
        });
        console.log('Bootstrap dropdowns reinitialized:', dropdownList.length);
    }

    // إصلاح إضافي للموضع
    setTimeout(fixDropdownPositioning, 500);
});

// إضافة أنماط CSS مباشرة للتأكد من عمل القوائم
const style = document.createElement('style');
style.textContent = `
    .dropdown-menu {
        z-index: 9999 !important;
        position: absolute !important;
        background: rgba(255, 255, 255, 0.98) !important;
        border: 1px solid rgba(0, 0, 0, 0.1) !important;
        border-radius: 12px !important;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
        backdrop-filter: blur(10px) !important;
        min-width: 200px !important;
        padding: 0.5rem !important;
    }

    .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .dropdown-item {
        color: #374151 !important;
        padding: 0.75rem 1rem !important;
        border-radius: 8px !important;
        transition: all 0.3s ease !important;
        margin-bottom: 0.25rem !important;
    }

    .dropdown-item:hover {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
        color: white !important;
        transform: translateX(4px) !important;
    }

    .dropdown-item i {
        margin-left: 0.5rem !important;
        color: #3b82f6 !important;
        transition: color 0.3s ease !important;
    }

    .dropdown-item:hover i {
        color: white !important;
    }

    .navbar .dropdown:hover .dropdown-menu {
        display: block !important;
    }
`;
document.head.appendChild(style);

console.log('Navbar fix styles applied');
