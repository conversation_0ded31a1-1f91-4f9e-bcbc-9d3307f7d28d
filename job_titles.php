<?php
/**
 * Job Titles Management Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include header
require_once 'includes/header.php';

// Require login and admin role
requireRole(['admin']);

// Check if job_titles table exists
try {
    $checkTableStmt = $pdo->query("SHOW TABLES LIKE 'job_titles'");
    if ($checkTableStmt->rowCount() == 0) {
        // Table doesn't exist, redirect to create table page
        flash('info_message', 'جدول العناوين الوظيفية غير موجود. سيتم توجيهك إلى صفحة إنشاء الجدول.', 'alert alert-info');
        header('Location: create_job_titles_table.php');
        exit;
    }
} catch (PDOException $e) {
    error_log("Check Table Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء التحقق من وجود جدول العناوين الوظيفية: ' . $e->getMessage(), 'alert alert-danger');
}

// Get job titles data
try {
    $stmt = $pdo->query("
        SELECT jt.*, g.name as grade_name
        FROM job_titles jt
        JOIN grades g ON jt.grade_id = g.id
        ORDER BY jt.grade_id ASC, jt.title ASC
    ");
    $jobTitles = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Job Titles Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات العناوين الوظيفية', 'alert alert-danger');
    $jobTitles = [];
}

// Process form submission for adding/editing job title
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_job_title'])) {
    // Sanitize inputs
    $titleId = isset($_POST['title_id']) ? (int)$_POST['title_id'] : 0;
    $gradeId = (int)$_POST['grade_id'];
    $title = sanitize($_POST['title']);
    $description = sanitize($_POST['description']);

    // Validate inputs
    $errors = [];

    if ($gradeId <= 0 || $gradeId > 10) {
        $errors[] = 'الدرجة غير صالحة';
    }

    if (empty($title)) {
        $errors[] = 'العنوان الوظيفي مطلوب';
    }

    // If no errors, add or update job title
    if (empty($errors)) {
        try {
            if ($titleId > 0) {
                // Update existing job title
                $stmt = $pdo->prepare("
                    UPDATE job_titles SET
                        grade_id = :grade_id,
                        title = :title,
                        description = :description
                    WHERE id = :id
                ");

                $stmt->execute([
                    ':grade_id' => $gradeId,
                    ':title' => $title,
                    ':description' => $description,
                    ':id' => $titleId
                ]);

                flash('success_message', 'تم تحديث العنوان الوظيفي بنجاح', 'alert alert-success');
            } else {
                // Add new job title
                $stmt = $pdo->prepare("
                    INSERT INTO job_titles (grade_id, title, description)
                    VALUES (:grade_id, :title, :description)
                ");

                $stmt->execute([
                    ':grade_id' => $gradeId,
                    ':title' => $title,
                    ':description' => $description
                ]);

                flash('success_message', 'تمت إضافة العنوان الوظيفي بنجاح', 'alert alert-success');
            }

            // Redirect to refresh the page
            redirect('job_titles.php');
        } catch (PDOException $e) {
            error_log("Save Job Title Error: " . $e->getMessage());
            flash('error_message', 'حدث خطأ أثناء حفظ بيانات العنوان الوظيفي', 'alert alert-danger');
        }
    } else {
        // Display errors
        foreach ($errors as $error) {
            flash('error_message', $error, 'alert alert-danger');
        }
    }
}

// Process form submission for deleting job title
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_job_title'])) {
    $titleId = (int)$_POST['title_id'];

    try {
        $stmt = $pdo->prepare("DELETE FROM job_titles WHERE id = :id");
        $stmt->execute([':id' => $titleId]);

        flash('success_message', 'تم حذف العنوان الوظيفي بنجاح', 'alert alert-success');
        redirect('job_titles.php');
    } catch (PDOException $e) {
        error_log("Delete Job Title Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء حذف العنوان الوظيفي', 'alert alert-danger');
    }
}

// Get grades
try {
    $gradesStmt = $pdo->query("SELECT id, name FROM grades ORDER BY id ASC");
    $grades = $gradesStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Grades Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الدرجات', 'alert alert-danger');
    $grades = [];

    // If grades table doesn't exist, create dummy grades
    if (strpos($e->getMessage(), "Table") !== false && strpos($e->getMessage(), "doesn't exist") !== false) {
        $grades = [];
        for ($i = 1; $i <= 10; $i++) {
            $grades[] = ['id' => $i, 'name' => 'الدرجة ' . $i];
        }
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-id-card-alt me-2"></i> إدارة العناوين الوظيفية
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addJobTitleModal">
            <i class="fas fa-plus me-1"></i> إضافة عنوان وظيفي
        </button>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى الرئيسية
        </a>
    </div>
</div>

<!-- Job Titles Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="jobTitlesTable">
                <thead>
                    <tr>
                        <th>الدرجة</th>
                        <th>العنوان الوظيفي</th>
                        <th>الوصف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (count($jobTitles) > 0): ?>
                        <?php foreach ($jobTitles as $jobTitle): ?>
                            <tr>
                                <td><?php echo $jobTitle['grade_name']; ?></td>
                                <td><?php echo $jobTitle['title']; ?></td>
                                <td><?php echo $jobTitle['description']; ?></td>
                                <td>
                                    <button class="btn btn-sm btn-warning edit-job-title-btn"
                                            data-id="<?php echo $jobTitle['id']; ?>"
                                            data-grade-id="<?php echo $jobTitle['grade_id']; ?>"
                                            data-title="<?php echo htmlspecialchars($jobTitle['title']); ?>"
                                            data-description="<?php echo htmlspecialchars($jobTitle['description']); ?>">
                                        <i class="fas fa-edit"></i> تعديل
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-job-title-btn"
                                            data-id="<?php echo $jobTitle['id']; ?>"
                                            data-title="<?php echo htmlspecialchars($jobTitle['title']); ?>">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="4" class="text-center">لا توجد بيانات للعناوين الوظيفية</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add/Edit Job Title Modal -->
<div class="modal fade" id="addJobTitleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">إضافة عنوان وظيفي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="jobTitleForm" action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                    <input type="hidden" id="title_id" name="title_id" value="0">

                    <div class="mb-3">
                        <label for="grade_id" class="form-label">الدرجة <span class="text-danger">*</span></label>
                        <select class="form-select" id="grade_id" name="grade_id" required>
                            <option value="">اختر الدرجة</option>
                            <?php foreach ($grades as $grade): ?>
                                <option value="<?php echo $grade['id']; ?>"><?php echo $grade['name']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">العنوان الوظيفي <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>

                    <input type="hidden" name="save_job_title" value="1">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" form="jobTitleForm" class="btn btn-primary">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Job Title Modal -->
<div class="modal fade" id="deleteJobTitleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حذف عنوان وظيفي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف العنوان الوظيفي: <span id="deleteTitleName"></span>؟</p>
                <form id="deleteJobTitleForm" action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                    <input type="hidden" id="delete_title_id" name="title_id">
                    <input type="hidden" name="delete_job_title" value="1">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" form="deleteJobTitleForm" class="btn btn-danger">حذف</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTable
        $('#jobTitlesTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
            },
            "order": [[0, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": 3 }
            ]
        });

        // Edit job title button click
        const editButtons = document.querySelectorAll('.edit-job-title-btn');
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const gradeId = this.getAttribute('data-grade-id');
                const title = this.getAttribute('data-title');
                const description = this.getAttribute('data-description');

                document.getElementById('title_id').value = id;
                document.getElementById('grade_id').value = gradeId;
                document.getElementById('title').value = title;
                document.getElementById('description').value = description;

                document.getElementById('modalTitle').textContent = 'تعديل العنوان الوظيفي';

                const modal = new bootstrap.Modal(document.getElementById('addJobTitleModal'));
                modal.show();
            });
        });

        // Delete job title button click
        const deleteButtons = document.querySelectorAll('.delete-job-title-btn');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const title = this.getAttribute('data-title');

                document.getElementById('delete_title_id').value = id;
                document.getElementById('deleteTitleName').textContent = title;

                const modal = new bootstrap.Modal(document.getElementById('deleteJobTitleModal'));
                modal.show();
            });
        });

        // Reset form when modal is closed
        const addJobTitleModal = document.getElementById('addJobTitleModal');
        addJobTitleModal.addEventListener('hidden.bs.modal', function() {
            document.getElementById('jobTitleForm').reset();
            document.getElementById('title_id').value = 0;
            document.getElementById('modalTitle').textContent = 'إضافة عنوان وظيفي';
        });
    });
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
