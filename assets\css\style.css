/**
 * Custom CSS Styles
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* RTL Specific Adjustments */
.dropdown-menu-end {
    left: 0;
    right: auto;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Transitions */
.transition-all {
    transition: all 0.3s ease;
}

/* Card Styles */
.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: bold;
}

/* Dashboard Cards */
.dashboard-card {
    text-align: center;
    padding: 20px;
}

.dashboard-card .icon {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #0d6efd;
}

.dashboard-card .count {
    font-size: 2rem;
    font-weight: bold;
}

.dashboard-card .title {
    font-size: 1.2rem;
    color: #6c757d;
}

/* Table Styles */
.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

.table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

/* Form Styles */
.form-label {
    font-weight: bold;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Button Styles */
.btn {
    border-radius: 5px;
    padding: 0.5rem 1rem;
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-icon i {
    margin-left: 0.5rem;
}

/* Alert Styles */
.alert {
    border-radius: 10px;
}

/* Badge Styles */
.badge {
    padding: 0.5em 0.8em;
    font-weight: normal;
}

/* Login Form */
.login-form {
    max-width: 400px;
    margin: 50px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.login-form .logo {
    text-align: center;
    margin-bottom: 20px;
}

.login-form .logo i {
    font-size: 3rem;
    color: #0d6efd;
}

/* Profile Page */
.profile-header {
    background-color: #0d6efd;
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.profile-header h2 {
    margin-bottom: 0;
}

/* Employee Card */
.employee-card {
    margin-bottom: 20px;
}

.employee-card .employee-info {
    padding: 15px;
}

.employee-card .label {
    font-weight: bold;
    color: #6c757d;
}

.employee-card .value {
    font-weight: normal;
}

/* Status Indicators */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-left: 5px;
}

.status-active {
    background-color: #198754;
}

.status-pending {
    background-color: #ffc107;
}

.status-expired {
    background-color: #dc3545;
}

/* Notification Styles */
.notification-counter {
    font-size: 0.6rem;
}

.notification-item {
    transition: background-color 0.3s ease;
    border-bottom: 1px solid #eee;
    padding: 10px;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: bold;
    margin-bottom: 2px;
}

.notification-text {
    color: #6c757d;
    margin-bottom: 2px;
}

.notification-time {
    font-size: 0.75rem;
    color: #adb5bd;
}

/* Chart Styles */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 20px;
}

/* Dashboard Improvements */
.dashboard-card {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.dashboard-card .icon {
    font-size: 3.5rem;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.dashboard-card:hover .icon {
    transform: scale(1.1);
}

.dashboard-card .count {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.dashboard-card .title {
    font-size: 1.2rem;
    color: #6c757d;
}

.dashboard-card .stretched-link::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    content: "";
}

/* Quick Links */
.btn-outline-primary, .btn-outline-success, .btn-outline-warning, .btn-outline-secondary {
    transition: all 0.3s ease;
}

.btn-outline-primary:hover, .btn-outline-success:hover, .btn-outline-warning:hover, .btn-outline-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    .container {
        width: 100%;
        max-width: 100%;
    }

    body {
        font-size: 12pt;
    }

    .table {
        width: 100% !important;
    }
}


/* RTL Fixes */
body, .container, .row, .col, .form-group, .table {
    direction: rtl;
    text-align: right;
}

.dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

.input-group-text {
    border-radius: 0 0.25rem 0.25rem 0 !important;
}

.form-control {
    border-radius: 0.25rem 0 0 0.25rem !important;
}
