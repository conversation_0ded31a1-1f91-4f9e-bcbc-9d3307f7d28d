/**
 * Enhanced CSS Styles
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 * Modern UI Design System
 */

/* CSS Custom Properties (Variables) */
:root {
    /* Primary Color Palette */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Secondary Color Palette */
    --secondary-50: #f8fafc;
    --secondary-100: #f1f5f9;
    --secondary-200: #e2e8f0;
    --secondary-300: #cbd5e1;
    --secondary-400: #94a3b8;
    --secondary-500: #64748b;
    --secondary-600: #475569;
    --secondary-700: #334155;
    --secondary-800: #1e293b;
    --secondary-900: #0f172a;

    /* Success Colors */
    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-200: #bbf7d0;
    --success-300: #86efac;
    --success-400: #4ade80;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;
    --success-800: #166534;
    --success-900: #14532d;

    /* Warning Colors */
    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-200: #fde68a;
    --warning-300: #fcd34d;
    --warning-400: #fbbf24;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    --warning-800: #92400e;
    --warning-900: #78350f;

    /* Danger Colors */
    --danger-50: #fef2f2;
    --danger-100: #fee2e2;
    --danger-200: #fecaca;
    --danger-300: #fca5a5;
    --danger-400: #f87171;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --danger-700: #b91c1c;
    --danger-800: #991b1b;
    --danger-900: #7f1d1d;

    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Typography */
    --font-family-primary: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-family-arabic: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* Enhanced General Styles */
body {
    font-family: var(--font-family-arabic);
    background-color: #f8f9fa;
    color: #212529;
    line-height: 1.6;
    font-size: 16px;
}

/* Typography Fixes */
h1, h2, h3, h4, h5, h6 {
    color: #212529;
}

.text-muted {
    color: #6c757d !important;
}

.card-title {
    color: #212529;
}

.card-text {
    color: #212529;
}

/* Enhanced Navbar Styles */
.modern-navbar {
    background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-bottom: 3px solid #3b82f6;
    padding: 1rem 0;
    backdrop-filter: blur(10px);
}

.modern-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-weight: 600;
    font-size: 1.25rem;
    text-decoration: none;
    color: white !important;
    transition: all var(--transition-normal);
}

.modern-brand:hover {
    color: var(--primary-100) !important;
    transform: scale(1.02);
}

.brand-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.modern-brand:hover .brand-icon {
    transform: rotate(5deg) scale(1.1);
    box-shadow: 0 6px 25px rgba(59, 130, 246, 0.5);
    border-color: rgba(255, 255, 255, 0.3);
}

.brand-text {
    font-weight: 600;
    letter-spacing: -0.025em;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s ease;
}

.navbar-nav .nav-link:hover::before {
    left: 100%;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: white !important;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(29, 78, 216, 0.3));
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.navbar-nav .nav-link i {
    margin-left: var(--spacing-xs);
    font-size: 0.9rem;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    padding: 0.5rem;
    margin-top: 0.5rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dropdown-item {
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #374151 !important;
    margin-bottom: 0.25rem;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white !important;
    transform: translateX(4px);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.dropdown-item i {
    margin-left: 0.5rem;
    color: #3b82f6;
    transition: color 0.3s ease;
}

.dropdown-item:hover i {
    color: white;
}

/* RTL Specific Adjustments */
.dropdown-menu-end {
    left: 0;
    right: auto;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Transitions */
.transition-all {
    transition: all 0.3s ease;
}

/* Enhanced Card Styles */
.card {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    background: white;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-200);
}

.card-header {
    border-radius: var(--radius-xl) var(--radius-xl) 0 0 !important;
    font-weight: 600;
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    color: white;
    border-bottom: none;
    padding: var(--spacing-lg);
}

.card-header.bg-success {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%) !important;
}

.card-header.bg-warning {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%) !important;
    color: var(--gray-800) !important;
}

.card-header.bg-danger {
    background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%) !important;
}

.card-header.bg-info {
    background: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-500) 100%) !important;
}

.card-header.bg-secondary {
    background: linear-gradient(135deg, var(--secondary-500) 0%, var(--secondary-600) 100%) !important;
}

.card-header.bg-dark {
    background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-900) 100%) !important;
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    background-color: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: var(--spacing-md) var(--spacing-lg);
}

/* Enhanced Dashboard Cards */
.dashboard-card {
    text-align: center;
    padding: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    transition: all var(--transition-normal);
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-300);
}

.dashboard-card:hover::before {
    height: 6px;
    background: linear-gradient(90deg, var(--primary-400), var(--primary-700));
}

.dashboard-card .icon {
    font-size: 3.5rem;
    margin-bottom: var(--spacing-lg);
    color: var(--primary-500);
    transition: all var(--transition-normal);
    display: inline-block;
}

.dashboard-card:hover .icon {
    transform: scale(1.1);
    color: var(--primary-600);
}

.dashboard-card .count {
    font-size: 2.5rem;
    font-weight: 700;
    color: #212529;
    margin-bottom: var(--spacing-sm);
    line-height: 1;
}

.dashboard-card .title {
    font-size: 1.1rem;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0;
}

/* Dashboard Card Variants */
.dashboard-card.success .icon {
    color: var(--success-500);
}

.dashboard-card.success::before {
    background: linear-gradient(90deg, var(--success-500), var(--success-600));
}

.dashboard-card.success:hover .icon {
    color: var(--success-600);
}

.dashboard-card.warning .icon {
    color: var(--warning-500);
}

.dashboard-card.warning::before {
    background: linear-gradient(90deg, var(--warning-500), var(--warning-600));
}

.dashboard-card.warning:hover .icon {
    color: var(--warning-600);
}

.dashboard-card.danger .icon {
    color: var(--danger-500);
}

.dashboard-card.danger::before {
    background: linear-gradient(90deg, var(--danger-500), var(--danger-600));
}

.dashboard-card.danger:hover .icon {
    color: var(--danger-600);
}

/* Table Styles */
.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

.table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #212529;
}

.table td {
    color: #212529;
}

/* Form Styles */
.form-label {
    font-weight: bold;
    color: #212529;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Enhanced Button Styles */
.btn {
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-weight: 500;
    transition: all var(--transition-fast);
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-color: var(--primary-500);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    border-color: var(--primary-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
    border-color: var(--success-500);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--success-600), var(--success-700));
    border-color: var(--success-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
    border-color: var(--warning-500);
    color: var(--gray-800);
}

.btn-warning:hover {
    background: linear-gradient(135deg, var(--warning-600), var(--warning-700));
    border-color: var(--warning-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-500), var(--danger-600));
    border-color: var(--danger-500);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, var(--danger-600), var(--danger-700));
    border-color: var(--danger-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-primary {
    border-color: var(--primary-500);
    color: var(--primary-500);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-500);
    border-color: var(--primary-500);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.btn-icon i {
    margin-left: 0;
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.875rem;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1.125rem;
}

/* Alert Styles */
.alert {
    border-radius: 10px;
}

/* Badge Styles */
.badge {
    padding: 0.5em 0.8em;
    font-weight: normal;
}

/* Login Form */
.login-form {
    max-width: 400px;
    margin: 50px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.login-form .logo {
    text-align: center;
    margin-bottom: 20px;
}

.login-form .logo i {
    font-size: 3rem;
    color: #0d6efd;
}

/* Profile Page */
.profile-header {
    background-color: #0d6efd;
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.profile-header h2 {
    margin-bottom: 0;
}

/* Employee Card */
.employee-card {
    margin-bottom: 20px;
}

.employee-card .employee-info {
    padding: 15px;
}

.employee-card .label {
    font-weight: bold;
    color: #6c757d;
}

.employee-card .value {
    font-weight: normal;
}

/* Status Indicators */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-left: 5px;
}

.status-active {
    background-color: #198754;
}

.status-pending {
    background-color: #ffc107;
}

.status-expired {
    background-color: #dc3545;
}

/* Notification Styles */
.notification-counter {
    font-size: 0.6rem;
}

.notification-item {
    transition: background-color 0.3s ease;
    border-bottom: 1px solid #eee;
    padding: 10px;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: bold;
    margin-bottom: 2px;
}

.notification-text {
    color: #6c757d;
    margin-bottom: 2px;
}

.notification-time {
    font-size: 0.75rem;
    color: #adb5bd;
}

/* Chart Styles */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 20px;
}

/* Dashboard Improvements */
.dashboard-card {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.dashboard-card .icon {
    font-size: 3.5rem;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.dashboard-card:hover .icon {
    transform: scale(1.1);
}

.dashboard-card .count {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.dashboard-card .title {
    font-size: 1.2rem;
    color: #6c757d;
}

.dashboard-card .stretched-link::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    content: "";
}

/* Quick Links */
.btn-outline-primary, .btn-outline-success, .btn-outline-warning, .btn-outline-secondary {
    transition: all 0.3s ease;
}

.btn-outline-primary:hover, .btn-outline-success:hover, .btn-outline-warning:hover, .btn-outline-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    .container {
        width: 100%;
        max-width: 100%;
    }

    body {
        font-size: 12pt;
    }

    .table {
        width: 100% !important;
    }
}


/* RTL Fixes */
body, .container, .row, .col, .form-group, .table {
    direction: rtl;
    text-align: right;
}

.dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

.input-group-text {
    border-radius: 0 0.25rem 0.25rem 0 !important;
}

.form-control {
    border-radius: 0.25rem 0 0 0.25rem !important;
}
