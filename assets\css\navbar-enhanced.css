/**
 * Enhanced Navbar Styles
 * تحسينات متقدمة لشريط التنقل
 */

/* شريط التنقل الرئيسي */
.modern-navbar {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 75%, #475569 100%) !important;
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.15),
        0 1px 3px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    border-bottom: 3px solid #3b82f6 !important;
    padding: 1rem 0 !important;
    backdrop-filter: blur(10px) !important;
    position: relative !important;
    overflow: hidden !important;
}

/* تأثير الخلفية المتحركة */
.modern-navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    animation: navbarShine 3s infinite;
}

@keyframes navbarShine {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

/* العلامة التجارية المحسنة */
.modern-brand {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
    font-weight: 600 !important;
    font-size: 1.25rem !important;
    text-decoration: none !important;
    color: white !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    z-index: 2 !important;
}

.modern-brand:hover {
    color: #dbeafe !important;
    transform: scale(1.02) !important;
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.5) !important;
}

/* أيقونة العلامة التجارية */
.brand-icon {
    width: 50px !important;
    height: 50px !important;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%) !important;
    border-radius: 15px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.4rem !important;
    color: white !important;
    box-shadow: 
        0 4px 15px rgba(59, 130, 246, 0.4),
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transition: all 0.3s ease !important;
    border: 2px solid rgba(255, 255, 255, 0.15) !important;
    position: relative !important;
    overflow: hidden !important;
}

.brand-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.3s ease;
}

.modern-brand:hover .brand-icon {
    transform: rotate(5deg) scale(1.1) !important;
    box-shadow: 
        0 6px 25px rgba(59, 130, 246, 0.6),
        0 3px 12px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.modern-brand:hover .brand-icon::before {
    left: 100%;
    top: 100%;
}

/* نص العلامة التجارية */
.brand-text {
    font-weight: 700 !important;
    letter-spacing: -0.025em !important;
    background: linear-gradient(135deg, #ffffff 0%, #dbeafe 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-shadow: none !important;
}

/* روابط التنقل */
.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85) !important;
    font-weight: 500 !important;
    padding: 0.75rem 1.25rem !important;
    border-radius: 10px !important;
    transition: all 0.3s ease !important;
    margin: 0 0.25rem !important;
    position: relative !important;
    overflow: hidden !important;
    text-transform: capitalize !important;
    letter-spacing: 0.025em !important;
}

/* تأثير الانزلاق للروابط */
.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s ease;
}

.navbar-nav .nav-link:hover::before {
    left: 100%;
}

/* حالة التمرير والنشط للروابط */
.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: white !important;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.25), rgba(29, 78, 216, 0.35)) !important;
    transform: translateY(-2px) !important;
    box-shadow: 
        0 4px 15px rgba(59, 130, 246, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(59, 130, 246, 0.3) !important;
}

/* أيقونات الروابط */
.navbar-nav .nav-link i {
    margin-left: 0.5rem !important;
    font-size: 0.9rem !important;
    transition: all 0.3s ease !important;
}

.navbar-nav .nav-link:hover i {
    transform: scale(1.1) !important;
    color: #dbeafe !important;
}

/* القوائم المنسدلة المحسنة */
.dropdown-menu {
    border: none !important;
    box-shadow: 
        0 10px 40px rgba(0, 0, 0, 0.15),
        0 4px 20px rgba(0, 0, 0, 0.1) !important;
    border-radius: 15px !important;
    padding: 0.75rem !important;
    margin-top: 0.5rem !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(15px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    min-width: 220px !important;
}

/* عناصر القائمة المنسدلة */
.dropdown-item {
    border-radius: 10px !important;
    padding: 0.75rem 1rem !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
    color: #374151 !important;
    margin-bottom: 0.25rem !important;
    position: relative !important;
    overflow: hidden !important;
}

.dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.3s ease;
}

.dropdown-item:hover::before {
    left: 100%;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
    color: white !important;
    transform: translateX(4px) !important;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;
}

.dropdown-item i {
    margin-left: 0.75rem !important;
    color: #3b82f6 !important;
    transition: color 0.3s ease !important;
    font-size: 0.9rem !important;
}

.dropdown-item:hover i {
    color: white !important;
    transform: scale(1.1) !important;
}

/* زر القائمة للهاتف المحمول */
.navbar-toggler {
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 8px !important;
    padding: 0.5rem !important;
    transition: all 0.3s ease !important;
}

.navbar-toggler:hover {
    border-color: rgba(255, 255, 255, 0.5) !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    transform: scale(1.05) !important;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
}

/* أيقونة زر القائمة */
.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.85%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
    transition: all 0.3s ease !important;
}

/* تحسينات للهاتف المحمول */
@media (max-width: 768px) {
    .modern-navbar {
        padding: 0.75rem 0 !important;
    }
    
    .brand-icon {
        width: 40px !important;
        height: 40px !important;
        font-size: 1.2rem !important;
    }
    
    .brand-text {
        font-size: 1.1rem !important;
    }
    
    .navbar-nav .nav-link {
        padding: 0.5rem 1rem !important;
        margin: 0.25rem 0 !important;
        border-radius: 8px !important;
    }
    
    .dropdown-menu {
        margin-top: 0.25rem !important;
        border-radius: 12px !important;
    }
}
