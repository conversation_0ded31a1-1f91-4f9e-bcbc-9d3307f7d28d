-- إن<PERSON><PERSON>ء جدول تصنيفات الوثائق
CREATE TABLE IF NOT EXISTS `document_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  CONSTRAINT `document_categories_parent_fk` FOREIGN KEY (`parent_id`) REFERENCES `document_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إ<PERSON><PERSON><PERSON><PERSON> جدول الوثائق
CREATE TABLE IF NOT EXISTS `documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_type` varchar(50) NOT NULL,
  `file_size` int(11) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `employee_id` int(11) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `document_date` date DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `status` enum('active','archived','expired','deleted') NOT NULL DEFAULT 'active',
  `is_confidential` tinyint(1) NOT NULL DEFAULT 0,
  `access_level` enum('public','department','restricted','private') NOT NULL DEFAULT 'public',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `view_count` int(11) NOT NULL DEFAULT 0,
  `download_count` int(11) NOT NULL DEFAULT 0,
  `is_signed` tinyint(1) NOT NULL DEFAULT 0,
  `signature_date` datetime DEFAULT NULL,
  `signed_by` int(11) DEFAULT NULL,
  `is_template` tinyint(1) NOT NULL DEFAULT 0,
  `tags` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `employee_id` (`employee_id`),
  KEY `department_id` (`department_id`),
  KEY `created_by` (`created_by`),
  KEY `signed_by` (`signed_by`),
  KEY `document_date` (`document_date`),
  KEY `expiry_date` (`expiry_date`),
  KEY `reference_number` (`reference_number`),
  KEY `status` (`status`),
  KEY `access_level` (`access_level`),
  CONSTRAINT `documents_category_fk` FOREIGN KEY (`category_id`) REFERENCES `document_categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `documents_employee_fk` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE SET NULL,
  CONSTRAINT `documents_department_fk` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL,
  CONSTRAINT `documents_created_by_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `documents_signed_by_fk` FOREIGN KEY (`signed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول الكلمات المفتاحية للوثائق
CREATE TABLE IF NOT EXISTS `document_keywords` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `document_id` int(11) NOT NULL,
  `keyword` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `document_id` (`document_id`),
  KEY `keyword` (`keyword`),
  CONSTRAINT `document_keywords_document_fk` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول صلاحيات الوصول للوثائق
CREATE TABLE IF NOT EXISTS `document_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `document_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `permission_type` enum('view','edit','delete','download','sign') NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `document_user_permission` (`document_id`,`user_id`,`permission_type`),
  UNIQUE KEY `document_department_permission` (`document_id`,`department_id`,`permission_type`),
  KEY `document_id` (`document_id`),
  KEY `user_id` (`user_id`),
  KEY `department_id` (`department_id`),
  CONSTRAINT `document_permissions_document_fk` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`) ON DELETE CASCADE,
  CONSTRAINT `document_permissions_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `document_permissions_department_fk` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول سجل الوثائق
CREATE TABLE IF NOT EXISTS `document_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `document_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` enum('create','view','edit','delete','download','sign','share','archive','restore') NOT NULL,
  `action_details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `document_id` (`document_id`),
  KEY `user_id` (`user_id`),
  KEY `action` (`action`),
  CONSTRAINT `document_logs_document_fk` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`) ON DELETE CASCADE,
  CONSTRAINT `document_logs_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول التوقيعات الإلكترونية
CREATE TABLE IF NOT EXISTS `document_signatures` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `document_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `signature_image` varchar(255) DEFAULT NULL,
  `signature_position` varchar(100) DEFAULT NULL,
  `signature_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `signature_type` enum('digital','electronic','manual') NOT NULL DEFAULT 'electronic',
  `signature_hash` varchar(255) DEFAULT NULL,
  `signature_certificate` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `is_valid` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `document_id` (`document_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `document_signatures_document_fk` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`) ON DELETE CASCADE,
  CONSTRAINT `document_signatures_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول القوانين والتشريعات
CREATE TABLE IF NOT EXISTS `legal_documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `document_number` varchar(100) DEFAULT NULL,
  `document_type` enum('law','regulation','instruction','decision','circular') NOT NULL,
  `issue_date` date NOT NULL,
  `effective_date` date DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `issuing_authority` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `file_path` varchar(255) NOT NULL,
  `status` enum('active','superseded','cancelled') NOT NULL DEFAULT 'active',
  `superseded_by` int(11) DEFAULT NULL,
  `related_to` text DEFAULT NULL,
  `keywords` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `document_number` (`document_number`),
  KEY `document_type` (`document_type`),
  KEY `issue_date` (`issue_date`),
  KEY `effective_date` (`effective_date`),
  KEY `expiry_date` (`expiry_date`),
  KEY `status` (`status`),
  KEY `superseded_by` (`superseded_by`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `legal_documents_superseded_fk` FOREIGN KEY (`superseded_by`) REFERENCES `legal_documents` (`id`) ON DELETE SET NULL,
  CONSTRAINT `legal_documents_created_by_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول نماذج المستندات
CREATE TABLE IF NOT EXISTS `document_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `file_path` varchar(255) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `template_type` enum('letter','form','report','certificate','other') NOT NULL DEFAULT 'letter',
  `is_official` tinyint(1) NOT NULL DEFAULT 0,
  `authority` varchar(255) DEFAULT NULL,
  `fields_json` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `template_type` (`template_type`),
  KEY `is_official` (`is_official`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `document_templates_category_fk` FOREIGN KEY (`category_id`) REFERENCES `document_categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `document_templates_created_by_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدخال بيانات أولية لتصنيفات الوثائق
INSERT INTO `document_categories` (`name`, `description`, `parent_id`) VALUES
('أوامر إدارية', 'الأوامر الإدارية الصادرة من الإدارة', NULL),
('كتب رسمية', 'المراسلات الرسمية الصادرة والواردة', NULL),
('قرارات', 'القرارات الإدارية', NULL),
('تعاميم', 'التعاميم الإدارية', NULL),
('ملفات موظفين', 'وثائق خاصة بملفات الموظفين', NULL),
('شهادات وتقارير', 'الشهادات والتقارير الرسمية', NULL),
('قوانين وتشريعات', 'القوانين والتشريعات والتعليمات', NULL),
('نماذج رسمية', 'النماذج الرسمية المعتمدة', NULL);

-- إدخال بيانات أولية لتصنيفات فرعية
INSERT INTO `document_categories` (`name`, `description`, `parent_id`) VALUES
('أوامر ترقية', 'أوامر الترقية للموظفين', 1),
('أوامر نقل', 'أوامر نقل الموظفين', 1),
('أوامر تكليف', 'أوامر تكليف الموظفين بمهام', 1),
('أوامر علاوة', 'أوامر منح العلاوات', 1),
('كتب صادرة', 'الكتب الصادرة من الدائرة', 2),
('كتب واردة', 'الكتب الواردة إلى الدائرة', 2),
('قرارات إدارية', 'القرارات المتعلقة بالشؤون الإدارية', 3),
('قرارات مالية', 'القرارات المتعلقة بالشؤون المالية', 3),
('تعاميم داخلية', 'التعاميم الداخلية للدائرة', 4),
('تعاميم خارجية', 'التعاميم الواردة من جهات خارجية', 4),
('وثائق شخصية', 'الوثائق الشخصية للموظفين', 5),
('شهادات خدمة', 'شهادات خدمة الموظفين', 6),
('تقارير أداء', 'تقارير تقييم أداء الموظفين', 6),
('قوانين', 'القوانين الصادرة من مجلس النواب', 7),
('تعليمات', 'التعليمات التنفيذية للقوانين', 7),
('نماذج طلبات', 'نماذج الطلبات الرسمية', 8),
('نماذج تقارير', 'نماذج التقارير الرسمية', 8);

-- إدخال بيانات أولية للقوانين والتشريعات العراقية
INSERT INTO `legal_documents` (`title`, `document_number`, `document_type`, `issue_date`, `effective_date`, `issuing_authority`, `description`, `file_path`, `status`, `keywords`, `created_by`) VALUES
('قانون الخدمة المدنية', '24', 'law', '1960-12-24', '1961-01-01', 'مجلس النواب العراقي', 'قانون الخدمة المدنية رقم 24 لسنة 1960 وتعديلاته', 'legal_documents/civil_service_law_24_1960.pdf', 'active', 'خدمة مدنية,موظفين,تعيين,ترفيع,علاوة', 1),
('قانون انضباط موظفي الدولة', '14', 'law', '1991-02-12', '1991-03-01', 'مجلس النواب العراقي', 'قانون انضباط موظفي الدولة والقطاع العام رقم 14 لسنة 1991 وتعديلاته', 'legal_documents/discipline_law_14_1991.pdf', 'active', 'انضباط,عقوبات,إجازات,غياب,دوام', 1),
('قانون التقاعد الموحد', '9', 'law', '2014-01-22', '2014-02-01', 'مجلس النواب العراقي', 'قانون التقاعد الموحد رقم 9 لسنة 2014 وتعديلاته', 'legal_documents/retirement_law_9_2014.pdf', 'active', 'تقاعد,راتب تقاعدي,خدمة,إحالة,استقالة', 1),
('قانون رواتب موظفي الدولة', '22', 'law', '2008-04-24', '2008-05-01', 'مجلس النواب العراقي', 'قانون رواتب موظفي الدولة والقطاع العام رقم 22 لسنة 2008 وتعديلاته', 'legal_documents/salary_law_22_2008.pdf', 'active', 'رواتب,مخصصات,درجات,علاوات,ترفيع', 1),
('قانون الملاك', '25', 'law', '1960-12-24', '1961-01-01', 'مجلس النواب العراقي', 'قانون الملاك رقم 25 لسنة 1960 وتعديلاته', 'legal_documents/staff_law_25_1960.pdf', 'active', 'ملاك,وظائف,درجات,عناوين وظيفية', 1),
('تعليمات تنفيذ قانون الموازنة العامة', '1', 'instruction', '2023-01-15', '2023-01-15', 'وزارة المالية', 'تعليمات تنفيذ قانون الموازنة العامة الاتحادية لجمهورية العراق للسنة المالية 2023', 'legal_documents/budget_instructions_2023.pdf', 'active', 'موازنة,صرف,تخصيصات,نفقات,إيرادات', 1),
('ضوابط الترفيع والعلاوة', '12', 'regulation', '2022-06-10', '2022-07-01', 'مجلس الخدمة العامة الاتحادي', 'ضوابط الترفيع والعلاوة السنوية لموظفي دوائر الدولة والقطاع العام', 'legal_documents/promotion_regulations_2022.pdf', 'active', 'ترفيع,علاوة,درجة,مدة,خدمة', 1),
('تعليمات منح الإجازات', '5', 'instruction', '2021-03-20', '2021-04-01', 'مجلس الخدمة العامة الاتحادي', 'تعليمات منح الإجازات لموظفي دوائر الدولة والقطاع العام', 'legal_documents/leave_instructions_2021.pdf', 'active', 'إجازات,اعتيادية,مرضية,دراسية,أمومة', 1);

-- إدخال بيانات أولية لنماذج المستندات
INSERT INTO `document_templates` (`title`, `description`, `file_path`, `category_id`, `template_type`, `is_official`, `authority`, `fields_json`, `created_by`) VALUES
('نموذج أمر إداري للترقية', 'نموذج الأمر الإداري الخاص بترقية الموظفين', 'templates/promotion_order_template.docx', 9, 'letter', 1, 'وزارة المالية', '{"fields": ["employee_name", "employee_id", "current_grade", "new_grade", "promotion_date", "years_in_service", "legal_reference", "effective_date"]}', 1),
('نموذج أمر إداري للعلاوة', 'نموذج الأمر الإداري الخاص بمنح العلاوة السنوية', 'templates/allowance_order_template.docx', 12, 'letter', 1, 'وزارة المالية', '{"fields": ["employee_name", "employee_id", "current_grade", "allowance_date", "years_in_service", "legal_reference", "effective_date"]}', 1),
('نموذج كتاب شكر وتقدير', 'نموذج كتاب الشكر والتقدير للموظفين', 'templates/appreciation_letter_template.docx', 2, 'letter', 1, 'الأمانة العامة لمجلس الوزراء', '{"fields": ["employee_name", "employee_id", "department", "achievement", "issue_date", "reference_number"]}', 1),
('نموذج شهادة خدمة', 'نموذج شهادة خدمة للموظفين', 'templates/service_certificate_template.docx', 12, 'certificate', 1, 'وزارة المالية', '{"fields": ["employee_name", "employee_id", "birth_date", "hire_date", "current_grade", "current_position", "department", "years_in_service", "issue_date", "reference_number"]}', 1),
('نموذج تقرير تقييم الأداء السنوي', 'نموذج التقرير السنوي لتقييم أداء الموظفين', 'templates/performance_evaluation_template.docx', 13, 'form', 1, 'وزارة المالية', '{"fields": ["employee_name", "employee_id", "department", "position", "evaluation_year", "performance_criteria", "rating", "strengths", "weaknesses", "recommendations", "evaluator_name", "evaluator_position", "evaluation_date"]}', 1),
('نموذج طلب إجازة', 'نموذج طلب الإجازة للموظفين', 'templates/leave_request_template.docx', 16, 'form', 1, 'وزارة المالية', '{"fields": ["employee_name", "employee_id", "department", "leave_type", "start_date", "end_date", "days_count", "reason", "contact_info", "substitute_name", "request_date"]}', 1),
('نموذج مباشرة بعد الإجازة', 'نموذج المباشرة بالدوام بعد انتهاء الإجازة', 'templates/return_to_work_template.docx', 16, 'form', 1, 'وزارة المالية', '{"fields": ["employee_name", "employee_id", "department", "leave_type", "leave_start_date", "leave_end_date", "return_date", "notes"]}', 1),
('نموذج طلب نقل', 'نموذج طلب النقل من دائرة إلى أخرى', 'templates/transfer_request_template.docx', 16, 'form', 1, 'وزارة المالية', '{"fields": ["employee_name", "employee_id", "current_department", "requested_department", "reason", "current_position", "years_in_service", "request_date"]}', 1);
