<?php
/**
 * Analytics Data Module
 * وحدة بيانات التحليلات
 */

// Define constants if not already defined
if (!defined('ALLOWANCES_PER_GRADE')) {
    define('ALLOWANCES_PER_GRADE', 11);
}

// Initialize variables with default values
$totalEmployees = 0;
$employeesByGrade = [];
$employeesByEducation = [];
$allowancesByMonth = [];
$promotionsByMonth = [];
$appreciationByType = [];
$departmentStats = [];
$upcomingAllowances = [
    'current' => 0,
    'within30Days' => 0,
    'within60Days' => 0,
    'within90Days' => 0,
    'beyond90Days' => 0
];
$upcomingPromotions = [
    'current' => 0,
    'within30Days' => 0,
    'within60Days' => 0,
    'within90Days' => 0,
    'beyond90Days' => 0
];

// Flag to check if we're using dummy data
$usingDummyData = false;

try {
    // Check if tables exist
    $tablesExist = true;

    // Check employees table
    $empTableCheck = $pdo->query("SHOW TABLES LIKE 'employees'");
    if ($empTableCheck->rowCount() == 0) {
        $tablesExist = false;
        $usingDummyData = true;
        // Create dummy data
        $totalEmployees = 100;

        // Dummy employees by grade
        $employeesByGrade = [
            ['current_grade' => 1, 'count' => 5],
            ['current_grade' => 2, 'count' => 8],
            ['current_grade' => 3, 'count' => 12],
            ['current_grade' => 4, 'count' => 15],
            ['current_grade' => 5, 'count' => 20],
            ['current_grade' => 6, 'count' => 15],
            ['current_grade' => 7, 'count' => 10],
            ['current_grade' => 8, 'count' => 8],
            ['current_grade' => 9, 'count' => 5],
            ['current_grade' => 10, 'count' => 2]
        ];

        // Dummy employees by education
        $employeesByEducation = [
            ['name' => 'بكالوريوس', 'count' => 45],
            ['name' => 'دبلوم', 'count' => 30],
            ['name' => 'ماجستير', 'count' => 15],
            ['name' => 'دكتوراه', 'count' => 10]
        ];

        // Dummy department stats
        $departmentStats = [
            [
                'id' => 1,
                'name' => 'قسم الموارد البشرية',
                'total_employees' => 25,
                'eligible_allowances' => 5,
                'eligible_promotions' => 3,
                'avg_years_service' => 8,
                'total_appreciation' => 15
            ],
            [
                'id' => 2,
                'name' => 'قسم تكنولوجيا المعلومات',
                'total_employees' => 30,
                'eligible_allowances' => 7,
                'eligible_promotions' => 4,
                'avg_years_service' => 6,
                'total_appreciation' => 20
            ],
            [
                'id' => 3,
                'name' => 'قسم المالية',
                'total_employees' => 20,
                'eligible_allowances' => 4,
                'eligible_promotions' => 2,
                'avg_years_service' => 10,
                'total_appreciation' => 12
            ],
            [
                'id' => 4,
                'name' => 'قسم الإدارة',
                'total_employees' => 25,
                'eligible_allowances' => 6,
                'eligible_promotions' => 3,
                'avg_years_service' => 12,
                'total_appreciation' => 18
            ]
        ];

        // Dummy upcoming data
        $upcomingAllowances = [
            'current' => 8,
            'within30Days' => 12,
            'within60Days' => 15,
            'within90Days' => 10,
            'beyond90Days' => 25
        ];

        $upcomingPromotions = [
            'current' => 5,
            'within30Days' => 8,
            'within60Days' => 10,
            'within90Days' => 7,
            'beyond90Days' => 15
        ];
    } else {
        // Get real data
        // Total employees
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM employees");
        $totalEmployees = $stmt->fetch()['count'];

        // Employees by grade
        $gradeStmt = $pdo->query("
            SELECT current_grade, COUNT(*) as count
            FROM employees
            GROUP BY current_grade
            ORDER BY current_grade ASC
        ");
        $employeesByGrade = $gradeStmt->fetchAll();

        // Check education_levels table
        $eduTableCheck = $pdo->query("SHOW TABLES LIKE 'education_levels'");
        if ($eduTableCheck->rowCount() > 0) {
            // Employees by education level
            $eduStmt = $pdo->query("
                SELECT el.name, COUNT(e.id) as count
                FROM employees e
                JOIN education_levels el ON e.education_level_id = el.id
                GROUP BY el.id, el.name
                ORDER BY COUNT(e.id) DESC
            ");
            $employeesByEducation = $eduStmt->fetchAll();
        } else {
            // Dummy education data
            $employeesByEducation = [
                ['name' => 'بكالوريوس', 'count' => 45],
                ['name' => 'دبلوم', 'count' => 30],
                ['name' => 'ماجستير', 'count' => 15],
                ['name' => 'دكتوراه', 'count' => 10]
            ];
        }

        // Department statistics
        $deptStatsStmt = $pdo->query("
            SELECT
                d.id,
                d.name,
                COUNT(e.id) as total_employees,
                SUM(CASE WHEN e.allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . " AND e.next_allowance_date <= CURDATE() THEN 1 ELSE 0 END) as eligible_allowances,
                SUM(CASE WHEN e.next_promotion_date <= CURDATE() THEN 1 ELSE 0 END) as eligible_promotions,
                AVG(e.years_of_service) as avg_years_service,
                0 as total_appreciation
            FROM departments d
            LEFT JOIN employees e ON d.id = e.department_id
            GROUP BY d.id, d.name
            ORDER BY d.name ASC
        ");
        $departmentStats = $deptStatsStmt->fetchAll();

        // Check if appreciation_letters table exists
        $appTableCheck = $pdo->query("SHOW TABLES LIKE 'appreciation_letters'");
        if ($appTableCheck->rowCount() > 0) {
            // Update total_appreciation for each department
            foreach ($departmentStats as &$dept) {
                $appStmt = $pdo->prepare("
                    SELECT COUNT(*) as count
                    FROM appreciation_letters a
                    JOIN employees e ON a.employee_id = e.id
                    WHERE e.department_id = :dept_id
                ");
                $appStmt->execute([':dept_id' => $dept['id']]);
                $appCount = $appStmt->fetch();
                $dept['total_appreciation'] = $appCount ? $appCount['count'] : 0;
            }
        }

        // Upcoming allowances and promotions
        $upcomingAllowancesStmt = $pdo->query("
            SELECT
                SUM(CASE WHEN next_allowance_date <= CURDATE() THEN 1 ELSE 0 END) as current,
                SUM(CASE WHEN next_allowance_date > CURDATE() AND next_allowance_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as within30Days,
                SUM(CASE WHEN next_allowance_date > DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND next_allowance_date <= DATE_ADD(CURDATE(), INTERVAL 60 DAY) THEN 1 ELSE 0 END) as within60Days,
                SUM(CASE WHEN next_allowance_date > DATE_ADD(CURDATE(), INTERVAL 60 DAY) AND next_allowance_date <= DATE_ADD(CURDATE(), INTERVAL 90 DAY) THEN 1 ELSE 0 END) as within90Days,
                SUM(CASE WHEN next_allowance_date > DATE_ADD(CURDATE(), INTERVAL 90 DAY) THEN 1 ELSE 0 END) as beyond90Days
            FROM employees
            WHERE allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
        ");
        $upcomingAllowances = $upcomingAllowancesStmt->fetch();

        $upcomingPromotionsStmt = $pdo->query("
            SELECT
                SUM(CASE WHEN next_promotion_date <= CURDATE() THEN 1 ELSE 0 END) as current,
                SUM(CASE WHEN next_promotion_date > CURDATE() AND next_promotion_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as within30Days,
                SUM(CASE WHEN next_promotion_date > DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND next_promotion_date <= DATE_ADD(CURDATE(), INTERVAL 60 DAY) THEN 1 ELSE 0 END) as within60Days,
                SUM(CASE WHEN next_promotion_date > DATE_ADD(CURDATE(), INTERVAL 60 DAY) AND next_promotion_date <= DATE_ADD(CURDATE(), INTERVAL 90 DAY) THEN 1 ELSE 0 END) as within90Days,
                SUM(CASE WHEN next_promotion_date > DATE_ADD(CURDATE(), INTERVAL 90 DAY) THEN 1 ELSE 0 END) as beyond90Days
            FROM employees
        ");
        $upcomingPromotions = $upcomingPromotionsStmt->fetch();
    }

    // Check allowance_history table
    $allowancesTableCheck = $pdo->query("SHOW TABLES LIKE 'allowance_history'");
    if ($allowancesTableCheck->rowCount() > 0) {
        // Allowances by month
        $allowancesStmt = $pdo->prepare("
            SELECT MONTH(created_at) as month, COUNT(*) as count
            FROM allowance_history
            WHERE YEAR(created_at) = :year
            GROUP BY MONTH(created_at)
            ORDER BY MONTH(created_at) ASC
        ");
        $allowancesStmt->execute([':year' => $year]);
        $allowancesByMonth = $allowancesStmt->fetchAll();
    } else {
        $usingDummyData = true;
        // Dummy allowances data
        $allowancesByMonth = [
            ['month' => 1, 'count' => 5],
            ['month' => 2, 'count' => 7],
            ['month' => 3, 'count' => 10],
            ['month' => 4, 'count' => 8],
            ['month' => 5, 'count' => 12],
            ['month' => 6, 'count' => 15],
            ['month' => 7, 'count' => 9],
            ['month' => 8, 'count' => 6],
            ['month' => 9, 'count' => 8],
            ['month' => 10, 'count' => 11],
            ['month' => 11, 'count' => 7],
            ['month' => 12, 'count' => 5]
        ];
    }

    // Check promotion_history table
    $promotionsTableCheck = $pdo->query("SHOW TABLES LIKE 'promotion_history'");
    if ($promotionsTableCheck->rowCount() > 0) {
        // Promotions by month
        $promotionsStmt = $pdo->prepare("
            SELECT MONTH(created_at) as month, COUNT(*) as count
            FROM promotion_history
            WHERE YEAR(created_at) = :year
            GROUP BY MONTH(created_at)
            ORDER BY MONTH(created_at) ASC
        ");
        $promotionsStmt->execute([':year' => $year]);
        $promotionsByMonth = $promotionsStmt->fetchAll();
    } else {
        $usingDummyData = true;
        // Dummy promotions data
        $promotionsByMonth = [
            ['month' => 1, 'count' => 2],
            ['month' => 2, 'count' => 3],
            ['month' => 3, 'count' => 5],
            ['month' => 4, 'count' => 4],
            ['month' => 5, 'count' => 6],
            ['month' => 6, 'count' => 8],
            ['month' => 7, 'count' => 5],
            ['month' => 8, 'count' => 3],
            ['month' => 9, 'count' => 4],
            ['month' => 10, 'count' => 6],
            ['month' => 11, 'count' => 3],
            ['month' => 12, 'count' => 2]
        ];
    }

    // Check appreciation_letters table
    $appreciationTableCheck = $pdo->query("SHOW TABLES LIKE 'appreciation_letters'");
    if ($appreciationTableCheck->rowCount() > 0) {
        // Appreciation letters by type
        $appreciationStmt = $pdo->prepare("
            SELECT issuer, COUNT(*) as count
            FROM appreciation_letters
            WHERE YEAR(issue_date) = :year
            GROUP BY issuer
        ");
        $appreciationStmt->execute([':year' => $year]);
        $appreciationByType = $appreciationStmt->fetchAll();
    } else {
        $usingDummyData = true;
        // Dummy appreciation data
        $appreciationByType = [
            ['issuer' => 'regular', 'count' => 35],
            ['issuer' => 'prime_minister', 'count' => 10]
        ];
    }

} catch (PDOException $e) {
    error_log("Get Analytics Error: " . $e->getMessage());

    // Check if it's a table not found error
    if (strpos($e->getMessage(), "doesn't exist") !== false) {
        flash('error_message', 'الجداول المطلوبة غير موجودة في قاعدة البيانات. سيتم استخدام بيانات تجريبية.', 'alert alert-warning');
        $usingDummyData = true;
    } else {
        flash('error_message', 'حدث خطأ أثناء استرجاع بيانات التحليلات: ' . $e->getMessage(), 'alert alert-danger');
    }
} catch (Exception $e) {
    error_log("Analytics General Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ عام أثناء تحميل بيانات التحليلات: ' . $e->getMessage(), 'alert alert-danger');
    $usingDummyData = true;
}
?>
