/**
 * Unified Reports & Analytics CSS
 * تصميم الصفحة الموحدة للتقارير والتحليلات
 */

/* Navigation Tabs */
.nav-tabs-custom {
    border-bottom: none;
}

.nav-tabs-custom .nav-link {
    border: none;
    background: transparent;
    color: rgba(255, 255, 255, 0.8);
    padding: 1rem 1.5rem;
    margin: 0;
    border-radius: 0;
    transition: all 0.3s ease;
    position: relative;
}

.nav-tabs-custom .nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.nav-tabs-custom .nav-link.active {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    border-bottom: 3px solid white;
}

.nav-tabs-custom .nav-link i {
    margin-right: 0.5rem;
}

/* Analytics Cards */
.analytics-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.analytics-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.analytics-card.primary::before {
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.analytics-card.success::before {
    background: linear-gradient(90deg, #10b981, #059669);
}

.analytics-card.info::before {
    background: linear-gradient(90deg, #06b6d4, #0891b2);
}

.analytics-card.warning::before {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.analytics-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.analytics-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.analytics-icon i {
    font-size: 1.5rem;
    color: #3b82f6;
}

.analytics-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.analytics-label {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
    margin-bottom: 0.75rem;
}

.analytics-trend {
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Timeline Chart */
.timeline-chart {
    padding: 1rem 0;
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 12px;
    top: 30px;
    width: 2px;
    height: 20px;
    background: #e2e8f0;
}

.timeline-marker {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 1rem;
    flex-shrink: 0;
}

.timeline-content h6 {
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
}

/* Notification Cards */
.notification-stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #3b82f6;
    transition: all 0.3s ease;
}

.notification-stat-card.unread {
    border-left-color: #ef4444;
}

.notification-stat-card.today {
    border-left-color: #10b981;
}

.notification-stat-card.week {
    border-left-color: #f59e0b;
}

.notification-stat-card.total {
    border-left-color: #8b5cf6;
}

.notification-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Notifications List */
.notifications-list {
    max-height: 600px;
    overflow-y: auto;
}

.notification-item {
    border-bottom: 1px solid #e5e7eb;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.notification-item:hover {
    background: #f9fafb;
}

.notification-item.unread {
    background: #fef3f2;
    border-left: 4px solid #ef4444;
}

.notification-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f1f5f9;
    flex-shrink: 0;
}

.notification-meta {
    flex: 1;
}

.notification-title {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
}

.notification-info {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: #64748b;
}

.notification-message {
    color: #374151;
    line-height: 1.5;
    margin-bottom: 0.75rem;
}

.notification-footer {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
}

/* Reminder Cards */
.reminder-stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #3b82f6;
    transition: all 0.3s ease;
}

.reminder-stat-card.pending {
    border-left-color: #f59e0b;
}

.reminder-stat-card.overdue {
    border-left-color: #ef4444;
}

.reminder-stat-card.today {
    border-left-color: #10b981;
}

.reminder-stat-card.completed {
    border-left-color: #6b7280;
}

.reminder-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Reminders List */
.reminders-list {
    max-height: 600px;
    overflow-y: auto;
}

.reminder-item {
    border-bottom: 1px solid #e5e7eb;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.reminder-item:hover {
    background: #f9fafb;
}

.reminder-item.completed {
    opacity: 0.7;
    background: #f8fafc;
}

.reminder-item.overdue {
    background: #fef2f2;
    border-left: 4px solid #ef4444;
}

.reminder-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.reminder-priority {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.reminder-meta {
    flex: 1;
}

.reminder-title {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
}

.reminder-info {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: #64748b;
}

.reminder-description {
    color: #374151;
    line-height: 1.5;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
}

.reminder-footer {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.reminder-actions {
    display: flex;
    gap: 0.5rem;
}

/* Report Statistics */
.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    transition: all 0.3s ease;
    text-align: center;
}

.stat-card.bg-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
    color: white;
}

.stat-card.bg-success {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    color: white;
}

.stat-card.bg-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2) !important;
    color: white;
}

.stat-card.bg-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    color: white;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-tabs-custom .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
    
    .analytics-card,
    .notification-stat-card,
    .reminder-stat-card {
        margin-bottom: 1rem;
    }
    
    .notification-header,
    .reminder-header {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .notification-info,
    .reminder-info {
        flex-direction: column;
        gap: 0.25rem;
    }
}

/* Print Styles */
@media print {
    .nav-tabs-custom,
    .notification-actions,
    .reminder-actions,
    .btn-group,
    .input-group {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
    
    .notification-item,
    .reminder-item {
        page-break-inside: avoid;
    }
}
