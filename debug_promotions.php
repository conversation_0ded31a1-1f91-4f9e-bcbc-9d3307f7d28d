<?php
/**
 * Debug Promotions
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

// Function to execute and debug a query
function debugQuery($pdo, $query, $params = []) {
    try {
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        return [
            'success' => true,
            'data' => $stmt->fetchAll(PDO::FETCH_ASSOC),
            'count' => $stmt->rowCount()
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Start HTML output
echo "<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تشخيص مشكلة الترقيات المستحقة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #0066cc; margin-top: 30px; }
        h3 { color: #666; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { padding: 8px; text-align: right; border: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .back-link { display: inline-block; margin-top: 20px; padding: 10px 15px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; }
        .query-box { background-color: #f0f8ff; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1>تشخيص مشكلة الترقيات المستحقة</h1>";

// Check if employees table exists
$employeesTableExists = $pdo->query("SHOW TABLES LIKE 'employees'")->rowCount() > 0;

if (!$employeesTableExists) {
    echo "<p class='error'>جدول الموظفين غير موجود!</p>";
} else {
    echo "<p class='success'>✓ جدول الموظفين موجود</p>";

    // Check if last_promotion_date column exists
    $columnsStmt = $pdo->query("DESCRIBE employees");
    $columns = $columnsStmt->fetchAll(PDO::FETCH_COLUMN);

    if (!in_array('last_promotion_date', $columns)) {
        echo "<p class='error'>عمود last_promotion_date غير موجود في جدول الموظفين!</p>";
    } else {
        echo "<p class='success'>✓ عمود last_promotion_date موجود في جدول الموظفين</p>";

        // Get all employees with their last promotion date
        echo "<h2>بيانات الموظفين وتواريخ آخر ترقية</h2>";

        $query = "
            SELECT e.id, e.full_name, e.current_grade, e.last_promotion_date,
                d.name as department_name,
                DATE_ADD(e.last_promotion_date, INTERVAL
                    CASE
                        WHEN e.current_grade BETWEEN 1 AND 4 THEN 5
                        ELSE 4
                    END YEAR) as next_promotion_date,
                DATEDIFF(DATE_ADD(e.last_promotion_date, INTERVAL
                    CASE
                        WHEN e.current_grade BETWEEN 1 AND 4 THEN 5
                        ELSE 4
                    END YEAR), CURDATE()) as days_until_promotion
            FROM employees e
            JOIN departments d ON e.department_id = d.id
            ORDER BY days_until_promotion ASC
        ";

        $result = debugQuery($pdo, $query);

        if ($result['success']) {
            echo "<p>عدد الموظفين: " . $result['count'] . "</p>";

            if ($result['count'] > 0) {
                echo "<table>";
                echo "<tr>
                    <th>الاسم</th>
                    <th>القسم</th>
                    <th>الدرجة</th>
                    <th>تاريخ آخر ترقية</th>
                    <th>تاريخ الترقية القادمة</th>
                    <th>الأيام المتبقية</th>
                    <th>مستحق في الأشهر الثلاثة القادمة؟</th>
                </tr>";

                $threeMonthsLater = date('Y-m-d', strtotime('+3 months'));
                $today = date('Y-m-d');
                $eligibleCount = 0;

                foreach ($result['data'] as $employee) {
                    $isEligible = ($employee['next_promotion_date'] >= $today && $employee['next_promotion_date'] <= $threeMonthsLater);

                    if ($isEligible) {
                        $eligibleCount++;
                    }

                    echo "<tr>";
                    echo "<td>" . $employee['full_name'] . "</td>";
                    echo "<td>" . $employee['department_name'] . "</td>";
                    echo "<td>" . $employee['current_grade'] . "</td>";
                    echo "<td>" . $employee['last_promotion_date'] . "</td>";
                    echo "<td>" . $employee['next_promotion_date'] . "</td>";
                    echo "<td>" . $employee['days_until_promotion'] . "</td>";
                    echo "<td class='" . ($isEligible ? 'success' : '') . "'>" . ($isEligible ? 'نعم' : 'لا') . "</td>";
                    echo "</tr>";
                }

                echo "</table>";

                echo "<p>عدد الموظفين المستحقين للترقية في الأشهر الثلاثة القادمة: <strong>" . $eligibleCount . "</strong></p>";

                // Debug the query used in dashboard.php
                echo "<h2>تشخيص استعلام لوحة المعلومات</h2>";

                $threeMonthsLater = date('Y-m-d', strtotime('+3 months'));

                echo "<div class='query-box'>";
                echo "<h3>الاستعلام المستخدم في لوحة المعلومات:</h3>";
                $dashboardQuery = "
                    SELECT e.id, e.full_name, e.current_grade, e.last_promotion_date,
                        d.name as department_name,
                        DATEDIFF(DATE_ADD(e.last_promotion_date, INTERVAL
                            CASE
                                WHEN e.current_grade BETWEEN 1 AND 4 THEN 5
                                ELSE 4
                            END YEAR), CURDATE()) as days_until_promotion
                    FROM employees e
                    JOIN departments d ON e.department_id = d.id
                    WHERE DATE_ADD(e.last_promotion_date, INTERVAL
                        CASE
                            WHEN e.current_grade BETWEEN 1 AND 4 THEN 5
                            ELSE 4
                        END YEAR) BETWEEN CURDATE() AND ?
                    ORDER BY days_until_promotion ASC
                    LIMIT 10
                ";
                echo "<pre>" . htmlspecialchars($dashboardQuery) . "</pre>";
                echo "<p>المعلمات: threeMonthsLater = $threeMonthsLater</p>";
                echo "</div>";

                $dashboardResult = debugQuery($pdo, $dashboardQuery, [$threeMonthsLater]);

                if ($dashboardResult['success']) {
                    echo "<p>عدد النتائج من استعلام لوحة المعلومات: <strong>" . $dashboardResult['count'] . "</strong></p>";

                    if ($dashboardResult['count'] > 0) {
                        echo "<table>";
                        echo "<tr>
                            <th>الاسم</th>
                            <th>القسم</th>
                            <th>الدرجة</th>
                            <th>تاريخ آخر ترقية</th>
                            <th>الأيام المتبقية</th>
                        </tr>";

                        foreach ($dashboardResult['data'] as $employee) {
                            echo "<tr>";
                            echo "<td>" . $employee['full_name'] . "</td>";
                            echo "<td>" . $employee['department_name'] . "</td>";
                            echo "<td>" . $employee['current_grade'] . "</td>";
                            echo "<td>" . $employee['last_promotion_date'] . "</td>";
                            echo "<td>" . $employee['days_until_promotion'] . "</td>";
                            echo "</tr>";
                        }

                        echo "</table>";
                    } else {
                        echo "<p class='warning'>⚠️ لا توجد نتائج من استعلام لوحة المعلومات</p>";

                        // Check if there are any NULL last_promotion_date values
                        $nullQuery = "SELECT COUNT(*) as count FROM employees WHERE last_promotion_date IS NULL";
                        $nullResult = $pdo->query($nullQuery)->fetch(PDO::FETCH_ASSOC);

                        echo "<p>عدد الموظفين بدون تاريخ آخر ترقية (NULL): " . $nullResult['count'] . "</p>";

                        // Check if there are any invalid last_promotion_date values
                        $invalidQuery = "SELECT COUNT(*) as count FROM employees WHERE last_promotion_date = '0000-00-00'";
                        $invalidResult = $pdo->query($invalidQuery)->fetch(PDO::FETCH_ASSOC);

                        echo "<p>عدد الموظفين بتاريخ آخر ترقية غير صالح (0000-00-00): " . $invalidResult['count'] . "</p>";
                    }
                } else {
                    echo "<p class='error'>خطأ في استعلام لوحة المعلومات: " . $dashboardResult['error'] . "</p>";
                }
            } else {
                echo "<p class='warning'>⚠️ لا يوجد موظفين في النظام</p>";
            }
        } else {
            echo "<p class='error'>خطأ: " . $result['error'] . "</p>";
        }
    }
}

// Add links to navigate
echo "
    <div style='margin-top: 30px;'>
        <a href='dashboard.php' class='back-link'>العودة إلى لوحة المعلومات</a>
    </div>
</body>
</html>";
?>
