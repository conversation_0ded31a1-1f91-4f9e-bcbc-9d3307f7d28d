<?php
/**
 * Debug Reports
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

// Function to check if table exists
function tableExists($pdo, $tableName) {
    $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
    return $stmt->rowCount() > 0;
}

// Function to get table data
function getTableData($pdo, $tableName, $limit = 10) {
    try {
        $stmt = $pdo->query("SELECT * FROM $tableName LIMIT $limit");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return ['error' => $e->getMessage()];
    }
}

// Function to get employee details
function getEmployeeDetails($pdo, $employeeId) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM employees WHERE id = ?");
        $stmt->execute([$employeeId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return ['error' => $e->getMessage()];
    }
}

// Function to get user details
function getUserDetails($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return ['error' => $e->getMessage()];
    }
}

// Function to execute and debug a query
function debugQuery($pdo, $query, $params = []) {
    try {
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        return [
            'success' => true,
            'data' => $stmt->fetchAll(PDO::FETCH_ASSOC),
            'count' => $stmt->rowCount()
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Start HTML output
echo "<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تشخيص مشاكل التقارير</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #0066cc; margin-top: 30px; }
        h3 { color: #666; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { padding: 8px; text-align: right; border: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .back-link { display: inline-block; margin-top: 20px; padding: 10px 15px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; }
        .query-box { background-color: #f0f8ff; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1>تشخيص مشاكل التقارير</h1>";

// Check tables
$tables = ['allowances', 'promotions', 'appreciation_letters'];
foreach ($tables as $table) {
    echo "<h2>جدول $table</h2>";
    
    if (tableExists($pdo, $table)) {
        echo "<p class='success'>✓ الجدول موجود</p>";
        
        // Get table data
        $data = getTableData($pdo, $table);
        
        if (isset($data['error'])) {
            echo "<p class='error'>خطأ: " . $data['error'] . "</p>";
        } else {
            echo "<h3>البيانات الموجودة في الجدول:</h3>";
            
            if (empty($data)) {
                echo "<p class='warning'>⚠️ لا توجد بيانات في الجدول</p>";
            } else {
                echo "<table>";
                echo "<tr>";
                foreach (array_keys($data[0]) as $key) {
                    echo "<th>$key</th>";
                }
                echo "</tr>";
                
                foreach ($data as $row) {
                    echo "<tr>";
                    foreach ($row as $key => $value) {
                        if ($key === 'employee_id' && !empty($value)) {
                            $employee = getEmployeeDetails($pdo, $value);
                            echo "<td>" . $value . " (" . ($employee['full_name'] ?? 'غير موجود') . ")</td>";
                        } elseif ($key === 'created_by' && !empty($value)) {
                            $user = getUserDetails($pdo, $value);
                            echo "<td>" . $value . " (" . ($user['full_name'] ?? 'غير موجود') . ")</td>";
                        } else {
                            echo "<td>" . (is_null($value) ? "NULL" : htmlspecialchars($value)) . "</td>";
                        }
                    }
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
    } else {
        echo "<p class='error'>✗ الجدول غير موجود</p>";
    }
}

// Debug allowances_monthly report
echo "<h2>تشخيص تقرير العلاوات الشهرية</h2>";

$currentYear = date('Y');
$currentMonth = date('m');
$startDate = $currentYear . '-' . $currentMonth . '-01';
$endDate = date('Y-m-t', strtotime($startDate));

echo "<div class='query-box'>";
echo "<h3>الاستعلام:</h3>";
$query = "
    SELECT a.*, e.employee_number, e.full_name, d.name as department_name, u.full_name as created_by_name
    FROM allowances a
    JOIN employees e ON a.employee_id = e.id
    JOIN departments d ON e.department_id = d.id
    JOIN users u ON a.created_by = u.id
    WHERE a.allowance_date BETWEEN :start_date AND :end_date
";
echo "<pre>" . htmlspecialchars($query) . "</pre>";
echo "<p>المعلمات: start_date = $startDate, end_date = $endDate</p>";
echo "</div>";

$result = debugQuery($pdo, $query, [':start_date' => $startDate, ':end_date' => $endDate]);

if ($result['success']) {
    echo "<p class='success'>✓ تم تنفيذ الاستعلام بنجاح</p>";
    echo "<p>عدد النتائج: " . $result['count'] . "</p>";
    
    if ($result['count'] > 0) {
        echo "<h3>النتائج:</h3>";
        echo "<table>";
        echo "<tr>";
        foreach (array_keys($result['data'][0]) as $key) {
            echo "<th>$key</th>";
        }
        echo "</tr>";
        
        foreach ($result['data'] as $row) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . (is_null($value) ? "NULL" : htmlspecialchars($value)) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ لا توجد نتائج للشهر الحالي</p>";
        
        // Check if there are any allowances in other months
        echo "<h3>التحقق من وجود علاوات في أشهر أخرى:</h3>";
        $query = "
            SELECT 
                YEAR(allowance_date) as year, 
                MONTH(allowance_date) as month, 
                COUNT(*) as count
            FROM allowances
            GROUP BY YEAR(allowance_date), MONTH(allowance_date)
            ORDER BY YEAR(allowance_date) DESC, MONTH(allowance_date) DESC
        ";
        
        $monthsResult = debugQuery($pdo, $query);
        
        if ($monthsResult['success'] && $monthsResult['count'] > 0) {
            echo "<table>";
            echo "<tr><th>السنة</th><th>الشهر</th><th>عدد العلاوات</th></tr>";
            
            foreach ($monthsResult['data'] as $row) {
                echo "<tr>";
                echo "<td>" . $row['year'] . "</td>";
                echo "<td>" . $row['month'] . "</td>";
                echo "<td>" . $row['count'] . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
            
            // Get sample allowance
            $sampleQuery = "SELECT * FROM allowances LIMIT 1";
            $sampleResult = debugQuery($pdo, $sampleQuery);
            
            if ($sampleResult['success'] && $sampleResult['count'] > 0) {
                echo "<h3>نموذج علاوة:</h3>";
                echo "<pre>" . print_r($sampleResult['data'][0], true) . "</pre>";
            }
        } else {
            echo "<p class='error'>لا توجد علاوات في أي شهر</p>";
        }
    }
} else {
    echo "<p class='error'>خطأ: " . $result['error'] . "</p>";
}

// Debug promotions_monthly report
echo "<h2>تشخيص تقرير الترفيعات الشهرية</h2>";

echo "<div class='query-box'>";
echo "<h3>الاستعلام:</h3>";
$query = "
    SELECT p.*, e.employee_number, e.full_name, d.name as department_name, u.full_name as created_by_name
    FROM promotions p
    JOIN employees e ON p.employee_id = e.id
    JOIN departments d ON e.department_id = d.id
    JOIN users u ON p.created_by = u.id
    WHERE p.promotion_date BETWEEN :start_date AND :end_date
";
echo "<pre>" . htmlspecialchars($query) . "</pre>";
echo "<p>المعلمات: start_date = $startDate, end_date = $endDate</p>";
echo "</div>";

$result = debugQuery($pdo, $query, [':start_date' => $startDate, ':end_date' => $endDate]);

if ($result['success']) {
    echo "<p class='success'>✓ تم تنفيذ الاستعلام بنجاح</p>";
    echo "<p>عدد النتائج: " . $result['count'] . "</p>";
    
    if ($result['count'] > 0) {
        echo "<h3>النتائج:</h3>";
        echo "<table>";
        echo "<tr>";
        foreach (array_keys($result['data'][0]) as $key) {
            echo "<th>$key</th>";
        }
        echo "</tr>";
        
        foreach ($result['data'] as $row) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . (is_null($value) ? "NULL" : htmlspecialchars($value)) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ لا توجد نتائج للشهر الحالي</p>";
        
        // Check if there are any promotions in other months
        echo "<h3>التحقق من وجود ترفيعات في أشهر أخرى:</h3>";
        $query = "
            SELECT 
                YEAR(promotion_date) as year, 
                MONTH(promotion_date) as month, 
                COUNT(*) as count
            FROM promotions
            GROUP BY YEAR(promotion_date), MONTH(promotion_date)
            ORDER BY YEAR(promotion_date) DESC, MONTH(promotion_date) DESC
        ";
        
        $monthsResult = debugQuery($pdo, $query);
        
        if ($monthsResult['success'] && $monthsResult['count'] > 0) {
            echo "<table>";
            echo "<tr><th>السنة</th><th>الشهر</th><th>عدد الترفيعات</th></tr>";
            
            foreach ($monthsResult['data'] as $row) {
                echo "<tr>";
                echo "<td>" . $row['year'] . "</td>";
                echo "<td>" . $row['month'] . "</td>";
                echo "<td>" . $row['count'] . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
            
            // Get sample promotion
            $sampleQuery = "SELECT * FROM promotions LIMIT 1";
            $sampleResult = debugQuery($pdo, $sampleQuery);
            
            if ($sampleResult['success'] && $sampleResult['count'] > 0) {
                echo "<h3>نموذج ترفيع:</h3>";
                echo "<pre>" . print_r($sampleResult['data'][0], true) . "</pre>";
            }
        } else {
            echo "<p class='error'>لا توجد ترفيعات في أي شهر</p>";
        }
    }
} else {
    echo "<p class='error'>خطأ: " . $result['error'] . "</p>";
}

// Add links to navigate
echo "
    <div style='margin-top: 30px;'>
        <a href='reports.php' class='back-link'>العودة إلى صفحة التقارير</a>
        <a href='create_tables_and_data.php' class='back-link' style='margin-right: 10px;'>إنشاء الجداول والبيانات</a>
    </div>
</body>
</html>";
?>
