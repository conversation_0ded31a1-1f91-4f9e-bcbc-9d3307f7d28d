<?php
/**
 * Alerts Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();

// Get filter parameters
$type = isset($_GET['type']) ? sanitize($_GET['type']) : '';
$status = isset($_GET['status']) ? sanitize($_GET['status']) : '';

// Build query
$query = "
    SELECT a.*, e.full_name, e.employee_number
    FROM alerts a
    JOIN employees e ON a.employee_id = e.id
    WHERE 1=1
";

$params = [];

if (!empty($type)) {
    $query .= " AND a.type = :type";
    $params[':type'] = $type;
}

if ($status === 'read') {
    $query .= " AND a.is_read = 1";
} elseif ($status === 'unread') {
    $query .= " AND a.is_read = 0";
}

$query .= " ORDER BY a.alert_date ASC";

// Get alerts
try {
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $alerts = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Alerts Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع التنبيهات', 'alert alert-danger');
    $alerts = [];
}

// Mark alert as read
if (isset($_GET['mark_read']) && is_numeric($_GET['mark_read'])) {
    $alertId = (int)$_GET['mark_read'];
    
    try {
        $stmt = $pdo->prepare("UPDATE alerts SET is_read = 1 WHERE id = :id");
        $stmt->execute([':id' => $alertId]);
        
        flash('success_message', 'تم تحديث حالة التنبيه بنجاح', 'alert alert-success');
        redirect('alerts.php');
    } catch (PDOException $e) {
        error_log("Mark Alert Read Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء تحديث حالة التنبيه', 'alert alert-danger');
    }
}

// Helper function to translate alert type to Arabic
function translateAlertType($type) {
    $translations = [
        'allowance' => 'علاوة سنوية',
        'promotion' => 'ترفيع وظيفي',
        'retirement' => 'تقاعد',
        'appreciation_limit' => 'حد كتب الشكر'
    ];
    
    return isset($translations[$type]) ? $translations[$type] : $type;
}

// Count alerts by type
$alertCounts = [
    'total' => count($alerts),
    'unread' => 0,
    'allowance' => 0,
    'promotion' => 0,
    'retirement' => 0,
    'appreciation_limit' => 0
];

foreach ($alerts as $alert) {
    if (!$alert['is_read']) {
        $alertCounts['unread']++;
    }
    
    if (isset($alertCounts[$alert['type']])) {
        $alertCounts[$alert['type']]++;
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-bell me-2"></i> التنبيهات والإشعارات
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى الرئيسية
        </a>
    </div>
</div>

<!-- Alerts Summary -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3 class="mb-0"><?php echo $alertCounts['total']; ?></h3>
                <p class="mb-0">إجمالي التنبيهات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h3 class="mb-0"><?php echo $alertCounts['unread']; ?></h3>
                <p class="mb-0">تنبيهات غير مقروءة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-dark">
            <div class="card-body text-center">
                <h3 class="mb-0"><?php echo $alertCounts['allowance'] + $alertCounts['promotion']; ?></h3>
                <p class="mb-0">علاوات وترفيعات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3 class="mb-0"><?php echo $alertCounts['retirement']; ?></h3>
                <p class="mb-0">تقاعد</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="get" class="row g-3">
            <div class="col-md-4">
                <label for="type" class="form-label">نوع التنبيه</label>
                <select class="form-select" id="type" name="type">
                    <option value="">الكل</option>
                    <option value="allowance" <?php echo $type === 'allowance' ? 'selected' : ''; ?>>علاوة سنوية</option>
                    <option value="promotion" <?php echo $type === 'promotion' ? 'selected' : ''; ?>>ترفيع وظيفي</option>
                    <option value="retirement" <?php echo $type === 'retirement' ? 'selected' : ''; ?>>تقاعد</option>
                    <option value="appreciation_limit" <?php echo $type === 'appreciation_limit' ? 'selected' : ''; ?>>حد كتب الشكر</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">الكل</option>
                    <option value="read" <?php echo $status === 'read' ? 'selected' : ''; ?>>مقروء</option>
                    <option value="unread" <?php echo $status === 'unread' ? 'selected' : ''; ?>>غير مقروء</option>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter me-1"></i> تصفية
                </button>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-secondary">
                    <i class="fas fa-redo me-1"></i> إعادة تعيين
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Alerts Table -->
<div class="card">
    <div class="card-body">
        <?php if (count($alerts) > 0): ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>النوع</th>
                            <th>الموظف</th>
                            <th>التاريخ</th>
                            <th>الرسالة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($alerts as $alert): ?>
                            <tr class="<?php echo $alert['is_read'] ? '' : 'table-warning'; ?>">
                                <td>
                                    <?php if ($alert['type'] === 'allowance'): ?>
                                        <span class="badge bg-success">علاوة</span>
                                    <?php elseif ($alert['type'] === 'promotion'): ?>
                                        <span class="badge bg-primary">ترفيع</span>
                                    <?php elseif ($alert['type'] === 'retirement'): ?>
                                        <span class="badge bg-danger">تقاعد</span>
                                    <?php elseif ($alert['type'] === 'appreciation_limit'): ?>
                                        <span class="badge bg-info">كتب شكر</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="employee_details.php?id=<?php echo $alert['employee_id']; ?>">
                                        <?php echo $alert['full_name']; ?> (<?php echo $alert['employee_number']; ?>)
                                    </a>
                                </td>
                                <td><?php echo formatArabicDate($alert['alert_date']); ?></td>
                                <td><?php echo $alert['message']; ?></td>
                                <td>
                                    <?php if ($alert['is_read']): ?>
                                        <span class="badge bg-secondary">مقروء</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning text-dark">غير مقروء</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!$alert['is_read']): ?>
                                        <a href="<?php echo $_SERVER['PHP_SELF'] . '?mark_read=' . $alert['id']; ?>" class="btn btn-sm btn-success">
                                            <i class="fas fa-check"></i> تحديد كمقروء
                                        </a>
                                    <?php endif; ?>
                                    <a href="employee_details.php?id=<?php echo $alert['employee_id']; ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-user"></i> عرض الموظف
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                لا توجد تنبيهات متاحة.
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
