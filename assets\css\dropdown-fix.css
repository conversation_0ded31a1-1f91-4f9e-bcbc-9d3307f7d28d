/**
 * Dropdown Position Fix
 * إصلاح موضع القوائم المنسدلة لتظهر خارج شريط التنقل
 */

/* إصلاح شريط التنقل الأساسي */
.navbar {
    overflow: visible !important;
    position: relative !important;
    z-index: 1030 !important;
}

.navbar .container,
.navbar .container-fluid {
    overflow: visible !important;
    position: relative !important;
}

.navbar-nav {
    overflow: visible !important;
    position: relative !important;
}

.navbar .nav-item {
    overflow: visible !important;
    position: relative !important;
}

/* إصلاح القوائم المنسدلة الأساسي */
.dropdown {
    position: relative !important;
    overflow: visible !important;
}

.dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    z-index: 9999 !important;
    display: none !important;
    float: none !important;
    min-width: 200px !important;
    padding: 0.5rem !important;
    margin: 0.5rem 0 0 !important;
    font-size: 1rem !important;
    color: #212529 !important;
    text-align: right !important;
    list-style: none !important;
    background-color: rgba(255, 255, 255, 0.98) !important;
    background-clip: padding-box !important;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
    backdrop-filter: blur(10px) !important;
    transform: none !important;
}

.dropdown-menu.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* إصلاح القوائم المنسدلة في شريط التنقل */
.navbar .dropdown-menu {
    position: absolute !important;
    top: calc(100% + 0.5rem) !important;
    left: auto !important;
    right: 0 !important;
    margin-top: 0 !important;
    transform: none !important;
    float: none !important;
}

/* إصلاح قائمة الإدارة */
.navbar #adminDropdown {
    position: relative !important;
}

.navbar #adminDropdown + .dropdown-menu,
.navbar .dropdown:has(#adminDropdown) .dropdown-menu {
    right: auto !important;
    left: 0 !important;
    top: calc(100% + 0.5rem) !important;
    position: absolute !important;
    transform: none !important;
}

/* إصلاح قائمة المستخدم */
.navbar #userDropdown {
    position: relative !important;
}

.navbar #userDropdown + .dropdown-menu,
.navbar .dropdown:has(#userDropdown) .dropdown-menu {
    right: 0 !important;
    left: auto !important;
    top: calc(100% + 0.5rem) !important;
    position: absolute !important;
    transform: none !important;
}

/* إصلاح قائمة الإشعارات */
.navbar #notificationsDropdown {
    position: relative !important;
}

.navbar #notificationsContainer {
    right: 0 !important;
    left: auto !important;
    top: calc(100% + 0.5rem) !important;
    position: absolute !important;
    transform: none !important;
    width: 320px !important;
    max-height: 400px !important;
    overflow-y: auto !important;
}

/* إصلاح عناصر القائمة المنسدلة */
.dropdown-item {
    display: block !important;
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    clear: both !important;
    font-weight: 400 !important;
    color: #374151 !important;
    text-align: inherit !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    background-color: transparent !important;
    border: 0 !important;
    border-radius: 8px !important;
    margin-bottom: 0.25rem !important;
    transition: all 0.3s ease !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
    color: white !important;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
    transform: translateX(4px) !important;
}

.dropdown-item i {
    margin-left: 0.5rem !important;
    color: #3b82f6 !important;
    transition: color 0.3s ease !important;
}

.dropdown-item:hover i,
.dropdown-item:focus i {
    color: white !important;
}

/* إصلاح الفاصل */
.dropdown-divider {
    height: 0 !important;
    margin: 0.5rem 0 !important;
    overflow: hidden !important;
    border-top: 1px solid #e9ecef !important;
}

/* إصلاح للشاشات الصغيرة */
@media (max-width: 768px) {
    .navbar .dropdown-menu {
        position: absolute !important;
        top: calc(100% + 0.25rem) !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        max-width: 300px !important;
        margin: 0 auto !important;
        transform: none !important;
    }
    
    .navbar #userDropdown + .dropdown-menu,
    .navbar #notificationsContainer {
        left: 50% !important;
        right: auto !important;
        transform: translateX(-50%) !important;
        width: 280px !important;
    }
    
    .navbar #adminDropdown + .dropdown-menu {
        left: 0 !important;
        right: 0 !important;
        transform: none !important;
        width: 100% !important;
        max-width: 300px !important;
        margin: 0 auto !important;
    }
}

/* إصلاح إضافي للتأكد من الظهور */
.navbar-nav .dropdown:hover .dropdown-menu,
.navbar-nav .dropdown.show .dropdown-menu {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* إصلاح z-index للتأكد من الظهور فوق العناصر الأخرى */
.navbar {
    z-index: 1030 !important;
}

.dropdown-menu {
    z-index: 9999 !important;
}

/* إصلاح للتأكد من عدم قطع القوائم */
body {
    overflow-x: visible !important;
}

.container,
.container-fluid {
    overflow: visible !important;
}

/* إصلاح خاص للقوائم المنسدلة في Bootstrap */
.navbar .dropdown-toggle::after {
    display: inline-block !important;
    margin-right: 0.255em !important;
    vertical-align: 0.255em !important;
    content: "" !important;
    border-top: 0.3em solid !important;
    border-left: 0.3em solid transparent !important;
    border-bottom: 0 !important;
    border-right: 0.3em solid transparent !important;
}

/* إصلاح تأثيرات الحركة */
.dropdown-menu {
    transition: all 0.3s ease !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(-10px) !important;
}

.dropdown-menu.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
}

/* إصلاح للتأكد من عمل القوائم مع JavaScript */
.dropdown.show .dropdown-menu {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
}

/* إصلاح خاص لقائمة الإشعارات */
#notificationsContainer.dropdown-menu {
    padding: 0 !important;
}

#notificationsContainer .text-center {
    padding: 1rem !important;
}

/* إصلاح للتأكد من الاتجاه الصحيح للنص العربي */
.dropdown-menu {
    direction: rtl !important;
    text-align: right !important;
}

.dropdown-item {
    direction: rtl !important;
    text-align: right !important;
}
