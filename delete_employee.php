<?php
/**
 * Delete Employee Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and proper role
requireRole(['admin', 'hr']);

// Check if employee ID is provided
$employeeId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($employeeId <= 0) {
    flash('error_message', 'معرف الموظف غير صحيح', 'alert alert-danger');
    redirect('employees.php');
}

// Get employee details
try {
    $stmt = $pdo->prepare("SELECT * FROM employees WHERE id = :id");
    $stmt->execute([':id' => $employeeId]);
    $employee = $stmt->fetch();

    if (!$employee) {
        flash('error_message', 'الموظف غير موجود', 'alert alert-danger');
        redirect('employees.php');
    }
} catch (PDOException $e) {
    error_log("Get Employee Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الموظف', 'alert alert-danger');
    redirect('employees.php');
}

// Check if employee has related records
try {
    // Check allowances
    $allowancesStmt = $pdo->prepare("SELECT COUNT(*) as count FROM allowances WHERE employee_id = :id");
    $allowancesStmt->execute([':id' => $employeeId]);
    $allowancesCount = $allowancesStmt->fetch()['count'];

    // Check promotions
    $promotionsStmt = $pdo->prepare("SELECT COUNT(*) as count FROM promotions WHERE employee_id = :id");
    $promotionsStmt->execute([':id' => $employeeId]);
    $promotionsCount = $promotionsStmt->fetch()['count'];

    // Check appreciation letters
    $lettersStmt = $pdo->prepare("SELECT COUNT(*) as count FROM appreciation_letters WHERE employee_id = :id");
    $lettersStmt->execute([':id' => $employeeId]);
    $lettersCount = $lettersStmt->fetch()['count'];

    // Check salary history
    $salaryHistoryStmt = $pdo->prepare("SELECT COUNT(*) as count FROM salary_history WHERE employee_id = :id");
    $salaryHistoryStmt->execute([':id' => $employeeId]);
    $salaryHistoryCount = $salaryHistoryStmt->fetch()['count'];

    // Check higher degree history
    $degreeHistoryStmt = $pdo->prepare("SELECT COUNT(*) as count FROM higher_degree_history WHERE employee_id = :id");
    $degreeHistoryStmt->execute([':id' => $employeeId]);
    $degreeHistoryCount = $degreeHistoryStmt->fetch()['count'];

    // Check alerts
    $alertsStmt = $pdo->prepare("SELECT COUNT(*) as count FROM alerts WHERE employee_id = :id");
    $alertsStmt->execute([':id' => $employeeId]);
    $alertsCount = $alertsStmt->fetch()['count'];

    $hasRelatedRecords = ($allowancesCount > 0 || $promotionsCount > 0 || $lettersCount > 0 ||
                          $salaryHistoryCount > 0 || $degreeHistoryCount > 0 || $alertsCount > 0);

} catch (PDOException $e) {
    error_log("Check Related Records Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء التحقق من السجلات المرتبطة', 'alert alert-danger');
    redirect('employees.php');
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['confirm_delete']) && $_POST['confirm_delete'] === 'yes') {
        try {
            // Start transaction
            $pdo->beginTransaction();

            // Delete related records if requested
            if (isset($_POST['delete_related']) && $_POST['delete_related'] === 'yes') {
                // Delete allowances
                if ($allowancesCount > 0) {
                    $deleteAllowancesStmt = $pdo->prepare("DELETE FROM allowances WHERE employee_id = :id");
                    $deleteAllowancesStmt->execute([':id' => $employeeId]);
                }

                // Delete promotions
                if ($promotionsCount > 0) {
                    $deletePromotionsStmt = $pdo->prepare("DELETE FROM promotions WHERE employee_id = :id");
                    $deletePromotionsStmt->execute([':id' => $employeeId]);
                }

                // Delete appreciation letters
                if ($lettersCount > 0) {
                    // Get file paths first to delete files
                    $filePathsStmt = $pdo->prepare("SELECT file_path FROM appreciation_letters WHERE employee_id = :id AND file_path IS NOT NULL");
                    $filePathsStmt->execute([':id' => $employeeId]);
                    $filePaths = $filePathsStmt->fetchAll(PDO::FETCH_COLUMN);

                    // Delete files
                    foreach ($filePaths as $filePath) {
                        if (file_exists($filePath)) {
                            @unlink($filePath);
                        }
                    }

                    // Delete records
                    $deleteLettersStmt = $pdo->prepare("DELETE FROM appreciation_letters WHERE employee_id = :id");
                    $deleteLettersStmt->execute([':id' => $employeeId]);
                }

                // Delete salary history
                if ($salaryHistoryCount > 0) {
                    $deleteSalaryHistoryStmt = $pdo->prepare("DELETE FROM salary_history WHERE employee_id = :id");
                    $deleteSalaryHistoryStmt->execute([':id' => $employeeId]);
                }

                // Delete higher degree history
                if ($degreeHistoryCount > 0) {
                    $deleteDegreeHistoryStmt = $pdo->prepare("DELETE FROM higher_degree_history WHERE employee_id = :id");
                    $deleteDegreeHistoryStmt->execute([':id' => $employeeId]);
                }

                // Delete alerts
                if ($alertsCount > 0) {
                    $deleteAlertsStmt = $pdo->prepare("DELETE FROM alerts WHERE employee_id = :id");
                    $deleteAlertsStmt->execute([':id' => $employeeId]);
                }
            }

            // Delete employee
            $deleteEmployeeStmt = $pdo->prepare("DELETE FROM employees WHERE id = :id");
            $deleteEmployeeStmt->execute([':id' => $employeeId]);

            // Log activity
            logActivity(
                $_SESSION['user_id'],
                'حذف موظف',
                'employees',
                $employeeId,
                'تم حذف الموظف: ' . $employee['full_name']
            );

            // Commit transaction
            $pdo->commit();

            // Redirect to employees page
            flash('success_message', 'تم حذف الموظف بنجاح', 'alert alert-success');
            redirect('employees.php');

        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();

            error_log("Delete Employee Error: " . $e->getMessage());
            flash('error_message', 'حدث خطأ أثناء حذف الموظف: ' . $e->getMessage(), 'alert alert-danger');
        }
    } else {
        flash('error_message', 'لم يتم تأكيد الحذف', 'alert alert-danger');
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-trash me-2"></i> حذف موظف
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="employee_details.php?id=<?php echo $employeeId; ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى تفاصيل الموظف
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">
            <i class="fas fa-exclamation-triangle me-2"></i> تأكيد الحذف
        </h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تحذير:</strong> أنت على وشك حذف الموظف <strong><?php echo $employee['full_name']; ?></strong> (الرقم الوظيفي: <?php echo $employee['employee_number']; ?>).
            <br>
            هذا الإجراء لا يمكن التراجع عنه.
        </div>

        <?php if ($hasRelatedRecords): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong>تنبيه:</strong> هذا الموظف مرتبط بالسجلات التالية:
                <ul class="mb-0 mt-2">
                    <?php if ($allowancesCount > 0): ?>
                        <li><?php echo $allowancesCount; ?> سجل علاوات</li>
                    <?php endif; ?>

                    <?php if ($promotionsCount > 0): ?>
                        <li><?php echo $promotionsCount; ?> سجل ترفيعات</li>
                    <?php endif; ?>

                    <?php if ($lettersCount > 0): ?>
                        <li><?php echo $lettersCount; ?> سجل كتب شكر</li>
                    <?php endif; ?>

                    <?php if ($salaryHistoryCount > 0): ?>
                        <li><?php echo $salaryHistoryCount; ?> سجل تاريخ الراتب</li>
                    <?php endif; ?>

                    <?php if ($degreeHistoryCount > 0): ?>
                        <li><?php echo $degreeHistoryCount; ?> سجل تاريخ الشهادات العليا</li>
                    <?php endif; ?>

                    <?php if ($alertsCount > 0): ?>
                        <li><?php echo $alertsCount; ?> سجل تنبيهات</li>
                    <?php endif; ?>
                </ul>
            </div>
        <?php endif; ?>

        <form action="<?php echo $_SERVER['PHP_SELF'] . '?id=' . $employeeId; ?>" method="post">
            <?php if ($hasRelatedRecords): ?>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="delete_related" name="delete_related" value="yes">
                        <label class="form-check-label" for="delete_related">
                            حذف جميع السجلات المرتبطة بالموظف
                        </label>
                    </div>
                    <div class="form-text text-danger">
                        <i class="fas fa-exclamation-circle me-1"></i>
                        تحذير: سيتم حذف جميع السجلات المرتبطة بالموظف بشكل نهائي.
                    </div>
                </div>
            <?php endif; ?>

            <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="confirm_delete" name="confirm_delete" value="yes" required>
                    <label class="form-check-label" for="confirm_delete">
                        أؤكد أنني أريد حذف هذا الموظف<?php echo $hasRelatedRecords ? ' وجميع السجلات المرتبطة به (إذا تم تحديد الخيار أعلاه)' : ''; ?>
                    </label>
                </div>
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-danger px-5" id="delete_button" disabled>
                    <i class="fas fa-trash me-1"></i> حذف الموظف
                </button>
                <a href="employee_details.php?id=<?php echo $employeeId; ?>" class="btn btn-secondary px-5">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<script>
    // Enable/disable delete button based on confirmation checkbox
    document.getElementById('confirm_delete').addEventListener('change', function() {
        document.getElementById('delete_button').disabled = !this.checked;
    });

    <?php if ($hasRelatedRecords): ?>
    // Show warning if user tries to delete without checking delete_related
    document.querySelector('form').addEventListener('submit', function(e) {
        if (!document.getElementById('delete_related').checked) {
            if (!confirm('أنت لم تقم بتحديد خيار حذف السجلات المرتبطة. هذا سيؤدي إلى فشل عملية الحذف. هل تريد المتابعة؟')) {
                e.preventDefault();
            }
        }
    });
    <?php endif; ?>
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
