-- تعديلات قاعدة البيانات لنظام إدارة العلاوات والترقية وكتب الشكر
-- وفقًا للمتطلبات الجديدة

-- إضافة جدول المراحل (Stages)
CREATE TABLE IF NOT EXISTS `stages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `grade_id` int(11) NOT NULL,
  `stage_number` int(11) NOT NULL,
  `nominal_salary` decimal(10,2) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `grade_stage` (`grade_id`, `stage_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدخال بيانات المراحل والرواتب الاسمية
INSERT INTO `stages` (`grade_id`, `stage_number`, `nominal_salary`) VALUES
-- الدرجة 1
(1, 1, 910),
(1, 2, 940),
(1, 3, 970),
(1, 4, 1000),
(1, 5, 1030),
(1, 6, 1060),
(1, 7, 1100),
-- الدرجة 2
(2, 1, 723),
(2, 2, 753),
(2, 3, 783),
(2, 4, 813),
(2, 5, 843),
(2, 6, 873),
(2, 7, 893),
-- الدرجة 3
(3, 1, 600),
(3, 2, 620),
(3, 3, 640),
(3, 4, 660),
(3, 5, 680),
(3, 6, 700),
(3, 7, 710),
-- الدرجة 4
(4, 1, 509),
(4, 2, 519),
(4, 3, 529),
(4, 4, 539),
(4, 5, 559),
(4, 6, 579),
(4, 7, 589),
-- الدرجة 5
(5, 1, 429),
(5, 2, 439),
(5, 3, 449),
(5, 4, 459),
(5, 5, 469),
(5, 6, 479),
(5, 7, 489),
-- الدرجة 6
(6, 1, 362),
(6, 2, 372),
(6, 3, 382),
(6, 4, 392),
(6, 5, 402),
(6, 6, 422),
(6, 7, 432),
-- الدرجة 7
(7, 1, 292),
(7, 2, 302),
(7, 3, 312),
(7, 4, 322),
(7, 5, 332),
(7, 6, 346),
(7, 7, 356),
-- الدرجة 8
(8, 1, 250),
(8, 2, 255),
(8, 3, 260),
(8, 4, 270),
(8, 5, 275),
(8, 6, 285),
(8, 7, 290),
-- الدرجة 9
(9, 1, 192),
(9, 2, 202),
(9, 3, 212),
(9, 4, 222),
(9, 5, 227),
(9, 6, 237),
(9, 7, 242),
-- الدرجة 10
(10, 1, 160),
(10, 2, 165),
(10, 3, 170),
(10, 4, 175),
(10, 5, 180),
(10, 6, 186),
(10, 7, 191);

-- تعديل جدول الموظفين لإضافة الحقول الجديدة
ALTER TABLE `employees`
ADD COLUMN `birth_date` date DEFAULT NULL AFTER `hire_date`,
ADD COLUMN `current_stage` int(11) NOT NULL DEFAULT 1 AFTER `current_grade`,
ADD COLUMN `nominal_salary` decimal(10,2) DEFAULT NULL AFTER `current_stage`,
ADD COLUMN `retirement_date` date DEFAULT NULL AFTER `birth_date`,
ADD COLUMN `early_retirement_eligible` tinyint(1) NOT NULL DEFAULT 0 AFTER `retirement_date`,
ADD COLUMN `higher_degree_date` date DEFAULT NULL AFTER `education_level_id`,
ADD COLUMN `previous_grade` int(11) DEFAULT NULL AFTER `higher_degree_date`,
ADD COLUMN `law_103_applied` tinyint(1) NOT NULL DEFAULT 0 AFTER `previous_grade`,
ADD COLUMN `appreciation_letters_count` int(11) NOT NULL DEFAULT 0 AFTER `allowances_in_current_grade`,
ADD COLUMN `max_grade_by_education` int(11) DEFAULT NULL AFTER `law_103_applied`;

-- إضافة جدول سجل الرواتب
CREATE TABLE IF NOT EXISTS `salary_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `grade` int(11) NOT NULL,
  `stage` int(11) NOT NULL,
  `nominal_salary` decimal(10,2) NOT NULL,
  `effective_date` date NOT NULL,
  `reason` enum('initial','allowance','promotion','higher_degree') NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `salary_history_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `salary_history_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة جدول سجل الشهادات العليا
CREATE TABLE IF NOT EXISTS `higher_degree_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `education_level_id` int(11) NOT NULL,
  `previous_grade` int(11) NOT NULL,
  `new_grade` int(11) NOT NULL,
  `degree_date` date NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `education_level_id` (`education_level_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `higher_degree_history_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `higher_degree_history_ibfk_2` FOREIGN KEY (`education_level_id`) REFERENCES `education_levels` (`id`),
  CONSTRAINT `higher_degree_history_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- تعديل جدول إعدادات النظام لإضافة الإعدادات الجديدة
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('retirement_age', '60', 'العمر القانوني للتقاعد'),
('early_retirement_years', '25', 'عدد سنوات الخدمة المطلوبة للتقاعد المبكر'),
('max_appreciation_letters', '3', 'الحد الأقصى لكتب الشكر سنوياً'),
('law_103_promotion_years', '2', 'عدد سنوات الترقية وفق قانون 103');

-- تعديل جدول المستويات التعليمية لإضافة الحد الأقصى للدرجة
ALTER TABLE `education_levels`
ADD COLUMN `min_grade` int(11) NOT NULL DEFAULT 10 AFTER `max_grade`;

-- تحديث بيانات المستويات التعليمية
UPDATE `education_levels` SET `min_grade` = 7 WHERE `name` = 'بكالوريوس';
UPDATE `education_levels` SET `min_grade` = 5 WHERE `name` = 'دبلوم';
UPDATE `education_levels` SET `min_grade` = 9 WHERE `name` = 'ثانوية عامة';
UPDATE `education_levels` SET `min_grade` = 10 WHERE `name` = 'متوسطة';
UPDATE `education_levels` SET `min_grade` = 10 WHERE `name` = 'ابتدائية';
UPDATE `education_levels` SET `min_grade` = 3 WHERE `name` = 'ماجستير';
UPDATE `education_levels` SET `min_grade` = 1 WHERE `name` = 'دكتوراه';

-- إضافة جدول التنبيهات
CREATE TABLE IF NOT EXISTS `alerts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('allowance','promotion','retirement','appreciation_limit') NOT NULL,
  `employee_id` int(11) NOT NULL,
  `alert_date` date NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  CONSTRAINT `alerts_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- تعديل جدول كتب الشكر لإضافة حقل للسنة
ALTER TABLE `appreciation_letters`
ADD COLUMN `year` int(11) NOT NULL AFTER `letter_date`;

-- تحديث حقل السنة في كتب الشكر الموجودة
UPDATE `appreciation_letters` SET `year` = YEAR(`letter_date`);
