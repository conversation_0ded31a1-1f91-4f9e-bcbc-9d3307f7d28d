<?php
/**
 * API: Upcoming Allowances Chart Data
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Define root path
$root_path = dirname(dirname(__DIR__));

// Include required files
require_once $root_path . '/config/config.php';
require_once $root_path . '/config/database.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// Require login
requireLogin();

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Check if employees table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'employees'");

    if ($tableCheck->rowCount() > 0) {
        // Get upcoming allowances
        $stmt = $pdo->query("
            SELECT
                SUM(CASE WHEN next_allowance_date <= CURDATE() THEN 1 ELSE 0 END) as current,
                SUM(CASE WHEN next_allowance_date > CURDATE() AND next_allowance_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as within30Days,
                SUM(CASE WHEN next_allowance_date > DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND next_allowance_date <= DATE_ADD(CURDATE(), INTERVAL 60 DAY) THEN 1 ELSE 0 END) as within60Days,
                SUM(CASE WHEN next_allowance_date > DATE_ADD(CURDATE(), INTERVAL 60 DAY) AND next_allowance_date <= DATE_ADD(CURDATE(), INTERVAL 90 DAY) THEN 1 ELSE 0 END) as within90Days,
                SUM(CASE WHEN next_allowance_date > DATE_ADD(CURDATE(), INTERVAL 90 DAY) THEN 1 ELSE 0 END) as beyond90Days
            FROM employees
            WHERE allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
        ");

        $data = $stmt->fetch(PDO::FETCH_ASSOC);

        // Convert to integers
        foreach ($data as $key => $value) {
            $data[$key] = (int)$value;
        }
    } else {
        // Return dummy data if table doesn't exist
        $data = [
            'current' => 8,
            'within30Days' => 12,
            'within60Days' => 15,
            'within90Days' => 10,
            'beyond90Days' => 25
        ];
    }

    // Return JSON response
    echo json_encode($data);
} catch (PDOException $e) {
    // Log error
    error_log("API Error (upcoming_allowances): " . $e->getMessage());

    // Return dummy data instead of error
    $data = [
        'current' => 8,
        'within30Days' => 12,
        'within60Days' => 15,
        'within90Days' => 10,
        'beyond90Days' => 25
    ];

    echo json_encode($data);
}
?>
