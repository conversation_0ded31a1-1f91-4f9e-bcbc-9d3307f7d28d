<?php
/**
 * Edit Employee Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and proper role
requireRole(['admin', 'hr']);

// Check if employee ID is provided
$employeeId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($employeeId <= 0) {
    flash('error_message', 'معرف الموظف غير صحيح', 'alert alert-danger');
    redirect('employees.php');
}

// Get employee details
try {
    $stmt = $pdo->prepare("
        SELECT e.*, d.name as department_name, el.name as education_level_name
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        JOIN education_levels el ON e.education_level_id = el.id
        WHERE e.id = :id
    ");
    $stmt->execute([':id' => $employeeId]);
    $employee = $stmt->fetch();

    if (!$employee) {
        flash('error_message', 'الموظف غير موجود', 'alert alert-danger');
        redirect('employees.php');
    }
} catch (PDOException $e) {
    error_log("Get Employee Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الموظف', 'alert alert-danger');
    redirect('employees.php');
}

// Get departments and education levels
try {
    $deptStmt = $pdo->query("SELECT id, name FROM departments ORDER BY name ASC");
    $departments = $deptStmt->fetchAll();

    $eduStmt = $pdo->query("SELECT id, name, max_grade FROM education_levels ORDER BY id ASC");
    $educationLevels = $eduStmt->fetchAll();

    // Check if job_titles table exists
    $checkTableStmt = $pdo->query("SHOW TABLES LIKE 'job_titles'");
    $jobTitlesTableExists = ($checkTableStmt->rowCount() > 0);

    // Get job titles for each grade
    $jobTitlesByGrade = [];
    if ($jobTitlesTableExists) {
        $jobTitlesStmt = $pdo->query("
            SELECT id, grade_id, title, description
            FROM job_titles
            ORDER BY grade_id ASC, title ASC
        ");
        $allJobTitles = $jobTitlesStmt->fetchAll();

        // Group job titles by grade
        foreach ($allJobTitles as $title) {
            $jobTitlesByGrade[$title['grade_id']][] = $title;
        }
    }
} catch (PDOException $e) {
    error_log("Get Reference Data Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء تحميل البيانات المرجعية', 'alert alert-danger');
    $departments = $educationLevels = [];
    $jobTitlesTableExists = false;
    $jobTitlesByGrade = [];
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $employeeNumber = sanitize($_POST['employee_number']);
    $fullName = sanitize($_POST['full_name']);
    $currentGrade = (int)$_POST['current_grade'];
    $currentStage = (int)$_POST['current_stage'];
    $jobTitle = sanitize($_POST['job_title']);
    $departmentId = (int)$_POST['department_id'];
    $educationLevelId = (int)$_POST['education_level_id'];
    $hireDate = sanitize($_POST['hire_date']);
    $yearsOfService = (int)$_POST['years_of_service'];
    $yearsInCurrentGrade = (int)$_POST['years_in_current_grade'];
    $allowancesInCurrentGrade = (int)$_POST['allowances_in_current_grade'];
    $lastAllowanceDate = !empty($_POST['last_allowance_date']) ? sanitize($_POST['last_allowance_date']) : null;
    $lastPromotionDate = !empty($_POST['last_promotion_date']) ? sanitize($_POST['last_promotion_date']) : null;
    $nextAllowanceDate = !empty($_POST['next_allowance_date']) ? sanitize($_POST['next_allowance_date']) : null;
    $nextPromotionDate = !empty($_POST['next_promotion_date']) ? sanitize($_POST['next_promotion_date']) : null;
    $notes = sanitize($_POST['notes']);

    // Validate inputs
    $errors = [];

    if (empty($employeeNumber)) {
        $errors[] = 'الرقم الوظيفي مطلوب';
    }

    if (empty($fullName)) {
        $errors[] = 'اسم الموظف مطلوب';
    }

    if ($currentGrade < 1 || $currentGrade > 10) {
        $errors[] = 'الدرجة الوظيفية يجب أن تكون بين 1 و 10';
    }

    if (empty($jobTitle)) {
        $errors[] = 'العنوان الوظيفي مطلوب';
    }

    if ($departmentId <= 0) {
        $errors[] = 'يرجى اختيار القسم';
    }

    if ($educationLevelId <= 0) {
        $errors[] = 'يرجى اختيار التحصيل الدراسي';
    }

    if (empty($hireDate)) {
        $errors[] = 'تاريخ التعيين مطلوب';
    }

    // Check if employee number already exists (excluding current employee)
    try {
        $stmt = $pdo->prepare("SELECT id FROM employees WHERE employee_number = :employee_number AND id != :id");
        $stmt->execute([':employee_number' => $employeeNumber, ':id' => $employeeId]);

        if ($stmt->rowCount() > 0) {
            $errors[] = 'الرقم الوظيفي موجود بالفعل';
        }
    } catch (PDOException $e) {
        error_log("Check Employee Number Error: " . $e->getMessage());
        $errors[] = 'حدث خطأ أثناء التحقق من الرقم الوظيفي';
    }

    // If no errors, update employee
    if (empty($errors)) {
        try {
            // If next allowance date is not provided, calculate it
            if (empty($nextAllowanceDate) && $lastAllowanceDate) {
                $nextAllowanceDate = calculateNextAllowanceDate($lastAllowanceDate);
            } elseif (empty($nextAllowanceDate)) {
                // If no last allowance date, use hire date + 1 year
                $nextAllowanceDate = calculateNextAllowanceDate($hireDate);
            }

            // If next promotion date is not provided, calculate it
            if (empty($nextPromotionDate) && $lastPromotionDate) {
                $nextPromotionDate = calculateNextPromotionDate($lastPromotionDate, $currentGrade);
            } elseif (empty($nextPromotionDate)) {
                // If no last promotion date, use hire date + required years
                // Get years needed from grade_service_years table
                try {
                    $serviceYearsStmt = $pdo->prepare("
                        SELECT service_years
                        FROM grade_service_years
                        WHERE grade_id = :grade_id
                    ");
                    $serviceYearsStmt->execute([':grade_id' => $currentGrade]);
                    $serviceYearsResult = $serviceYearsStmt->fetch();

                    if ($serviceYearsResult) {
                        $yearsNeeded = (int)$serviceYearsResult['service_years'];
                    } else {
                        // Fallback to constants if not found in table
                        $yearsNeeded = ($currentGrade >= 5) ? PROMOTION_YEARS_LOWER_GRADES : PROMOTION_YEARS_UPPER_GRADES;
                    }
                } catch (PDOException $e) {
                    error_log("Get Service Years Error: " . $e->getMessage());
                    // Fallback to constants if error
                    $yearsNeeded = ($currentGrade >= 5) ? PROMOTION_YEARS_LOWER_GRADES : PROMOTION_YEARS_UPPER_GRADES;
                }

                $hireDateObj = new DateTime($hireDate);
                $hireDateObj->add(new DateInterval('P' . $yearsNeeded . 'Y'));
                $nextPromotionDate = $hireDateObj->format('Y-m-d');
            }

            // Get nominal salary based on grade and stage
            $nominalSalary = getNominalSalary($currentGrade, $currentStage);

            // Update employee
            $stmt = $pdo->prepare("
                UPDATE employees SET
                    employee_number = :employee_number,
                    full_name = :full_name,
                    current_grade = :current_grade,
                    current_stage = :current_stage,
                    nominal_salary = :nominal_salary,
                    job_title = :job_title,
                    department_id = :department_id,
                    education_level_id = :education_level_id,
                    hire_date = :hire_date,
                    years_of_service = :years_of_service,
                    years_in_current_grade = :years_in_current_grade,
                    allowances_in_current_grade = :allowances_in_current_grade,
                    last_allowance_date = :last_allowance_date,
                    last_promotion_date = :last_promotion_date,
                    next_allowance_date = :next_allowance_date,
                    next_promotion_date = :next_promotion_date,
                    notes = :notes
                WHERE id = :id
            ");

            $stmt->execute([
                ':employee_number' => $employeeNumber,
                ':full_name' => $fullName,
                ':current_grade' => $currentGrade,
                ':current_stage' => $currentStage,
                ':nominal_salary' => $nominalSalary,
                ':job_title' => $jobTitle,
                ':department_id' => $departmentId,
                ':education_level_id' => $educationLevelId,
                ':hire_date' => $hireDate,
                ':years_of_service' => $yearsOfService,
                ':years_in_current_grade' => $yearsInCurrentGrade,
                ':allowances_in_current_grade' => $allowancesInCurrentGrade,
                ':last_allowance_date' => $lastAllowanceDate,
                ':last_promotion_date' => $lastPromotionDate,
                ':next_allowance_date' => $nextAllowanceDate,
                ':next_promotion_date' => $nextPromotionDate,
                ':notes' => $notes,
                ':id' => $employeeId
            ]);

            // Log activity
            logActivity(
                $_SESSION['user_id'],
                'تعديل موظف',
                'employees',
                $employeeId,
                'تم تعديل بيانات الموظف: ' . $fullName
            );

            // Redirect to employee details
            flash('success_message', 'تم تعديل بيانات الموظف بنجاح', 'alert alert-success');
            redirect('employee_details.php?id=' . $employeeId);
        } catch (PDOException $e) {
            error_log("Edit Employee Error: " . $e->getMessage());
            flash('error_message', 'حدث خطأ أثناء تعديل بيانات الموظف', 'alert alert-danger');
        }
    } else {
        // Display errors
        $errorMessage = '<ul class="mb-0">';
        foreach ($errors as $error) {
            $errorMessage .= '<li>' . $error . '</li>';
        }
        $errorMessage .= '</ul>';

        flash('error_message', $errorMessage, 'alert alert-danger');
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-user-edit me-2"></i> تعديل بيانات الموظف
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="employees.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى قائمة الموظفين
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF'] . '?id=' . $employeeId; ?>" method="post" class="needs-validation" novalidate>
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="employee_number" class="form-label">الرقم الوظيفي <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="employee_number" name="employee_number" value="<?php echo $employee['employee_number']; ?>" required>
                    <div class="invalid-feedback">يرجى إدخال الرقم الوظيفي</div>
                </div>
                <div class="col-md-6">
                    <label for="full_name" class="form-label">اسم الموظف <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo $employee['full_name']; ?>" required>
                    <div class="invalid-feedback">يرجى إدخال اسم الموظف</div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-3">
                    <label for="current_grade" class="form-label">الدرجة الحالية <span class="text-danger">*</span></label>
                    <select class="form-select" id="current_grade" name="current_grade" required>
                        <option value="">اختر الدرجة</option>
                        <?php for ($i = 1; $i <= 10; $i++): ?>
                            <option value="<?php echo $i; ?>" <?php echo ($employee['current_grade'] == $i) ? 'selected' : ''; ?>>الدرجة <?php echo $i; ?></option>
                        <?php endfor; ?>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار الدرجة الحالية</div>
                </div>
                <div class="col-md-3">
                    <label for="current_stage" class="form-label">المرحلة الحالية <span class="text-danger">*</span></label>
                    <select class="form-select" id="current_stage" name="current_stage" required>
                        <option value="">اختر المرحلة</option>
                        <?php for ($i = 1; $i <= 11; $i++): ?>
                            <option value="<?php echo $i; ?>" <?php echo ($employee['current_stage'] == $i) ? 'selected' : ''; ?>>المرحلة <?php echo $i; ?></option>
                        <?php endfor; ?>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار المرحلة الحالية</div>
                </div>
                <div class="col-md-3">
                    <label for="job_title" class="form-label">العنوان الوظيفي <span class="text-danger">*</span></label>
                    <?php if ($jobTitlesTableExists): ?>
                        <select class="form-select" id="job_title" name="job_title" required>
                            <option value="">اختر العنوان الوظيفي</option>
                            <?php
                            // Add current job title if not in the list
                            $currentJobTitle = $employee['job_title'];
                            $currentGrade = $employee['current_grade'];
                            $titleExists = false;

                            if (isset($jobTitlesByGrade[$currentGrade])) {
                                foreach ($jobTitlesByGrade[$currentGrade] as $title) {
                                    if ($title['title'] === $currentJobTitle) {
                                        $titleExists = true;
                                        break;
                                    }
                                }
                            }

                            if (!$titleExists && !empty($currentJobTitle)) {
                                echo '<option value="' . htmlspecialchars($currentJobTitle) . '" selected>' . htmlspecialchars($currentJobTitle) . ' (العنوان الحالي)</option>';
                            }

                            // Add job titles for current grade
                            if (isset($jobTitlesByGrade[$currentGrade])) {
                                foreach ($jobTitlesByGrade[$currentGrade] as $title) {
                                    $selected = ($title['title'] === $currentJobTitle) ? 'selected' : '';
                                    echo '<option value="' . htmlspecialchars($title['title']) . '" ' . $selected . '>' . htmlspecialchars($title['title']) . '</option>';
                                }
                            }
                            ?>
                        </select>
                        <div class="invalid-feedback">يرجى اختيار العنوان الوظيفي</div>
                    <?php else: ?>
                        <input type="text" class="form-control" id="job_title" name="job_title" value="<?php echo $employee['job_title']; ?>" required>
                        <div class="invalid-feedback">يرجى إدخال العنوان الوظيفي</div>
                    <?php endif; ?>
                </div>
                <div class="col-md-4">
                    <label for="department_id" class="form-label">القسم <span class="text-danger">*</span></label>
                    <select class="form-select" id="department_id" name="department_id" required>
                        <option value="">اختر القسم</option>
                        <?php foreach ($departments as $department): ?>
                            <option value="<?php echo $department['id']; ?>" <?php echo ($employee['department_id'] == $department['id']) ? 'selected' : ''; ?>><?php echo $department['name']; ?></option>
                        <?php endforeach; ?>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار القسم</div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="education_level_id" class="form-label">التحصيل الدراسي <span class="text-danger">*</span></label>
                    <select class="form-select" id="education_level_id" name="education_level_id" required>
                        <option value="">اختر التحصيل الدراسي</option>
                        <?php foreach ($educationLevels as $level): ?>
                            <option value="<?php echo $level['id']; ?>" <?php echo ($employee['education_level_id'] == $level['id']) ? 'selected' : ''; ?>><?php echo $level['name']; ?> (الحد الأقصى: الدرجة <?php echo $level['max_grade']; ?>)</option>
                        <?php endforeach; ?>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار التحصيل الدراسي</div>
                </div>
                <div class="col-md-4">
                    <label for="hire_date" class="form-label">تاريخ التعيين <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="hire_date" name="hire_date" value="<?php echo $employee['hire_date']; ?>" required>
                    <div class="invalid-feedback">يرجى إدخال تاريخ التعيين</div>
                </div>
                <div class="col-md-4">
                    <label for="years_of_service" class="form-label">سنوات الخدمة</label>
                    <input type="number" class="form-control" id="years_of_service" name="years_of_service" min="0" value="<?php echo $employee['years_of_service']; ?>">
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-3">
                    <label for="years_in_current_grade" class="form-label">سنوات الخدمة في الدرجة الحالية</label>
                    <input type="number" class="form-control" id="years_in_current_grade" name="years_in_current_grade" min="0" value="<?php echo $employee['years_in_current_grade']; ?>">
                </div>
                <div class="col-md-3">
                    <label for="allowances_in_current_grade" class="form-label">عدد العلاوات في الدرجة الحالية</label>
                    <input type="number" class="form-control" id="allowances_in_current_grade" name="allowances_in_current_grade" min="0" max="11" value="<?php echo $employee['allowances_in_current_grade']; ?>">
                </div>
                <div class="col-md-3">
                    <label for="last_allowance_date" class="form-label">تاريخ آخر علاوة</label>
                    <input type="date" class="form-control" id="last_allowance_date" name="last_allowance_date" value="<?php echo $employee['last_allowance_date']; ?>">
                </div>
                <div class="col-md-3">
                    <label for="next_allowance_date" class="form-label">تاريخ العلاوة القادمة</label>
                    <input type="date" class="form-control" id="next_allowance_date" name="next_allowance_date" value="<?php echo $employee['next_allowance_date']; ?>">
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="last_promotion_date" class="form-label">تاريخ آخر ترفيع</label>
                    <input type="date" class="form-control" id="last_promotion_date" name="last_promotion_date" value="<?php echo $employee['last_promotion_date']; ?>">
                </div>
                <div class="col-md-4">
                    <label for="next_promotion_date" class="form-label">تاريخ الترفيع القادم</label>
                    <input type="date" class="form-control" id="next_promotion_date" name="next_promotion_date" value="<?php echo $employee['next_promotion_date']; ?>">
                </div>
                <div class="col-md-4">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $employee['notes']; ?></textarea>
                </div>
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-warning px-5">
                    <i class="fas fa-save me-1"></i> حفظ التعديلات
                </button>
                <a href="employees.php" class="btn btn-secondary px-5">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>

<?php if ($jobTitlesTableExists): ?>
<script>
// Store job titles by grade
const jobTitlesByGrade = <?php echo json_encode($jobTitlesByGrade); ?>;
// Store current job title
const currentJobTitle = "<?php echo addslashes($employee['job_title']); ?>";

// Function to update job titles dropdown based on selected grade
function updateJobTitles() {
    const gradeSelect = document.getElementById('current_grade');
    const jobTitleSelect = document.getElementById('job_title');

    // Clear current options
    jobTitleSelect.innerHTML = '<option value="">اختر العنوان الوظيفي</option>';

    // Get selected grade
    const selectedGrade = gradeSelect.value;

    // Add current job title if not in the list for selected grade
    let titleExists = false;

    if (selectedGrade && jobTitlesByGrade[selectedGrade]) {
        // Check if current title exists in the list
        for (const title of jobTitlesByGrade[selectedGrade]) {
            if (title.title === currentJobTitle) {
                titleExists = true;
                break;
            }
        }

        // Add job titles for selected grade
        jobTitlesByGrade[selectedGrade].forEach(title => {
            const option = document.createElement('option');
            option.value = title.title;
            option.textContent = title.title;
            if (title.title === currentJobTitle) {
                option.selected = true;
            }
            if (title.description) {
                option.title = title.description;
            }
            jobTitleSelect.appendChild(option);
        });
    }

    // Add current job title if not in the list
    if (!titleExists && currentJobTitle) {
        const option = document.createElement('option');
        option.value = currentJobTitle;
        option.textContent = currentJobTitle + ' (العنوان الحالي)';
        option.selected = true;
        jobTitleSelect.appendChild(option);
    }
}

// Add event listener to grade select
document.addEventListener('DOMContentLoaded', function() {
    const gradeSelect = document.getElementById('current_grade');
    gradeSelect.addEventListener('change', updateJobTitles);

    // Initialize job titles if grade is already selected
    if (gradeSelect.value) {
        updateJobTitles();
    }
});
</script>
<?php endif; ?>
