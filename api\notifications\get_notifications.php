<?php
/**
 * API: Get Notifications
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Define root path
$root_path = dirname(dirname(__DIR__));

// Include required files
require_once $root_path . '/config/config.php';
require_once $root_path . '/config/database.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// Require login
requireLogin();

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Check if notifications table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'notifications'");

    if ($tableCheck->rowCount() > 0) {
        // Get notifications for current user
        $stmt = $pdo->prepare("
            SELECT id, type, title, message, link, read, created_at
            FROM notifications
            WHERE user_id = :user_id
            ORDER BY created_at DESC
            LIMIT 20
        ");

        $stmt->execute([':user_id' => $_SESSION['user_id']]);
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        // Return empty array if table doesn't exist
        $notifications = [];
    }

    // Return JSON response
    echo json_encode([
        'success' => true,
        'notifications' => $notifications
    ]);
} catch (PDOException $e) {
    // Log error
    error_log("API Error (get_notifications): " . $e->getMessage());

    // Return empty array instead of error
    echo json_encode([
        'success' => true,
        'notifications' => []
    ]);
}
?>
