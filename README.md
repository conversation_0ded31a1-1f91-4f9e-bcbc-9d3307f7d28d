# نظام إدارة العلاوات والترقية وكتب الشكر

## نظرة عامة

نظام إدارة العلاوات والترقية وكتب الشكر هو تطبيق ويب مصمم لمؤسسات الدولة لإدارة شؤون الموظفين المتعلقة بالعلاوات السنوية، الترقية الوظيفية، وكتب الشكر. يساعد النظام في تتبع استحقاقات الموظفين وتواريخ العلاوات والترقيات بشكل آلي، مع مراعاة قواعد العمل المحددة.

## المميزات الرئيسية

1. **إدارة بيانات الموظفين**:
   - تسجيل بيانات تفصيلية لكل موظف
   - تتبع الدرجة الوظيفية والمرحلة والراتب الاسمي
   - حساب تاريخ التقاعد تلقائياً بناءً على تاريخ الميلاد

2. **إدارة العلاوات**:
   - حساب تلقائي لاستحقاق العلاوات
   - تتبع العلاوات المستلمة في كل درجة (الحد الأقصى 11 علاوة)
   - تحديث المرحلة والراتب الاسمي عند إضافة علاوة

3. **إدارة الترقيات**:
   - حساب تلقائي لاستحقاق الترقية بناءً على سنوات الخدمة
   - مراعاة قيود التحصيل الدراسي على الترقية
   - تحديث الدرجة والمرحلة والراتب الاسمي عند الترقية

4. **إدارة كتب الشكر**:
   - تسجيل كتب الشكر وتأثيرها على تواريخ العلاوات والترقيات
   - تمييز بين كتب الشكر العادية وكتب شكر رئيس الوزراء

5. **إدارة الرواتب الاسمية**:
   - جدول للرواتب الاسمية لكل درجة ومرحلة
   - تتبع تاريخ تغييرات الراتب الاسمي
   - تقارير عن الرواتب الاسمية

6. **تطبيق قانون 103 للشهادات الأعلى**:
   - إعادة تصنيف الموظف عند حصوله على شهادة أعلى
   - تتبع تاريخ الشهادات العليا
   - حساب تواريخ الترقية وفق قانون 103

7. **نظام التنبيهات والإشعارات**:
   - تنبيهات للعلاوات والترقيات المستحقة
   - تنبيهات للتقاعد المتوقع
   - تنبيهات لحد كتب الشكر

8. **التقارير والإحصائيات**:
   - تقارير متنوعة عن المستحقين للعلاوات والترقيات
   - تقارير عن الرواتب الاسمية والتقاعد المتوقع
   - إحصائيات عن الموظفين والأقسام

9. **إدارة المستخدمين والصلاحيات**:
   - مستويات مختلفة من الصلاحيات (مدير النظام، موظف الموارد البشرية، مستخدم عادي)
   - تسجيل جميع العمليات في سجلات النظام

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache أو Nginx)
- XAMPP (للتطوير المحلي)

## التثبيت

1. **إعداد قاعدة البيانات**:
   - قم بإنشاء قاعدة بيانات جديدة باسم `ترقيات`
   - استورد ملف `sql/complete_database.sql` لإنشاء جداول قاعدة البيانات وإضافة البيانات الأولية

2. **تكوين النظام**:
   - قم بتعديل ملف `config/database.php` لإعداد اتصال قاعدة البيانات
   - قم بتعديل ملف `config/config.php` لتخصيص إعدادات النظام

3. **تثبيت النظام**:
   - انسخ جميع ملفات المشروع إلى مجلد الويب الخاص بك
   - تأكد من أن المجلدات `uploads` و `uploads/appreciation_letters` قابلة للكتابة

4. **الوصول إلى النظام**:
   - افتح المتصفح وانتقل إلى عنوان النظام (مثلاً: `http://localhost/ترقيات`)
   - سجل الدخول باستخدام بيانات المستخدم الافتراضية:
     - اسم المستخدم: `admin`
     - كلمة المرور: `password`

## هيكل المشروع

```
/
├── assets/              # ملفات CSS و JavaScript والصور
├── config/              # ملفات الإعدادات
├── database/            # ملفات قاعدة البيانات
├── includes/            # ملفات PHP المشتركة
├── modules/             # وحدات النظام المختلفة
├── uploads/             # مجلد لتحميل الملفات
│   └── appreciation_letters/  # كتب الشكر المرفوعة
├── index.php            # الصفحة الرئيسية
├── login.php            # صفحة تسجيل الدخول
└── README.md            # ملف التوثيق
```

## قواعد العمل

### العلاوات السنوية
- كل درجة تحتوي على 11 علاوة فقط
- بعد إكمال 11 علاوة، يتوقف الموظف عن استلام العلاوات حتى يتم ترفيعه
- كتاب الشكر العادي يقدم العلاوة لمدة شهر واحد
- كتاب شكر رئيس الوزراء يقدم العلاوة لمدة 6 أشهر
- عند منح العلاوة، يتم زيادة المرحلة بمقدار 1 وتحديث الراتب الاسمي

### الترقية الوظيفية
- الترقية من الدرجة 10 إلى 5 تتم بعد مرور 4 سنوات
- الترقية من الدرجة 5 إلى 1 تتم بعد مرور 5 سنوات
- قيود التحصيل الدراسي:
  - من لا يحمل شهادة أو من يحمل شهادة ابتدائية أو متوسطة: يُرقّى حتى الدرجة 10 فقط
  - من يحمل شهادة إعدادية: يُرقّى حتى الدرجة 9 فقط
  - حملة الدبلوم: يُرقّى حتى الدرجة 5 فقط
  - حملة البكالوريوس: يُرقّى حتى الدرجة 4 فقط
  - حملة الماجستير: يُرقّى حتى الدرجة 2 فقط
  - حملة الدكتوراه: يُرقّى حتى الدرجة 1 فقط
- عند الترقية، يتم تعيين المرحلة إلى 1 وتحديث الراتب الاسمي

### الرواتب الاسمية
- لكل درجة ومرحلة راتب اسمي محدد
- يتم تحديث الراتب الاسمي عند تغيير الدرجة أو المرحلة
- يتم الاحتفاظ بسجل كامل لتغييرات الراتب الاسمي

### قانون 103 للشهادات الأعلى
- عند حصول الموظف على شهادة أعلى، يتم إعادته إلى الدرجة المقابلة للشهادة الجديدة
- يترقى الموظف كل سنتين حتى يصل إلى الدرجة السابقة
- بعد العودة إلى الدرجة الأصلية، يستأنف الترقية حسب النظام الطبيعي

### التقاعد
- يتم حساب تاريخ التقاعد تلقائياً بناءً على تاريخ الميلاد (60 سنة)
- يمكن للموظف التقاعد المبكر بعد 25 سنة من الخدمة
- يتم إنشاء تنبيه قبل سنة من تاريخ التقاعد

## الصلاحيات

1. **مدير النظام (admin)**:
   - الوصول الكامل لجميع وظائف النظام
   - إدارة المستخدمين والأقسام
   - الاطلاع على سجلات النظام

2. **موظف الموارد البشرية (hr)**:
   - إدارة بيانات الموظفين
   - إضافة وتعديل العلاوات والترقيات وكتب الشكر
   - الوصول إلى التقارير

3. **مستخدم عادي (viewer)**:
   - عرض بيانات الموظفين
   - عرض العلاوات والترقيات وكتب الشكر
   - عرض التقارير

## الدعم والتواصل

للحصول على المساعدة أو الإبلاغ عن مشكلة، يرجى التواصل مع فريق الدعم الفني.

## الترخيص

هذا النظام مرخص للاستخدام الداخلي فقط ضمن مؤسسات الدولة.
