<?php
/**
 * Unified Dashboard - Main Page
 * لوحة التحكم الموحدة - الصفحة الرئيسية
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();

// Get dashboard statistics
try {
    // Total employees count
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM employees");
    $totalEmployees = $stmt->fetch()['count'];

    // Employees eligible for allowance
    $stmt = $pdo->query("
        SELECT COUNT(*) as count FROM employees
        WHERE allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
        AND next_allowance_date <= CURDATE()
    ");
    $eligibleForAllowance = $stmt->fetch()['count'];

    // Employees eligible for promotion
    $stmt = $pdo->query("
        SELECT COUNT(*) as count FROM employees
        WHERE next_promotion_date <= CURDATE()
    ");
    $eligibleForPromotion = $stmt->fetch()['count'];

    // Total appreciation letters
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM appreciation_letters");
    $totalAppreciationLetters = $stmt->fetch()['count'];

    // Employees by department
    $stmt = $pdo->query("
        SELECT d.name, COUNT(e.id) as count
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        GROUP BY e.department_id
        ORDER BY count DESC
    ");
    $employeesByDept = $stmt->fetchAll();

    // Employees by grade
    $stmt = $pdo->query("
        SELECT e.current_grade, COUNT(e.id) as count
        FROM employees e
        GROUP BY e.current_grade
        ORDER BY e.current_grade ASC
    ");
    $employeesByGrade = $stmt->fetchAll();

    // Recent activities
    $stmt = $pdo->query("
        SELECT sl.*, u.username, u.full_name
        FROM system_logs sl
        JOIN users u ON sl.user_id = u.id
        ORDER BY sl.created_at DESC
        LIMIT 10
    ");
    $recentActivities = $stmt->fetchAll();

    // Upcoming allowances
    $stmt = $pdo->query("
        SELECT e.id, e.employee_number, e.full_name, e.current_grade, e.next_allowance_date, d.name as department_name
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        WHERE e.allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
        AND e.next_allowance_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
        ORDER BY e.next_allowance_date ASC
        LIMIT 5
    ");
    $upcomingAllowances = $stmt->fetchAll();

    // Upcoming promotions
    $stmt = $pdo->query("
        SELECT e.id, e.employee_number, e.full_name, e.current_grade, e.next_promotion_date, d.name as department_name
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        WHERE e.next_promotion_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
        ORDER BY e.next_promotion_date ASC
        LIMIT 5
    ");
    $upcomingPromotions = $stmt->fetchAll();

    // Get retirement age from settings
    $retirementAge = 60; // Default
    $settingStmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'retirement_age'");
    $settingStmt->execute();
    $setting = $settingStmt->fetch();
    if ($setting) {
        $retirementAge = (int)$setting['setting_value'];
    }

    // Upcoming retirements
    $stmt = $pdo->query("
        SELECT e.id, e.employee_number, e.full_name, e.birth_date, e.current_grade, d.name as department_name,
               TIMESTAMPDIFF(YEAR, e.birth_date, CURDATE()) as current_age,
               DATE_ADD(e.birth_date, INTERVAL $retirementAge YEAR) as retirement_date
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        WHERE DATE_ADD(e.birth_date, INTERVAL $retirementAge YEAR) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 180 DAY)
        ORDER BY retirement_date ASC
        LIMIT 5
    ");
    $upcomingRetirements = $stmt->fetchAll();

    // Monthly statistics for charts
    $stmt = $pdo->query("
        SELECT 
            MONTH(created_at) as month,
            COUNT(*) as count,
            'allowances' as type
        FROM allowance_history 
        WHERE YEAR(created_at) = YEAR(CURDATE())
        GROUP BY MONTH(created_at)
        UNION ALL
        SELECT 
            MONTH(created_at) as month,
            COUNT(*) as count,
            'promotions' as type
        FROM promotion_history 
        WHERE YEAR(created_at) = YEAR(CURDATE())
        GROUP BY MONTH(created_at)
        ORDER BY month
    ");
    $monthlyStats = $stmt->fetchAll();

    // Appreciation letters by month
    $stmt = $pdo->query("
        SELECT 
            MONTH(issue_date) as month,
            COUNT(*) as count
        FROM appreciation_letters 
        WHERE YEAR(issue_date) = YEAR(CURDATE())
        GROUP BY MONTH(issue_date)
        ORDER BY month
    ");
    $appreciationByMonth = $stmt->fetchAll();

} catch (PDOException $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء تحميل البيانات', 'alert alert-danger');
    $totalEmployees = $eligibleForAllowance = $eligibleForPromotion = $totalAppreciationLetters = 0;
    $employeesByDept = $employeesByGrade = $recentActivities = $upcomingAllowances = $upcomingPromotions = $upcomingRetirements = [];
    $monthlyStats = $appreciationByMonth = [];
}
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb bg-light rounded-modern p-3">
        <li class="breadcrumb-item active" aria-current="page">
            <i class="fas fa-home me-2"></i>لوحة التحكم الرئيسية
        </li>
    </ol>
</nav>

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="display-5 mb-2 text-gradient-primary">
            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم الموحدة
        </h1>
        <p class="text-muted">نظرة شاملة على إحصائيات النظام والأنشطة الحديثة</p>
    </div>
    <div class="col-md-4 text-md-end">
        <div class="btn-group" role="group">
            <button id="refreshDashboard" class="btn btn-primary btn-icon">
                <i class="fas fa-sync-alt"></i>
                تحديث البيانات
            </button>
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-cog"></i>
                    خيارات
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="analytics.php"><i class="fas fa-chart-line me-2"></i>التحليلات المتقدمة</a></li>
                    <li><a class="dropdown-item" href="reports.php"><i class="fas fa-file-alt me-2"></i>التقارير</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="system_settings.php"><i class="fas fa-sliders-h me-2"></i>إعدادات النظام</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="dashboard-card animate__animated animate__fadeIn">
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="count"><?php echo number_format($totalEmployees); ?></div>
            <div class="title">إجمالي الموظفين</div>
            <div class="subtitle">جميع الموظفين المسجلين</div>
            <a href="employees.php" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="dashboard-card success animate__animated animate__fadeIn" style="animation-delay: 0.1s;">
            <div class="icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="count"><?php echo number_format($eligibleForAllowance); ?></div>
            <div class="title">مستحقي العلاوة</div>
            <div class="subtitle">مستحقين حالياً</div>
            <a href="allowances.php?status=eligible" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="dashboard-card animate__animated animate__fadeIn" style="animation-delay: 0.2s;">
            <div class="icon">
                <i class="fas fa-level-up-alt"></i>
            </div>
            <div class="count"><?php echo number_format($eligibleForPromotion); ?></div>
            <div class="title">مستحقي الترفيع</div>
            <div class="subtitle">مستحقين حالياً</div>
            <a href="promotions.php?status=eligible" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="dashboard-card warning animate__animated animate__fadeIn" style="animation-delay: 0.3s;">
            <div class="icon">
                <i class="fas fa-certificate"></i>
            </div>
            <div class="count"><?php echo number_format($totalAppreciationLetters); ?></div>
            <div class="title">كتب الشكر</div>
            <div class="subtitle">إجمالي الكتب الصادرة</div>
            <a href="appreciation.php" class="stretched-link"></a>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row mb-4">
    <div class="col-lg-6 mb-4">
        <div class="card h-100 shadow-modern rounded-modern animate__animated animate__fadeIn" style="animation-delay: 0.4s;">
            <div class="card-header bg-gradient-primary">
                <h5 class="mb-0 text-white">
                    <i class="fas fa-chart-pie me-2"></i> توزيع الموظفين حسب الأقسام
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="employeesByDepartmentChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card h-100 shadow-modern rounded-modern animate__animated animate__fadeIn" style="animation-delay: 0.5s;">
            <div class="card-header bg-gradient-success">
                <h5 class="mb-0 text-white">
                    <i class="fas fa-chart-bar me-2"></i> العلاوات والترفيعات خلال العام
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="allowancesPromotionsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-lg-6 mb-4">
        <div class="card h-100 shadow-modern rounded-modern animate__animated animate__fadeIn" style="animation-delay: 0.6s;">
            <div class="card-header bg-gradient-warning">
                <h5 class="mb-0 text-dark">
                    <i class="fas fa-certificate me-2"></i> كتب الشكر خلال العام
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="appreciationLettersChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card h-100 shadow-modern rounded-modern animate__animated animate__fadeIn" style="animation-delay: 0.7s;">
            <div class="card-header bg-gradient-info">
                <h5 class="mb-0 text-white">
                    <i class="fas fa-layer-group me-2"></i> توزيع الموظفين حسب الدرجة
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="employeesByGradeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
