<?php
/**
 * Unified Dashboard - Main Page
 * لوحة التحكم الموحدة - الصفحة الرئيسية
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();

// Get dashboard statistics
try {
    // Total employees count
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM employees");
    $totalEmployees = $stmt->fetch()['count'];

    // Employees eligible for allowance
    $stmt = $pdo->query("
        SELECT COUNT(*) as count FROM employees
        WHERE allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
        AND next_allowance_date <= CURDATE()
    ");
    $eligibleForAllowance = $stmt->fetch()['count'];

    // Employees eligible for promotion
    $stmt = $pdo->query("
        SELECT COUNT(*) as count FROM employees
        WHERE next_promotion_date <= CURDATE()
    ");
    $eligibleForPromotion = $stmt->fetch()['count'];

    // Total appreciation letters
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM appreciation_letters");
    $totalAppreciationLetters = $stmt->fetch()['count'];

    // Employees by department
    $stmt = $pdo->query("
        SELECT d.name, COUNT(e.id) as count
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        GROUP BY e.department_id
        ORDER BY count DESC
    ");
    $employeesByDept = $stmt->fetchAll();

    // Employees by grade
    $stmt = $pdo->query("
        SELECT e.current_grade, COUNT(e.id) as count
        FROM employees e
        GROUP BY e.current_grade
        ORDER BY e.current_grade ASC
    ");
    $employeesByGrade = $stmt->fetchAll();

    // Recent activities
    $stmt = $pdo->query("
        SELECT sl.*, u.username, u.full_name
        FROM system_logs sl
        JOIN users u ON sl.user_id = u.id
        ORDER BY sl.created_at DESC
        LIMIT 10
    ");
    $recentActivities = $stmt->fetchAll();

    // Upcoming allowances
    $stmt = $pdo->query("
        SELECT e.id, e.employee_number, e.full_name, e.current_grade, e.next_allowance_date, d.name as department_name
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        WHERE e.allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
        AND e.next_allowance_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
        ORDER BY e.next_allowance_date ASC
        LIMIT 5
    ");
    $upcomingAllowances = $stmt->fetchAll();

    // Upcoming promotions
    $stmt = $pdo->query("
        SELECT e.id, e.employee_number, e.full_name, e.current_grade, e.next_promotion_date, d.name as department_name
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        WHERE e.next_promotion_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
        ORDER BY e.next_promotion_date ASC
        LIMIT 5
    ");
    $upcomingPromotions = $stmt->fetchAll();

    // Get retirement age from settings
    $retirementAge = 60; // Default
    $settingStmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'retirement_age'");
    $settingStmt->execute();
    $setting = $settingStmt->fetch();
    if ($setting) {
        $retirementAge = (int)$setting['setting_value'];
    }

    // Upcoming retirements
    $stmt = $pdo->query("
        SELECT e.id, e.employee_number, e.full_name, e.birth_date, e.current_grade, d.name as department_name,
               TIMESTAMPDIFF(YEAR, e.birth_date, CURDATE()) as current_age,
               DATE_ADD(e.birth_date, INTERVAL $retirementAge YEAR) as retirement_date
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        WHERE DATE_ADD(e.birth_date, INTERVAL $retirementAge YEAR) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 180 DAY)
        ORDER BY retirement_date ASC
        LIMIT 5
    ");
    $upcomingRetirements = $stmt->fetchAll();

    // Monthly statistics for charts
    $stmt = $pdo->query("
        SELECT
            MONTH(created_at) as month,
            COUNT(*) as count,
            'allowances' as type
        FROM allowance_history
        WHERE YEAR(created_at) = YEAR(CURDATE())
        GROUP BY MONTH(created_at)
        UNION ALL
        SELECT
            MONTH(created_at) as month,
            COUNT(*) as count,
            'promotions' as type
        FROM promotion_history
        WHERE YEAR(created_at) = YEAR(CURDATE())
        GROUP BY MONTH(created_at)
        ORDER BY month
    ");
    $monthlyStats = $stmt->fetchAll();

    // Appreciation letters by month
    $stmt = $pdo->query("
        SELECT
            MONTH(issue_date) as month,
            COUNT(*) as count
        FROM appreciation_letters
        WHERE YEAR(issue_date) = YEAR(CURDATE())
        GROUP BY MONTH(issue_date)
        ORDER BY month
    ");
    $appreciationByMonth = $stmt->fetchAll();

} catch (PDOException $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء تحميل البيانات', 'alert alert-danger');
    $totalEmployees = $eligibleForAllowance = $eligibleForPromotion = $totalAppreciationLetters = 0;
    $employeesByDept = $employeesByGrade = $recentActivities = $upcomingAllowances = $upcomingPromotions = $upcomingRetirements = [];
    $monthlyStats = $appreciationByMonth = [];
}
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb bg-light rounded-modern p-3">
        <li class="breadcrumb-item active" aria-current="page">
            <i class="fas fa-home me-2"></i>لوحة التحكم الرئيسية
        </li>
    </ol>
</nav>

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="display-5 mb-2 text-gradient-primary">
            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم الموحدة
        </h1>
        <p class="text-muted">نظرة شاملة على إحصائيات النظام والأنشطة الحديثة</p>
    </div>
    <div class="col-md-4 text-md-end">
        <div class="btn-group" role="group">
            <button id="refreshDashboard" class="btn btn-primary btn-icon">
                <i class="fas fa-sync-alt"></i>
                تحديث البيانات
            </button>
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-cog"></i>
                    خيارات
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="analytics.php"><i class="fas fa-chart-line me-2"></i>التحليلات المتقدمة</a></li>
                    <li><a class="dropdown-item" href="reports.php"><i class="fas fa-file-alt me-2"></i>التقارير</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="system_settings.php"><i class="fas fa-sliders-h me-2"></i>إعدادات النظام</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="dashboard-card animate__animated animate__fadeIn">
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="count"><?php echo number_format($totalEmployees); ?></div>
            <div class="title">إجمالي الموظفين</div>
            <div class="subtitle">جميع الموظفين المسجلين</div>
            <a href="employees.php" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="dashboard-card success animate__animated animate__fadeIn" style="animation-delay: 0.1s;">
            <div class="icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="count"><?php echo number_format($eligibleForAllowance); ?></div>
            <div class="title">مستحقي العلاوة</div>
            <div class="subtitle">مستحقين حالياً</div>
            <a href="allowances.php?status=eligible" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="dashboard-card animate__animated animate__fadeIn" style="animation-delay: 0.2s;">
            <div class="icon">
                <i class="fas fa-level-up-alt"></i>
            </div>
            <div class="count"><?php echo number_format($eligibleForPromotion); ?></div>
            <div class="title">مستحقي الترفيع</div>
            <div class="subtitle">مستحقين حالياً</div>
            <a href="promotions.php?status=eligible" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="dashboard-card warning animate__animated animate__fadeIn" style="animation-delay: 0.3s;">
            <div class="icon">
                <i class="fas fa-certificate"></i>
            </div>
            <div class="count"><?php echo number_format($totalAppreciationLetters); ?></div>
            <div class="title">كتب الشكر</div>
            <div class="subtitle">إجمالي الكتب الصادرة</div>
            <a href="appreciation.php" class="stretched-link"></a>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row mb-4">
    <div class="col-lg-6 mb-4">
        <div class="card h-100 shadow-modern rounded-modern animate__animated animate__fadeIn" style="animation-delay: 0.4s;">
            <div class="card-header bg-gradient-primary">
                <h5 class="mb-0 text-white">
                    <i class="fas fa-chart-pie me-2"></i> توزيع الموظفين حسب الأقسام
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="employeesByDepartmentChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card h-100 shadow-modern rounded-modern animate__animated animate__fadeIn" style="animation-delay: 0.5s;">
            <div class="card-header bg-gradient-success">
                <h5 class="mb-0 text-white">
                    <i class="fas fa-chart-bar me-2"></i> العلاوات والترفيعات خلال العام
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="allowancesPromotionsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-lg-6 mb-4">
        <div class="card h-100 shadow-modern rounded-modern animate__animated animate__fadeIn" style="animation-delay: 0.6s;">
            <div class="card-header bg-gradient-warning">
                <h5 class="mb-0 text-dark">
                    <i class="fas fa-certificate me-2"></i> كتب الشكر خلال العام
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="appreciationLettersChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card h-100 shadow-modern rounded-modern animate__animated animate__fadeIn" style="animation-delay: 0.7s;">
            <div class="card-header bg-gradient-info">
                <h5 class="mb-0 text-white">
                    <i class="fas fa-layer-group me-2"></i> توزيع الموظفين حسب الدرجة
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="employeesByGradeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upcoming Activities Section -->
<div class="row mb-4">
    <!-- Upcoming Allowances -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100 shadow-modern rounded-modern animate__animated animate__fadeIn" style="animation-delay: 0.8s;">
            <div class="card-header bg-gradient-success">
                <h6 class="mb-0 text-white">
                    <i class="fas fa-money-bill-wave me-2"></i> العلاوات القادمة
                </h6>
            </div>
            <div class="card-body">
                <?php if (count($upcomingAllowances) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover table-sm">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($upcomingAllowances as $employee): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="status-indicator status-active me-2"></div>
                                                <div>
                                                    <div class="fw-bold"><?php echo $employee['full_name']; ?></div>
                                                    <small class="text-muted"><?php echo $employee['employee_number']; ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">
                                                <?php echo formatArabicDate($employee['next_allowance_date']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="allowances.php" class="btn btn-sm btn-outline-success">عرض الكل</a>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-calendar-check fa-2x mb-2"></i>
                        <p class="mb-0">لا توجد علاوات مستحقة خلال الـ 30 يوم القادمة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Upcoming Promotions -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100 shadow-modern rounded-modern animate__animated animate__fadeIn" style="animation-delay: 0.9s;">
            <div class="card-header bg-gradient-primary">
                <h6 class="mb-0 text-white">
                    <i class="fas fa-level-up-alt me-2"></i> الترفيعات القادمة
                </h6>
            </div>
            <div class="card-body">
                <?php if (count($upcomingPromotions) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover table-sm">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($upcomingPromotions as $employee): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="status-indicator status-pending me-2"></div>
                                                <div>
                                                    <div class="fw-bold"><?php echo $employee['full_name']; ?></div>
                                                    <small class="text-muted"><?php echo $employee['employee_number']; ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">
                                                <?php echo formatArabicDate($employee['next_promotion_date']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="promotions.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-arrow-up fa-2x mb-2"></i>
                        <p class="mb-0">لا توجد ترفيعات مستحقة خلال الـ 30 يوم القادمة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Upcoming Retirements -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100 shadow-modern rounded-modern animate__animated animate__fadeIn" style="animation-delay: 1.0s;">
            <div class="card-header bg-gradient-danger">
                <h6 class="mb-0 text-white">
                    <i class="fas fa-user-clock me-2"></i> التقاعد المتوقع
                </h6>
            </div>
            <div class="card-body">
                <?php if (count($upcomingRetirements) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover table-sm">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($upcomingRetirements as $employee): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="status-indicator status-expired me-2"></div>
                                                <div>
                                                    <div class="fw-bold"><?php echo $employee['full_name']; ?></div>
                                                    <small class="text-muted"><?php echo $employee['employee_number']; ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-danger">
                                                <?php echo formatArabicDate($employee['retirement_date']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="reports.php?type=upcoming_retirement&period=180" class="btn btn-sm btn-outline-danger">عرض الكل</a>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-user-check fa-2x mb-2"></i>
                        <p class="mb-0">لا يوجد موظفين مقبلين على التقاعد خلال الـ 6 أشهر القادمة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions & Recent Activities -->
<div class="row mb-4">
    <!-- Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100 shadow-modern rounded-modern animate__animated animate__fadeIn" style="animation-delay: 1.1s;">
            <div class="card-header bg-gradient-dark">
                <h6 class="mb-0 text-white">
                    <i class="fas fa-bolt me-2"></i> إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="employees.php?action=add" class="btn btn-outline-primary btn-icon">
                        <i class="fas fa-user-plus"></i>
                        إضافة موظف جديد
                    </a>
                    <a href="allowances.php?action=process" class="btn btn-outline-success btn-icon">
                        <i class="fas fa-money-bill-wave"></i>
                        معالجة العلاوات
                    </a>
                    <a href="promotions.php?action=process" class="btn btn-outline-info btn-icon">
                        <i class="fas fa-level-up-alt"></i>
                        معالجة الترفيعات
                    </a>
                    <a href="appreciation.php?action=add" class="btn btn-outline-warning btn-icon">
                        <i class="fas fa-certificate"></i>
                        إصدار كتاب شكر
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="col-lg-8 mb-4">
        <div class="card h-100 shadow-modern rounded-modern animate__animated animate__fadeIn" style="animation-delay: 1.2s;">
            <div class="card-header bg-gradient-secondary">
                <h6 class="mb-0 text-white">
                    <i class="fas fa-history me-2"></i> آخر النشاطات
                </h6>
            </div>
            <div class="card-body">
                <?php if (count($recentActivities) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover table-sm">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>النشاط</th>
                                    <th>التفاصيل</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($recentActivities, 0, 5) as $activity): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                                <span class="fw-bold"><?php echo $activity['full_name']; ?></span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $activity['action']; ?></span>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo substr($activity['details'], 0, 50) . '...'; ?></small>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo date('d/m H:i', strtotime($activity['created_at'])); ?></small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php if (hasRole(['admin'])): ?>
                        <div class="text-center mt-3">
                            <a href="system_logs.php" class="btn btn-sm btn-outline-secondary">عرض جميع السجلات</a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <p class="mb-0">لا توجد نشاطات حديثة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- System Status & Quick Links -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-modern rounded-modern animate__animated animate__fadeIn" style="animation-delay: 1.3s;">
            <div class="card-header bg-gradient-info">
                <h6 class="mb-0 text-white">
                    <i class="fas fa-link me-2"></i> روابط سريعة ومعلومات النظام
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="row text-center">
                            <div class="col-md-3 col-6 mb-3">
                                <a href="analytics.php" class="btn btn-outline-primary w-100 p-3 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                                    <div>التحليلات المتقدمة</div>
                                </a>
                            </div>
                            <div class="col-md-3 col-6 mb-3">
                                <a href="reports.php" class="btn btn-outline-success w-100 p-3 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-file-alt fa-2x mb-2"></i>
                                    <div>التقارير الشاملة</div>
                                </a>
                            </div>
                            <div class="col-md-3 col-6 mb-3">
                                <a href="notifications.php" class="btn btn-outline-warning w-100 p-3 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-bell fa-2x mb-2"></i>
                                    <div>نظام التنبيهات</div>
                                </a>
                            </div>
                            <div class="col-md-3 col-6 mb-3">
                                <a href="system_settings.php" class="btn btn-outline-secondary w-100 p-3 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-cogs fa-2x mb-2"></i>
                                    <div>إعدادات النظام</div>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="bg-light rounded-modern p-3">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>معلومات النظام
                            </h6>
                            <div class="row text-center">
                                <div class="col-6 mb-2">
                                    <div class="fw-bold text-success"><?php echo date('Y-m-d'); ?></div>
                                    <small class="text-muted">التاريخ الحالي</small>
                                </div>
                                <div class="col-6 mb-2">
                                    <div class="fw-bold text-info"><?php echo $_SESSION['full_name']; ?></div>
                                    <small class="text-muted">المستخدم الحالي</small>
                                </div>
                                <div class="col-6">
                                    <div class="fw-bold text-warning"><?php echo isset($_SESSION['user_role']) ? $_SESSION['user_role'] : 'مستخدم'; ?></div>
                                    <small class="text-muted">الصلاحية</small>
                                </div>
                                <div class="col-6">
                                    <div class="fw-bold text-primary"><?php echo date('H:i'); ?></div>
                                    <small class="text-muted">الوقت الحالي</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Pass PHP data to JavaScript
window.employeesByDept = <?php echo json_encode($employeesByDept); ?>;
window.employeesByGrade = <?php echo json_encode($employeesByGrade); ?>;
window.monthlyStats = <?php echo json_encode($monthlyStats); ?>;
window.appreciationByMonth = <?php echo json_encode($appreciationByMonth); ?>;

// Dashboard refresh functionality
document.getElementById('refreshDashboard').addEventListener('click', function() {
    const btn = this;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
    btn.disabled = true;

    setTimeout(() => {
        location.reload();
    }, 1000);
});

// Auto-refresh every 5 minutes
setInterval(() => {
    location.reload();
}, 300000);
</script>

<!-- Dashboard Charts JavaScript -->
<script src="assets/js/dashboard-charts.js"></script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
