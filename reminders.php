<?php
/**
 * Reminders Page
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and proper role
requireRole(['admin', 'hr']);

// Get filter parameters
$type = isset($_GET['type']) ? sanitize($_GET['type']) : 'all';
$department = isset($_GET['department']) ? (int)$_GET['department'] : 0;
$days = isset($_GET['days']) ? (int)$_GET['days'] : 30;

// Get reminder days from settings
try {
    $stmt = $pdo->query("SELECT setting_value FROM system_settings WHERE setting_key = 'reminder_days'");
    $reminderDays = $stmt->fetch()['setting_value'];
} catch (PDOException $e) {
    error_log("Get Reminder Days Error: " . $e->getMessage());
    $reminderDays = 30; // Default value
}

// If days parameter is not set, use reminder days from settings
if (!isset($_GET['days'])) {
    $days = $reminderDays;
}

// Get departments for filter
try {
    $deptStmt = $pdo->query("SELECT id, name FROM departments ORDER BY name ASC");
    $departments = $deptStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Departments Error: " . $e->getMessage());
    $departments = [];
}

// Build query for allowances
$allowancesQuery = "
    SELECT e.*, d.name as department_name
    FROM employees e
    JOIN departments d ON e.department_id = d.id
    WHERE e.allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
    AND e.next_allowance_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL :days DAY)
";

if ($department > 0) {
    $allowancesQuery .= " AND e.department_id = :department";
}

$allowancesQuery .= " ORDER BY e.next_allowance_date ASC, e.full_name ASC";

// Build query for promotions
$promotionsQuery = "
    SELECT e.*, d.name as department_name, el.name as education_level_name, el.max_grade
    FROM employees e
    JOIN departments d ON e.department_id = d.id
    JOIN education_levels el ON e.education_level_id = el.id
    WHERE e.current_grade > el.max_grade
    AND e.next_promotion_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL :days DAY)
";

if ($department > 0) {
    $promotionsQuery .= " AND e.department_id = :department";
}

$promotionsQuery .= " ORDER BY e.next_promotion_date ASC, e.full_name ASC";

// Get employees eligible for allowances
$allowances = [];
if ($type === 'all' || $type === 'allowances') {
    try {
        $stmt = $pdo->prepare($allowancesQuery);
        $params = [':days' => $days];

        if ($department > 0) {
            $params[':department'] = $department;
        }

        $stmt->execute($params);
        $allowances = $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Get Allowances Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء استرجاع بيانات العلاوات', 'alert alert-danger');
        $allowances = [];
    }
}

// Get employees eligible for promotions
$promotions = [];
if ($type === 'all' || $type === 'promotions') {
    try {
        $stmt = $pdo->prepare($promotionsQuery);
        $params = [':days' => $days];

        if ($department > 0) {
            $params[':department'] = $department;
        }

        $stmt->execute($params);
        $promotions = $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Get Promotions Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الترقيات', 'alert alert-danger');
        $promotions = [];
    }
}

// Send reminders
if (isset($_GET['send_reminders']) && $_GET['send_reminders'] == 1) {
    try {
        // Start transaction
        $pdo->beginTransaction();

        // Get users to send reminders to
        $usersStmt = $pdo->query("
            SELECT id, username, full_name, email
            FROM users
            WHERE role IN ('admin', 'hr')
        ");
        $users = $usersStmt->fetchAll();

        // Create notifications for allowances
        if (count($allowances) > 0) {
            $insertStmt = $pdo->prepare("
                INSERT INTO notifications (
                    user_id, type, title, message, link, is_read, created_at, priority
                ) VALUES (
                    :user_id, 'allowance', :title, :message, :link, 0, NOW(), 'normal'
                )
            ");

            foreach ($users as $user) {
                $title = 'تذكير بالعلاوات المستحقة';
                $message = 'هناك ' . count($allowances) . ' موظف مستحق للعلاوة خلال الـ ' . $days . ' يوم القادمة';
                $link = 'allowances.php?status=upcoming';

                $insertStmt->execute([
                    ':user_id' => $user['id'],
                    ':title' => $title,
                    ':message' => $message,
                    ':link' => $link
                ]);
            }
        }

        // Create notifications for promotions
        if (count($promotions) > 0) {
            $insertStmt = $pdo->prepare("
                INSERT INTO notifications (
                    user_id, type, title, message, link, is_read, created_at, priority
                ) VALUES (
                    :user_id, 'promotion', :title, :message, :link, 0, NOW(), 'normal'
                )
            ");

            foreach ($users as $user) {
                $title = 'تذكير بالترقيات المستحقة';
                $message = 'هناك ' . count($promotions) . ' موظف مستحق للترقية خلال الـ ' . $days . ' يوم القادمة';
                $link = 'promotions.php?status=upcoming';

                $insertStmt->execute([
                    ':user_id' => $user['id'],
                    ':title' => $title,
                    ':message' => $message,
                    ':link' => $link
                ]);
            }
        }

        // Log activity
        logActivity(
            $_SESSION['user_id'],
            'إرسال تذكيرات',
            'reminders',
            0,
            'تم إرسال تذكيرات للمديرين حول الاستحقاقات القادمة'
        );

        // Commit transaction
        $pdo->commit();

        flash('success_message', 'تم إرسال التذكيرات بنجاح', 'alert alert-success');
        redirect('reminders.php');
    } catch (PDOException $e) {
        // Rollback transaction on error
        $pdo->rollBack();

        error_log("Send Reminders Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء إرسال التذكيرات', 'alert alert-danger');
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-bell me-2"></i> نظام التذكيرات
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="?send_reminders=1" class="btn btn-primary">
            <i class="fas fa-paper-plane me-1"></i> إرسال تذكيرات للمديرين
        </a>
        <button class="btn btn-outline-secondary btn-print">
            <i class="fas fa-print me-1"></i> طباعة
        </button>
        <button class="btn btn-outline-success btn-export" data-table-target="#remindersTable" data-filename="reminders">
            <i class="fas fa-file-excel me-1"></i> تصدير
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="get" class="row g-3">
            <div class="col-md-3">
                <label for="type" class="form-label">نوع الاستحقاق</label>
                <select class="form-select" id="type" name="type">
                    <option value="all" <?php echo ($type === 'all') ? 'selected' : ''; ?>>الكل</option>
                    <option value="allowances" <?php echo ($type === 'allowances') ? 'selected' : ''; ?>>العلاوات</option>
                    <option value="promotions" <?php echo ($type === 'promotions') ? 'selected' : ''; ?>>الترقيات</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="department" class="form-label">القسم</label>
                <select class="form-select" id="department" name="department">
                    <option value="0">جميع الأقسام</option>
                    <?php foreach ($departments as $dept): ?>
                        <option value="<?php echo $dept['id']; ?>" <?php echo ($department == $dept['id']) ? 'selected' : ''; ?>>
                            <?php echo $dept['name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="days" class="form-label">خلال الأيام القادمة</label>
                <input type="number" class="form-control" id="days" name="days" min="1" max="365" value="<?php echo $days; ?>">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">تطبيق</button>
            </div>
        </form>
    </div>
</div>

<!-- Allowances Reminders -->
<?php if ($type === 'all' || $type === 'allowances'): ?>
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-money-bill-wave me-2"></i> تذكيرات العلاوات المستحقة خلال <?php echo $days; ?> يوم
            </h5>
        </div>
        <div class="card-body">
            <?php if (count($allowances) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="remindersTable">
                        <thead>
                            <tr>
                                <th>الرقم الوظيفي</th>
                                <th>الاسم</th>
                                <th>الدرجة</th>
                                <th>القسم</th>
                                <th>العلاوات المستلمة</th>
                                <th>تاريخ آخر علاوة</th>
                                <th>تاريخ العلاوة القادمة</th>
                                <th>الأيام المتبقية</th>
                                <th class="no-print">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($allowances as $employee): ?>
                                <?php
                                    $nextAllowanceDate = new DateTime($employee['next_allowance_date']);
                                    $today = new DateTime();
                                    $daysRemaining = $today->diff($nextAllowanceDate)->days;
                                    $isEligible = $today >= $nextAllowanceDate;
                                ?>
                                <tr>
                                    <td><?php echo $employee['employee_number']; ?></td>
                                    <td><?php echo $employee['full_name']; ?></td>
                                    <td><?php echo $employee['current_grade']; ?></td>
                                    <td><?php echo $employee['department_name']; ?></td>
                                    <td><?php echo $employee['allowances_in_current_grade']; ?> من <?php echo ALLOWANCES_PER_GRADE; ?></td>
                                    <td><?php echo $employee['last_allowance_date'] ? formatArabicDate($employee['last_allowance_date']) : '-'; ?></td>
                                    <td><?php echo formatArabicDate($employee['next_allowance_date']); ?></td>
                                    <td>
                                        <?php if ($isEligible): ?>
                                            <span class="badge bg-danger">مستحق حالياً</span>
                                        <?php else: ?>
                                            <?php echo $daysRemaining; ?> يوم
                                        <?php endif; ?>
                                    </td>
                                    <td class="no-print">
                                        <a href="employee_details.php?id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if (hasRole(['admin', 'hr']) && $isEligible): ?>
                                            <a href="add_allowance.php?employee_id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="إضافة علاوة">
                                                <i class="fas fa-plus"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> لا توجد علاوات مستحقة خلال الـ <?php echo $days; ?> يوم القادمة
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<!-- Promotions Reminders -->
<?php if ($type === 'all' || $type === 'promotions'): ?>
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-level-up-alt me-2"></i> تذكيرات الترقيات المستحقة خلال <?php echo $days; ?> يوم
            </h5>
        </div>
        <div class="card-body">
            <?php if (count($promotions) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="promotionsTable">
                        <thead>
                            <tr>
                                <th>الرقم الوظيفي</th>
                                <th>الاسم</th>
                                <th>الدرجة الحالية</th>
                                <th>القسم</th>
                                <th>التحصيل الدراسي</th>
                                <th>الحد الأقصى للدرجة</th>
                                <th>تاريخ آخر ترقية</th>
                                <th>تاريخ الترقية القادم</th>
                                <th>الأيام المتبقية</th>
                                <th class="no-print">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($promotions as $employee): ?>
                                <?php
                                    $nextPromotionDate = new DateTime($employee['next_promotion_date']);
                                    $today = new DateTime();
                                    $daysRemaining = $today->diff($nextPromotionDate)->days;
                                    $isEligible = $today >= $nextPromotionDate;
                                ?>
                                <tr>
                                    <td><?php echo $employee['employee_number']; ?></td>
                                    <td><?php echo $employee['full_name']; ?></td>
                                    <td><?php echo $employee['current_grade']; ?></td>
                                    <td><?php echo $employee['department_name']; ?></td>
                                    <td><?php echo $employee['education_level_name']; ?></td>
                                    <td><?php echo $employee['max_grade']; ?></td>
                                    <td><?php echo $employee['last_promotion_date'] ? formatArabicDate($employee['last_promotion_date']) : '-'; ?></td>
                                    <td><?php echo formatArabicDate($employee['next_promotion_date']); ?></td>
                                    <td>
                                        <?php if ($isEligible): ?>
                                            <span class="badge bg-danger">مستحق حالياً</span>
                                        <?php else: ?>
                                            <?php echo $daysRemaining; ?> يوم
                                        <?php endif; ?>
                                    </td>
                                    <td class="no-print">
                                        <a href="employee_details.php?id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if (hasRole(['admin', 'hr']) && $isEligible): ?>
                                            <a href="add_promotion.php?employee_id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="إضافة ترقية">
                                                <i class="fas fa-plus"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> لا توجد ترقيات مستحقة خلال الـ <?php echo $days; ?> يوم القادمة
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<?php
// Include footer
require_once 'includes/footer.php';
?>
