<?php
/**
 * Login Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Check if already logged in
if (isLoggedIn()) {
    redirect('index.php');
}

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $username = sanitize($_POST['username']);
    $password = $_POST['password']; // Don't sanitize password
    
    // Validate inputs
    if (empty($username) || empty($password)) {
        flash('login_message', 'يرجى إدخال اسم المستخدم وكلمة المرور', 'alert alert-danger');
    } else {
        // Attempt login
        if (loginUser($username, $password)) {
            // Redirect to dashboard
            redirect('index.php');
        } else {
            flash('login_message', 'اسم المستخدم أو كلمة المرور غير صحيحة', 'alert alert-danger');
        }
    }
}
?>

<div class="row">
    <div class="col-md-6 mx-auto">
        <div class="login-form">
            <div class="logo">
                <i class="fas fa-award"></i>
                <h2 class="mt-3"><?php echo APP_NAME; ?></h2>
                <p class="text-muted">يرجى تسجيل الدخول للوصول إلى النظام</p>
            </div>
            
            <?php flash('login_message'); ?>
            
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" class="needs-validation" novalidate>
                <div class="mb-3">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="invalid-feedback">يرجى إدخال اسم المستخدم</div>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="invalid-feedback">يرجى إدخال كلمة المرور</div>
                </div>
                
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-2"></i> تسجيل الدخول
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
