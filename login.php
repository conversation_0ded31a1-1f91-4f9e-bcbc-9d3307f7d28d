<?php
/**
 * Login Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Check if already logged in
if (isLoggedIn()) {
    redirect('index.php');
}

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $username = sanitize($_POST['username']);
    $password = $_POST['password']; // Don't sanitize password

    // Validate inputs
    if (empty($username) || empty($password)) {
        flash('login_message', 'يرجى إدخال اسم المستخدم وكلمة المرور', 'alert alert-danger');
    } else {
        // Attempt login
        if (loginUser($username, $password)) {
            // Redirect to dashboard
            redirect('index.php');
        } else {
            flash('login_message', 'اسم المستخدم أو كلمة المرور غير صحيحة', 'alert alert-danger');
        }
    }
}
?>

<div class="row justify-content-center">
    <div class="col-md-5 col-lg-4">
        <div class="card shadow-modern rounded-modern">
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <div class="brand-icon mx-auto mb-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
                        <i class="fas fa-award"></i>
                    </div>
                    <h2 class="text-gradient-primary mb-2"><?php echo APP_NAME; ?></h2>
                    <p class="text-muted">يرجى تسجيل الدخول للوصول إلى النظام</p>
                </div>

                <?php flash('login_message'); ?>

                <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" class="needs-validation" novalidate>
                    <div class="mb-4">
                        <label for="username" class="form-label">
                            <i class="fas fa-user me-2 text-primary"></i>اسم المستخدم
                        </label>
                        <input type="text" class="form-control form-control-lg" id="username" name="username" required
                               placeholder="أدخل اسم المستخدم">
                        <div class="invalid-feedback">يرجى إدخال اسم المستخدم</div>
                    </div>

                    <div class="mb-4">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-2 text-primary"></i>كلمة المرور
                        </label>
                        <input type="password" class="form-control form-control-lg" id="password" name="password" required
                               placeholder="أدخل كلمة المرور">
                        <div class="invalid-feedback">يرجى إدخال كلمة المرور</div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg btn-icon">
                            <i class="fas fa-sign-in-alt"></i>
                            تسجيل الدخول
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Additional Info Card -->
        <div class="card mt-4 shadow-modern rounded-modern">
            <div class="card-body text-center p-4">
                <h6 class="text-muted mb-2">
                    <i class="fas fa-info-circle me-2"></i>معلومات النظام
                </h6>
                <small class="text-muted">
                    نظام إدارة شؤون الموظفين والترقيات<br>
                    الإصدار 2.0 - محدث ومطور
                </small>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
