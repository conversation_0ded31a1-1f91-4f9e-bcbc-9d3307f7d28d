<?php
/**
 * Create Database Script
 * إنشاء قاعدة البيانات
 */

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database credentials
$host = 'localhost';
$user = 'root';
$pass = ''; // تم تعديل كلمة المرور بناءً على نتائج الاختبار
$dbname = 'employee_promotions_db';
$charset = 'utf8mb4';

echo "<h1>إنشاء قاعدة البيانات</h1>";

try {
    // Connect to MySQL without database
    echo "<p>محاولة الاتصال بـ MySQL...</p>";
    $pdo = new PDO("mysql:host=$host", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color:green;'>تم الاتصال بـ MySQL بنجاح!</p>";
    
    // Check if database exists
    $stmt = $pdo->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$dbname'");
    $dbExists = $stmt->rowCount() > 0;
    
    if ($dbExists) {
        echo "<p style='color:orange;'>قاعدة البيانات <strong>$dbname</strong> موجودة بالفعل.</p>";
    } else {
        // Create database
        echo "<p>إنشاء قاعدة البيانات <strong>$dbname</strong>...</p>";
        $pdo->exec("CREATE DATABASE `$dbname` CHARACTER SET $charset COLLATE {$charset}_unicode_ci");
        echo "<p style='color:green;'>تم إنشاء قاعدة البيانات <strong>$dbname</strong> بنجاح!</p>";
    }
    
    // Connect to the database
    echo "<p>محاولة الاتصال بقاعدة البيانات <strong>$dbname</strong>...</p>";
    $dbPdo = new PDO("mysql:host=$host;dbname=$dbname;charset=$charset", $user, $pass);
    $dbPdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color:green;'>تم الاتصال بقاعدة البيانات <strong>$dbname</strong> بنجاح!</p>";
    
    // Check if schema.sql exists
    $schemaFile = 'database/schema.sql';
    if (file_exists($schemaFile)) {
        echo "<p>تم العثور على ملف هيكل قاعدة البيانات <strong>$schemaFile</strong>.</p>";
        echo "<p>لاستيراد هيكل قاعدة البيانات، انقر على الزر أدناه:</p>";
        echo "<form method='post'>";
        echo "<input type='submit' name='import_schema' value='استيراد هيكل قاعدة البيانات' style='padding: 10px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;'>";
        echo "</form>";
    } else {
        echo "<p style='color:red;'>لم يتم العثور على ملف هيكل قاعدة البيانات <strong>$schemaFile</strong>.</p>";
        echo "<p>يرجى التأكد من وجود الملف في المسار الصحيح.</p>";
    }
    
    // Import schema if requested
    if (isset($_POST['import_schema']) && file_exists($schemaFile)) {
        echo "<h2>استيراد هيكل قاعدة البيانات</h2>";
        
        // Read schema file
        $sql = file_get_contents($schemaFile);
        
        // Split into individual queries
        $queries = explode(';', $sql);
        $executedQueries = 0;
        $errors = [];
        
        // Execute each query
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                try {
                    $dbPdo->exec($query);
                    $executedQueries++;
                } catch (PDOException $e) {
                    $errors[] = "خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "<br>الاستعلام: " . $query;
                }
            }
        }
        
        if (empty($errors)) {
            echo "<p style='color:green;'>تم استيراد هيكل قاعدة البيانات بنجاح. تم تنفيذ $executedQueries استعلام.</p>";
        } else {
            echo "<p style='color:red;'>حدث خطأ أثناء استيراد هيكل قاعدة البيانات:</p>";
            echo "<ul>";
            foreach ($errors as $error) {
                echo "<li>$error</li>";
            }
            echo "</ul>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color:red;'>خطأ: " . $e->getMessage() . "</p>";
}

// Add link to go back to the main page
echo "<p><a href='index.php' style='display: inline-block; margin-top: 20px; padding: 10px; background-color: #2196F3; color: white; text-decoration: none; border-radius: 4px;'>العودة إلى الصفحة الرئيسية</a></p>";
?>
