<?php
/**
 * Reset Password Page
 * صفحة إعادة تعيين كلمة المرور
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once 'config/database.php';

// Include functions if available
if (file_exists('includes/functions.php')) {
    require_once 'includes/functions.php';
}

// CSS styles
$styles = '
<style>
    body {
        font-family: Arial, sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 20px;
        direction: rtl;
    }
    .container {
        max-width: 800px;
        margin: 0 auto;
        background-color: #fff;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    h1, h2 {
        color: #007bff;
    }
    .alert-success {
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
    table, th, td {
        border: 1px solid #ddd;
    }
    th, td {
        padding: 10px;
        text-align: right;
    }
    th {
        background-color: #f2f2f2;
    }
    .btn {
        display: inline-block;
        padding: 8px 16px;
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.5;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        cursor: pointer;
        border: 1px solid transparent;
        border-radius: 4px;
        text-decoration: none;
    }
    .btn-primary {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }
    .btn-success {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    .btn-danger {
        color: #fff;
        background-color: #dc3545;
        border-color: #dc3545;
    }
    .form-group {
        margin-bottom: 15px;
    }
    label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    input[type="text"],
    input[type="password"],
    select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
    }
    .login-info {
        background-color: #f8f9fa;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-top: 20px;
    }
</style>
';

// Function to sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Start HTML output
echo '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور - نظام إدارة العلاوات والترفيع</title>
    ' . $styles . '
</head>
<body>
    <div class="container">
        <h1>إعادة تعيين كلمة المرور</h1>';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['reset_password'])) {
        $username = sanitizeInput($_POST['username']);
        $newPassword = sanitizeInput($_POST['new_password']);
        $confirmPassword = sanitizeInput($_POST['confirm_password']);
        
        // Validate input
        $errors = [];
        
        if (empty($username)) {
            $errors[] = "يرجى إدخال اسم المستخدم";
        }
        
        if (empty($newPassword)) {
            $errors[] = "يرجى إدخال كلمة المرور الجديدة";
        } elseif (strlen($newPassword) < 6) {
            $errors[] = "يجب أن تكون كلمة المرور على الأقل 6 أحرف";
        }
        
        if ($newPassword !== $confirmPassword) {
            $errors[] = "كلمة المرور وتأكيدها غير متطابقين";
        }
        
        if (empty($errors)) {
            try {
                // Check if user exists
                $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
                $stmt->execute([$username]);
                $userExists = $stmt->rowCount() > 0;
                
                if ($userExists) {
                    // Hash the new password
                    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                    
                    // Update user password
                    $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = ?");
                    $stmt->execute([$hashedPassword, $username]);
                    
                    echo '<div class="alert-success">
                        <p>تم إعادة تعيين كلمة المرور بنجاح!</p>
                    </div>';
                    
                    // Show login details
                    echo '<div class="login-info">
                        <h2>معلومات تسجيل الدخول:</h2>
                        <p><strong>اسم المستخدم:</strong> ' . $username . '</p>
                        <p><strong>كلمة المرور:</strong> ' . $newPassword . '</p>
                        <p><a href="login.php" class="btn btn-primary">انتقل إلى صفحة تسجيل الدخول</a></p>
                    </div>';
                } else {
                    echo '<div class="alert-danger">
                        <p>المستخدم غير موجود!</p>
                    </div>';
                }
            } catch (PDOException $e) {
                echo '<div class="alert-danger">
                    <p>خطأ في قاعدة البيانات: ' . $e->getMessage() . '</p>
                </div>';
            }
        } else {
            echo '<div class="alert-danger">
                <ul>';
            foreach ($errors as $error) {
                echo '<li>' . $error . '</li>';
            }
            echo '</ul>
            </div>';
        }
    }
}

// Show list of users
try {
    $stmt = $pdo->query("SELECT id, username, full_name, role FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) > 0) {
        echo '<h2>المستخدمون الحاليون:</h2>
        <table>
            <tr>
                <th>ID</th>
                <th>اسم المستخدم</th>
                <th>الاسم الكامل</th>
                <th>الدور</th>
            </tr>';
        
        foreach ($users as $user) {
            echo '<tr>
                <td>' . $user['id'] . '</td>
                <td>' . $user['username'] . '</td>
                <td>' . $user['full_name'] . '</td>
                <td>' . $user['role'] . '</td>
            </tr>';
        }
        
        echo '</table>';
    } else {
        echo '<div class="alert-danger">
            <p>لا يوجد مستخدمون في النظام!</p>
        </div>';
    }
} catch (PDOException $e) {
    echo '<div class="alert-danger">
        <p>خطأ في قاعدة البيانات: ' . $e->getMessage() . '</p>
    </div>';
}

// Reset password form
echo '<h2>إعادة تعيين كلمة المرور</h2>
<form method="post">
    <div class="form-group">
        <label for="username">اسم المستخدم:</label>
        <input type="text" id="username" name="username" required>
    </div>
    
    <div class="form-group">
        <label for="new_password">كلمة المرور الجديدة:</label>
        <input type="password" id="new_password" name="new_password" required>
    </div>
    
    <div class="form-group">
        <label for="confirm_password">تأكيد كلمة المرور:</label>
        <input type="password" id="confirm_password" name="confirm_password" required>
    </div>
    
    <button type="submit" name="reset_password" class="btn btn-primary">إعادة تعيين كلمة المرور</button>
</form>

<p><a href="index.php" class="btn btn-success">العودة إلى الصفحة الرئيسية</a></p>
<p><a href="login.php" class="btn btn-success">الذهاب إلى صفحة تسجيل الدخول</a></p>

</div>
</body>
</html>';
?>
