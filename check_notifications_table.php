<?php
/**
 * Check Notifications Table
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

// Start HTML output
echo "<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>فحص جدول التنبيهات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #0066cc; margin-top: 30px; }
        p { margin: 10px 0; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .back-link { display: inline-block; margin-top: 20px; padding: 10px 15px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; }
        .btn { display: inline-block; margin-top: 10px; padding: 5px 10px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>فحص جدول التنبيهات</h1>";

// Check if notifications table exists
echo "<h2>التحقق من وجود جدول التنبيهات</h2>";
try {
    $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'notifications'");
    $tableExists = $tableCheckStmt->rowCount() > 0;

    if ($tableExists) {
        echo "<p class='success'>✓ جدول التنبيهات موجود</p>";

        // Get table structure
        $columnsStmt = $pdo->query("DESCRIBE notifications");
        $columns = $columnsStmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<h3>هيكل جدول التنبيهات:</h3>";
        echo "<pre>";
        print_r($columns);
        echo "</pre>";

        // Check if employee_id column exists
        $employeeIdExists = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'employee_id') {
                $employeeIdExists = true;
                break;
            }
        }

        if ($employeeIdExists) {
            echo "<p class='success'>✓ عمود employee_id موجود في جدول التنبيهات</p>";
        } else {
            echo "<p class='error'>✗ عمود employee_id غير موجود في جدول التنبيهات</p>";

            // Add employee_id column
            echo "<form method='post' action=''>";
            echo "<input type='hidden' name='add_employee_id' value='1'>";
            echo "<button type='submit' class='btn'>إضافة عمود employee_id</button>";
            echo "</form>";

            if (isset($_POST['add_employee_id'])) {
                try {
                    $pdo->exec("ALTER TABLE notifications ADD COLUMN employee_id int(11) DEFAULT NULL AFTER user_id");
                    $pdo->exec("ALTER TABLE notifications ADD INDEX employee_id (employee_id)");
                    echo "<p class='success'>✓ تم إضافة عمود employee_id إلى جدول التنبيهات بنجاح</p>";
                    echo "<script>window.location.reload();</script>";
                } catch (PDOException $e) {
                    echo "<p class='error'>خطأ في إضافة عمود employee_id: " . $e->getMessage() . "</p>";
                }
            }
        }

        // Check if is_read column exists
        $isReadExists = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'is_read') {
                $isReadExists = true;
                break;
            }
        }

        if ($isReadExists) {
            echo "<p class='success'>✓ عمود is_read موجود في جدول التنبيهات</p>";
        } else {
            echo "<p class='error'>✗ عمود is_read غير موجود في جدول التنبيهات</p>";

            // Add is_read column
            echo "<form method='post' action=''>";
            echo "<input type='hidden' name='add_is_read' value='1'>";
            echo "<button type='submit' class='btn'>إضافة عمود is_read</button>";
            echo "</form>";

            if (isset($_POST['add_is_read'])) {
                try {
                    $pdo->exec("ALTER TABLE notifications ADD COLUMN is_read tinyint(1) NOT NULL DEFAULT 0 AFTER message");
                    $pdo->exec("ALTER TABLE notifications ADD INDEX is_read (is_read)");
                    echo "<p class='success'>✓ تم إضافة عمود is_read إلى جدول التنبيهات بنجاح</p>";
                    echo "<script>window.location.reload();</script>";
                } catch (PDOException $e) {
                    echo "<p class='error'>خطأ في إضافة عمود is_read: " . $e->getMessage() . "</p>";
                }
            }
        }

        // Check if read_at column exists
        $readAtExists = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'read_at') {
                $readAtExists = true;
                break;
            }
        }

        if ($readAtExists) {
            echo "<p class='success'>✓ عمود read_at موجود في جدول التنبيهات</p>";
        } else {
            echo "<p class='error'>✗ عمود read_at غير موجود في جدول التنبيهات</p>";

            // Add read_at column
            echo "<form method='post' action=''>";
            echo "<input type='hidden' name='add_read_at' value='1'>";
            echo "<button type='submit' class='btn'>إضافة عمود read_at</button>";
            echo "</form>";

            if (isset($_POST['add_read_at'])) {
                try {
                    $pdo->exec("ALTER TABLE notifications ADD COLUMN read_at datetime DEFAULT NULL AFTER is_read");
                    echo "<p class='success'>✓ تم إضافة عمود read_at إلى جدول التنبيهات بنجاح</p>";
                    echo "<script>window.location.reload();</script>";
                } catch (PDOException $e) {
                    echo "<p class='error'>خطأ في إضافة عمود read_at: " . $e->getMessage() . "</p>";
                }
            }
        }

        // Option to recreate the table
        echo "<h3>إعادة إنشاء جدول التنبيهات</h3>";
        echo "<p class='warning'>⚠️ تحذير: سيؤدي هذا الإجراء إلى حذف جدول التنبيهات الحالي وإنشائه من جديد. سيتم فقدان جميع البيانات الموجودة في الجدول.</p>";

        echo "<form method='post' action='' onsubmit=\"return confirm('هل أنت متأكد من أنك تريد إعادة إنشاء جدول التنبيهات؟ سيتم فقدان جميع البيانات الموجودة في الجدول.')\">";
        echo "<input type='hidden' name='recreate_table' value='1'>";
        echo "<button type='submit' class='btn' style='background-color: #dc3545;'>إعادة إنشاء جدول التنبيهات</button>";
        echo "</form>";

        if (isset($_POST['recreate_table'])) {
            try {
                // Drop the table
                $pdo->exec("DROP TABLE IF EXISTS notifications");

                // Create the table
                $sql = "
                    CREATE TABLE `notifications` (
                      `id` int(11) NOT NULL AUTO_INCREMENT,
                      `user_id` int(11) NOT NULL,
                      `employee_id` int(11) DEFAULT NULL,
                      `type` varchar(50) NOT NULL,
                      `title` varchar(255) NOT NULL,
                      `message` text NOT NULL,
                      `link` varchar(255) DEFAULT NULL,
                      `is_read` tinyint(1) NOT NULL DEFAULT 0,
                      `read_at` datetime DEFAULT NULL,
                      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                      `due_date` date DEFAULT NULL,
                      `priority` varchar(20) NOT NULL DEFAULT 'normal',
                      PRIMARY KEY (`id`),
                      KEY `user_id` (`user_id`),
                      KEY `employee_id` (`employee_id`),
                      KEY `type` (`type`),
                      KEY `is_read` (`is_read`),
                      KEY `due_date` (`due_date`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                ";
                $pdo->exec($sql);

                echo "<p class='success'>✓ تم إعادة إنشاء جدول التنبيهات بنجاح</p>";
                echo "<script>window.location.reload();</script>";
            } catch (PDOException $e) {
                echo "<p class='error'>خطأ في إعادة إنشاء جدول التنبيهات: " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p class='error'>✗ جدول التنبيهات غير موجود</p>";

        // Create the table
        echo "<form method='post' action=''>";
        echo "<input type='hidden' name='create_table' value='1'>";
        echo "<button type='submit' class='btn'>إنشاء جدول التنبيهات</button>";
        echo "</form>";

        if (isset($_POST['create_table'])) {
            try {
                $sql = "
                    CREATE TABLE `notifications` (
                      `id` int(11) NOT NULL AUTO_INCREMENT,
                      `user_id` int(11) NOT NULL,
                      `employee_id` int(11) DEFAULT NULL,
                      `type` varchar(50) NOT NULL,
                      `title` varchar(255) NOT NULL,
                      `message` text NOT NULL,
                      `link` varchar(255) DEFAULT NULL,
                      `is_read` tinyint(1) NOT NULL DEFAULT 0,
                      `read_at` datetime DEFAULT NULL,
                      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                      `due_date` date DEFAULT NULL,
                      `priority` varchar(20) NOT NULL DEFAULT 'normal',
                      PRIMARY KEY (`id`),
                      KEY `user_id` (`user_id`),
                      KEY `employee_id` (`employee_id`),
                      KEY `type` (`type`),
                      KEY `is_read` (`is_read`),
                      KEY `due_date` (`due_date`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                ";
                $pdo->exec($sql);

                echo "<p class='success'>✓ تم إنشاء جدول التنبيهات بنجاح</p>";
                echo "<script>window.location.reload();</script>";
            } catch (PDOException $e) {
                echo "<p class='error'>خطأ في إنشاء جدول التنبيهات: " . $e->getMessage() . "</p>";
            }
        }
    }
} catch (PDOException $e) {
    echo "<p class='error'>خطأ في التحقق من وجود جدول التنبيهات: " . $e->getMessage() . "</p>";
}

// Add links to navigate
echo "
    <div style='margin-top: 30px;'>
        <a href='generate_notifications.php' class='back-link'>العودة إلى صفحة توليد التنبيهات</a>
    </div>
</body>
</html>";
?>
