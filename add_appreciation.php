<?php
/**
 * Add Appreciation Letter Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and proper role
requireRole(['admin', 'hr']);

// Check if employee ID is provided
$employeeId = isset($_GET['employee_id']) ? (int)$_GET['employee_id'] : 0;
$employee = null;

// Get employee details if ID is provided
if ($employeeId > 0) {
    try {
        $stmt = $pdo->prepare("
            SELECT e.*, d.name as department_name
            FROM employees e
            JOIN departments d ON e.department_id = d.id
            WHERE e.id = :id
        ");
        $stmt->execute([':id' => $employeeId]);
        $employee = $stmt->fetch();
        
        if (!$employee) {
            flash('error_message', 'الموظف غير موجود', 'alert alert-danger');
            redirect('appreciation.php');
        }
    } catch (PDOException $e) {
        error_log("Get Employee Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الموظف', 'alert alert-danger');
        redirect('appreciation.php');
    }
}

// Get all employees for dropdown
try {
    $stmt = $pdo->query("
        SELECT e.id, e.employee_number, e.full_name, d.name as department_name
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        ORDER BY e.full_name ASC
    ");
    $employees = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Employees Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الموظفين', 'alert alert-danger');
    $employees = [];
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $selectedEmployeeId = isset($_POST['employee_id']) ? (int)$_POST['employee_id'] : $employeeId;
    $letterNumber = sanitize($_POST['letter_number']);
    $letterDate = sanitize($_POST['letter_date']);
    $issuer = sanitize($_POST['issuer']);
    $notes = sanitize($_POST['notes']);
    
    // Set months reduction based on issuer
    $monthsReduction = ($issuer === 'prime_minister') ? PM_LETTER_MONTHS : REGULAR_LETTER_MONTHS;
    
    // Validate inputs
    $errors = [];
    
    if ($selectedEmployeeId <= 0) {
        $errors[] = 'يرجى اختيار الموظف';
    }
    
    if (empty($letterNumber)) {
        $errors[] = 'رقم الكتاب مطلوب';
    }
    
    if (empty($letterDate)) {
        $errors[] = 'تاريخ الكتاب مطلوب';
    }
    
    // Handle file upload
    $filePath = null;
    if (isset($_FILES['letter_file']) && $_FILES['letter_file']['error'] === UPLOAD_ERR_OK) {
        $fileName = $_FILES['letter_file']['name'];
        $fileSize = $_FILES['letter_file']['size'];
        $fileTmpName = $_FILES['letter_file']['tmp_name'];
        $fileType = $_FILES['letter_file']['type'];
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        // Check file size
        if ($fileSize > MAX_FILE_SIZE) {
            $errors[] = 'حجم الملف يجب أن يكون أقل من ' . (MAX_FILE_SIZE / 1024 / 1024) . ' ميجابايت';
        }
        
        // Check file extension
        if (!in_array($fileExt, ALLOWED_FILE_TYPES)) {
            $errors[] = 'نوع الملف غير مسموح به. الأنواع المسموح بها: ' . implode(', ', ALLOWED_FILE_TYPES);
        }
        
        // If no errors, move file to uploads directory
        if (empty($errors)) {
            $newFileName = uniqid() . '_' . $fileName;
            $uploadPath = UPLOAD_DIR . 'appreciation_letters/' . $newFileName;
            
            if (move_uploaded_file($fileTmpName, $uploadPath)) {
                $filePath = 'uploads/appreciation_letters/' . $newFileName;
            } else {
                $errors[] = 'حدث خطأ أثناء رفع الملف';
            }
        }
    }
    
    // If no errors, add appreciation letter
    if (empty($errors)) {
        try {
            // Start transaction
            $pdo->beginTransaction();
            
            // Insert appreciation letter record
            $stmt = $pdo->prepare("
                INSERT INTO appreciation_letters (
                    employee_id, letter_date, letter_number, issuer, file_path, months_reduction, notes, created_by
                ) VALUES (
                    :employee_id, :letter_date, :letter_number, :issuer, :file_path, :months_reduction, :notes, :created_by
                )
            ");
            
            $stmt->execute([
                ':employee_id' => $selectedEmployeeId,
                ':letter_date' => $letterDate,
                ':letter_number' => $letterNumber,
                ':issuer' => $issuer,
                ':file_path' => $filePath,
                ':months_reduction' => $monthsReduction,
                ':notes' => $notes,
                ':created_by' => $_SESSION['user_id']
            ]);
            
            $letterId = $pdo->lastInsertId();
            
            // Update employee's next allowance and promotion dates
            // Get employee current data
            $empStmt = $pdo->prepare("
                SELECT next_allowance_date, next_promotion_date
                FROM employees
                WHERE id = :id
            ");
            $empStmt->execute([':id' => $selectedEmployeeId]);
            $empData = $empStmt->fetch();
            
            // Calculate new dates with reduction
            $nextAllowanceDate = new DateTime($empData['next_allowance_date']);
            $nextAllowanceDate->sub(new DateInterval('P' . $monthsReduction . 'M'));
            
            $nextPromotionDate = new DateTime($empData['next_promotion_date']);
            $nextPromotionDate->sub(new DateInterval('P' . $monthsReduction . 'M'));
            
            // Update employee record
            $updateStmt = $pdo->prepare("
                UPDATE employees SET
                    next_allowance_date = :next_allowance_date,
                    next_promotion_date = :next_promotion_date
                WHERE id = :id
            ");
            
            $updateStmt->execute([
                ':next_allowance_date' => $nextAllowanceDate->format('Y-m-d'),
                ':next_promotion_date' => $nextPromotionDate->format('Y-m-d'),
                ':id' => $selectedEmployeeId
            ]);
            
            // Get employee name for log
            $nameStmt = $pdo->prepare("SELECT full_name FROM employees WHERE id = :id");
            $nameStmt->execute([':id' => $selectedEmployeeId]);
            $employeeName = $nameStmt->fetch()['full_name'];
            
            // Log activity
            logActivity(
                $_SESSION['user_id'],
                'إضافة كتاب شكر',
                'appreciation_letters',
                $letterId,
                'تمت إضافة كتاب شكر للموظف: ' . $employeeName
            );
            
            // Commit transaction
            $pdo->commit();
            
            // Redirect to appreciation letters page
            flash('success_message', 'تمت إضافة كتاب الشكر بنجاح', 'alert alert-success');
            redirect('employee_details.php?id=' . $selectedEmployeeId);
            
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            
            error_log("Add Appreciation Letter Error: " . $e->getMessage());
            flash('error_message', 'حدث خطأ أثناء إضافة كتاب الشكر', 'alert alert-danger');
        }
    } else {
        // Display errors
        $errorMessage = '<ul class="mb-0">';
        foreach ($errors as $error) {
            $errorMessage .= '<li>' . $error . '</li>';
        }
        $errorMessage .= '</ul>';
        
        flash('error_message', $errorMessage, 'alert alert-danger');
    }
}

// Set default letter date to today
$defaultLetterDate = date('Y-m-d');
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-certificate me-2"></i> إضافة كتاب شكر
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="appreciation.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى قائمة كتب الشكر
        </a>
    </div>
</div>

<?php if ($employee): ?>
    <!-- Employee Information Card -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-user me-2"></i> معلومات الموظف
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="fw-bold">الرقم الوظيفي:</div>
                    <div><?php echo $employee['employee_number']; ?></div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="fw-bold">الاسم:</div>
                    <div><?php echo $employee['full_name']; ?></div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="fw-bold">القسم:</div>
                    <div><?php echo $employee['department_name']; ?></div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="fw-bold">الدرجة الحالية:</div>
                    <div>الدرجة <?php echo $employee['current_grade']; ?></div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="fw-bold">تاريخ العلاوة القادمة:</div>
                    <div><?php echo formatArabicDate($employee['next_allowance_date']); ?></div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="fw-bold">تاريخ الترفيع القادم:</div>
                    <div><?php echo formatArabicDate($employee['next_promotion_date']); ?></div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Add Appreciation Letter Form -->
<div class="card">
    <div class="card-header bg-warning text-dark">
        <h5 class="mb-0">
            <i class="fas fa-plus-circle me-2"></i> إضافة كتاب شكر جديد
        </h5>
    </div>
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF'] . ($employeeId ? '?employee_id=' . $employeeId : ''); ?>" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
            <?php if (!$employee): ?>
                <div class="mb-3">
                    <label for="employee_id" class="form-label">الموظف <span class="text-danger">*</span></label>
                    <select class="form-select" id="employee_id" name="employee_id" required>
                        <option value="">اختر الموظف</option>
                        <?php foreach ($employees as $emp): ?>
                            <option value="<?php echo $emp['id']; ?>">
                                <?php echo $emp['full_name']; ?> (<?php echo $emp['employee_number']; ?>) - <?php echo $emp['department_name']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار الموظف</div>
                </div>
            <?php else: ?>
                <input type="hidden" name="employee_id" value="<?php echo $employeeId; ?>">
            <?php endif; ?>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="letter_number" class="form-label">رقم الكتاب <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="letter_number" name="letter_number" required>
                    <div class="invalid-feedback">يرجى إدخال رقم الكتاب</div>
                </div>
                <div class="col-md-6">
                    <label for="letter_date" class="form-label">تاريخ الكتاب <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="letter_date" name="letter_date" value="<?php echo $defaultLetterDate; ?>" required>
                    <div class="invalid-feedback">يرجى إدخال تاريخ الكتاب</div>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="issuer" class="form-label">الجهة المانحة <span class="text-danger">*</span></label>
                    <select class="form-select" id="issuer" name="issuer" required>
                        <option value="regular">كتاب شكر عادي (تخفيض شهر واحد)</option>
                        <option value="prime_minister">كتاب شكر رئيس الوزراء (تخفيض 6 أشهر)</option>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار الجهة المانحة</div>
                </div>
                <div class="col-md-6">
                    <label for="letter_file" class="form-label">نسخة من الكتاب</label>
                    <input type="file" class="form-control" id="letter_file" name="letter_file">
                    <div class="form-text">الملفات المسموح بها: PDF, JPG, JPEG, PNG (الحد الأقصى: <?php echo MAX_FILE_SIZE / 1024 / 1024; ?> ميجابايت)</div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="notes" class="form-label">ملاحظات</label>
                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                سيتم تحديث تواريخ استحقاق العلاوة والترفيع للموظف تلقائياً بعد إضافة كتاب الشكر.
                <ul class="mb-0 mt-2">
                    <li>كتاب الشكر العادي: تخفيض <?php echo REGULAR_LETTER_MONTHS; ?> شهر</li>
                    <li>كتاب شكر رئيس الوزراء: تخفيض <?php echo PM_LETTER_MONTHS; ?> أشهر</li>
                </ul>
            </div>
            
            <div class="text-center mt-4">
                <button type="submit" class="btn btn-warning px-5">
                    <i class="fas fa-save me-1"></i> حفظ كتاب الشكر
                </button>
                <a href="<?php echo $employeeId ? 'employee_details.php?id=' . $employeeId : 'appreciation.php'; ?>" class="btn btn-secondary px-5">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
