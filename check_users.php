<?php
/**
 * Check Users and Create Admin
 * التحقق من المستخدمين وإنشاء مستخدم مدير جديد
 */

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once 'config/database.php';

echo "<h1>التحقق من المستخدمين وإنشاء مستخدم مدير</h1>";

try {
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<p style='color:red;'>جدول المستخدمين غير موجود!</p>";
        echo "<p>يجب إنشاء جدول المستخدمين أولاً. تأكد من استيراد ملف schema.sql.</p>";
        exit;
    }
    
    echo "<p style='color:green;'>جدول المستخدمين موجود.</p>";
    
    // Check existing users
    $stmt = $pdo->query("SELECT id, username, full_name, role FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) > 0) {
        echo "<h2>المستخدمون الحاليون:</h2>";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>الدور</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['username'] . "</td>";
            echo "<td>" . $user['full_name'] . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p style='color:orange;'>لا يوجد مستخدمون في النظام.</p>";
    }
    
    // Form to create new admin user
    echo "<h2>إنشاء مستخدم مدير جديد</h2>";
    echo "<form method='post'>";
    echo "<table>";
    echo "<tr><td>اسم المستخدم:</td><td><input type='text' name='username' value='admin' required></td></tr>";
    echo "<tr><td>كلمة المرور:</td><td><input type='text' name='password' value='admin123' required></td></tr>";
    echo "<tr><td>الاسم الكامل:</td><td><input type='text' name='full_name' value='مدير النظام' required></td></tr>";
    echo "<tr><td>البريد الإلكتروني:</td><td><input type='email' name='email' value='<EMAIL>' required></td></tr>";
    echo "<tr><td colspan='2'><input type='submit' name='create_admin' value='إنشاء مستخدم مدير' style='padding: 5px 10px;'></td></tr>";
    echo "</table>";
    echo "</form>";
    
    // Process form submission
    if (isset($_POST['create_admin'])) {
        $username = $_POST['username'];
        $password = $_POST['password'];
        $fullName = $_POST['full_name'];
        $email = $_POST['email'];
        
        // Check if username already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $userExists = $stmt->rowCount() > 0;
        
        if ($userExists) {
            echo "<p style='color:red;'>اسم المستخدم موجود بالفعل. سيتم تحديث كلمة المرور فقط.</p>";
            
            // Update password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = ?");
            $stmt->execute([$hashedPassword, $username]);
            
            echo "<p style='color:green;'>تم تحديث كلمة المرور بنجاح!</p>";
        } else {
            // Create new admin user
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            $stmt = $pdo->prepare("
                INSERT INTO users (username, password, full_name, email, role, status, created_at)
                VALUES (?, ?, ?, ?, 'admin', 'active', NOW())
            ");
            
            $stmt->execute([$username, $hashedPassword, $fullName, $email]);
            
            echo "<p style='color:green;'>تم إنشاء مستخدم المدير بنجاح!</p>";
        }
        
        // Show login details
        echo "<div style='background-color: #f8f9fa; padding: 10px; border: 1px solid #ddd; margin-top: 10px;'>";
        echo "<h3>معلومات تسجيل الدخول:</h3>";
        echo "<p><strong>اسم المستخدم:</strong> " . $username . "</p>";
        echo "<p><strong>كلمة المرور:</strong> " . $password . "</p>";
        echo "<p><a href='login.php' style='display: inline-block; padding: 5px 10px; background-color: #007bff; color: white; text-decoration: none;'>انتقل إلى صفحة تسجيل الدخول</a></p>";
        echo "</div>";
        
        // Verify password hash
        echo "<h3>التحقق من تشفير كلمة المرور:</h3>";
        $stmt = $pdo->prepare("SELECT password FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            $storedHash = $user['password'];
            $verifyResult = password_verify($password, $storedHash);
            
            echo "<p>كلمة المرور المدخلة: " . $password . "</p>";
            echo "<p>التشفير المخزن: " . $storedHash . "</p>";
            echo "<p>نتيجة التحقق: " . ($verifyResult ? "<span style='color:green;'>صحيحة</span>" : "<span style='color:red;'>غير صحيحة</span>") . "</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color:red;'>خطأ: " . $e->getMessage() . "</p>";
}

// Add link to go back to the main page
echo "<p><a href='index.php' style='display: inline-block; margin-top: 20px; padding: 5px 10px; background-color: #6c757d; color: white; text-decoration: none;'>العودة إلى الصفحة الرئيسية</a></p>";
?>
