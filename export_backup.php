<?php
/**
 * Export Backup Page - تصدير واستيراد قاعدة البيانات
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and admin role
requireRole(['admin']);

// Create backup directory if it doesn't exist
$backupDir = 'backups';
if (!file_exists($backupDir)) {
    mkdir($backupDir, 0755, true);
}

// Check if test action is requested
if (isset($_GET['action']) && $_GET['action'] === 'test') {
    // Set headers for file download
    header('Content-Type: text/plain; charset=utf-8');
    header('Content-Disposition: attachment; filename="test_sql_' . date('Y-m-d_H-i-s') . '.sql"');

    // Create a simple test SQL file
    echo "-- ملف SQL للاختبار\n";
    echo "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n\n";

    echo "-- إنشاء جدول اختبار\n";
    echo "DROP TABLE IF EXISTS `test_table`;\n";
    echo "CREATE TABLE `test_table` (\n";
    echo "  `id` int(11) NOT NULL AUTO_INCREMENT,\n";
    echo "  `name` varchar(50) NOT NULL,\n";
    echo "  `created_at` datetime NOT NULL,\n";
    echo "  PRIMARY KEY (`id`)\n";
    echo ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;\n\n";

    echo "-- إدخال بيانات اختبار\n";
    echo "INSERT INTO `test_table` (`id`, `name`, `created_at`) VALUES\n";
    echo "(1, 'اختبار 1', '2023-01-01 00:00:00'),\n";
    echo "(2, 'اختبار 2', '2023-01-02 00:00:00'),\n";
    echo "(3, 'اختبار 3', '2023-01-03 00:00:00');\n\n";

    echo "-- انتهى ملف الاختبار\n";

    // Stop execution after export
    exit;
}

// Check if export action is requested
if (isset($_GET['action']) && $_GET['action'] === 'export') {
    // Set headers for file download
    header('Content-Type: text/plain; charset=utf-8');
    header('Content-Disposition: attachment; filename="database_backup_' . date('Y-m-d_H-i-s') . '.sql"');

    try {
        // Use existing PDO connection
        $pdo = $GLOBALS['pdo'];

        // Output header with clear instructions
        echo "-- نظام إدارة العلاوات والترفيع وكتب الشكر - نسخة احتياطية\n";
        echo "-- تاريخ النسخ الاحتياطي: " . date('Y-m-d H:i:s') . "\n";
        echo "-- ملاحظة: يجب استخدام هذا الملف مع صفحة استيراد قاعدة البيانات في النظام\n";
        echo "-- لا تقم بتعديل محتويات هذا الملف\n\n";

        // Add database selection statement
        echo "-- اختيار قاعدة البيانات\n";
        echo "USE `" . DB_NAME . "`;\n\n";

        // Disable foreign key checks and autocommit
        echo "-- تعطيل فحص المفاتيح الخارجية والتنفيذ التلقائي\n";
        echo "SET FOREIGN_KEY_CHECKS=0;\n";
        echo "SET AUTOCOMMIT=0;\n";
        echo "START TRANSACTION;\n\n";

        // Get all tables
        $tables = [];
        $result = $pdo->query("SHOW TABLES");
        while ($row = $result->fetch(PDO::FETCH_NUM)) {
            $tables[] = $row[0];
        }

        // Export each table structure and data
        foreach ($tables as $table) {
            echo "-- -----------------------------\n";
            echo "-- بنية وبيانات جدول `$table`\n";
            echo "-- -----------------------------\n";

            // Get table structure
            $result = $pdo->query("SHOW CREATE TABLE `$table`");
            $row = $result->fetch(PDO::FETCH_NUM);
            echo "DROP TABLE IF EXISTS `$table`;\n";
            echo $row[1] . ";\n\n";

            // Get table data
            $result = $pdo->query("SELECT * FROM `$table`");
            $rows = $result->fetchAll(PDO::FETCH_ASSOC);

            if (count($rows) > 0) {
                echo "-- إدخال بيانات جدول `$table`\n";

                // Get column names
                $columns = array_keys($rows[0]);
                $columnList = '`' . implode('`, `', $columns) . '`';

                // Insert data in smaller batches to avoid issues
                $batchSize = 50;
                $batchCount = ceil(count($rows) / $batchSize);

                for ($i = 0; $i < $batchCount; $i++) {
                    $batchRows = array_slice($rows, $i * $batchSize, $batchSize);

                    if (!empty($batchRows)) {
                        echo "INSERT INTO `$table` ($columnList) VALUES\n";

                        $rowCount = count($batchRows);
                        foreach ($batchRows as $j => $row) {
                            $values = [];
                            foreach ($row as $value) {
                                if ($value === null) {
                                    $values[] = 'NULL';
                                } else {
                                    $values[] = $pdo->quote($value);
                                }
                            }

                            echo "(" . implode(', ', $values) . ")";
                            if ($j < $rowCount - 1) {
                                echo ",\n";
                            } else {
                                echo ";\n\n";
                            }
                        }
                    }
                }
            } else {
                echo "-- جدول `$table` لا يحتوي على بيانات\n\n";
            }
        }

        // Enable foreign key checks and commit transaction
        echo "-- إعادة تفعيل فحص المفاتيح الخارجية وتنفيذ التعديلات\n";
        echo "SET FOREIGN_KEY_CHECKS=1;\n";
        echo "COMMIT;\n";
        echo "SET AUTOCOMMIT=1;\n\n";

        echo "-- انتهت عملية النسخ الاحتياطي بنجاح\n";
        echo "-- عدد الجداول: " . count($tables) . "\n";

    } catch (PDOException $e) {
        echo "-- Error: " . $e->getMessage();
    }

    // Stop execution after export
    exit;
}

// Process restore request
if (isset($_POST['restore_backup']) && isset($_FILES['backup_file'])) {
    try {
        $uploadedFile = $_FILES['backup_file'];

        // Check for upload errors
        if ($uploadedFile['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('حدث خطأ أثناء رفع الملف. رمز الخطأ: ' . $uploadedFile['error']);
        }

        // Check file type
        $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));
        if ($fileExtension !== 'sql') {
            throw new Exception('نوع الملف غير صالح. يجب أن يكون الملف بتنسيق SQL.');
        }

        // Read the SQL file
        $sql = file_get_contents($uploadedFile['tmp_name']);

        // Check if file is empty
        if (empty($sql)) {
            throw new Exception('ملف SQL فارغ. يرجى التحقق من الملف.');
        }

        // Create a temporary log file for debugging
        $logFile = 'backups/restore_log_' . date('Y-m-d_H-i-s') . '.txt';
        file_put_contents($logFile, "بدء عملية الاستعادة: " . date('Y-m-d H:i:s') . "\n");
        file_put_contents($logFile, "حجم الملف: " . filesize($uploadedFile['tmp_name']) . " بايت\n", FILE_APPEND);

        // Get database connection
        $pdo = $GLOBALS['pdo']; // Use existing PDO connection

        // Disable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS=0");
        file_put_contents($logFile, "تم تعطيل فحص المفاتيح الخارجية\n", FILE_APPEND);

        // Split SQL file into individual queries
        // Use a more reliable method to split SQL queries
        $sqlLines = explode("\n", $sql);
        $query = '';
        $queryCount = 0;
        $errorCount = 0;

        file_put_contents($logFile, "بدء تنفيذ الاستعلامات...\n", FILE_APPEND);

        foreach ($sqlLines as $line) {
            // Skip comments and empty lines
            if (substr(trim($line), 0, 2) == '--' || trim($line) == '') {
                continue;
            }

            // Add this line to the current query
            $query .= $line . "\n";

            // If this line ends with a semicolon, execute the query
            if (substr(trim($line), -1, 1) == ';') {
                try {
                    $pdo->exec($query);
                    $queryCount++;

                    // Log every 100 queries to avoid huge log files
                    if ($queryCount % 100 == 0) {
                        file_put_contents($logFile, "تم تنفيذ $queryCount استعلام\n", FILE_APPEND);
                    }
                } catch (PDOException $e) {
                    $errorCount++;
                    file_put_contents($logFile, "خطأ في الاستعلام: " . $query . "\n", FILE_APPEND);
                    file_put_contents($logFile, "رسالة الخطأ: " . $e->getMessage() . "\n", FILE_APPEND);

                    // Continue despite errors
                    if ($errorCount > 10) {
                        throw new Exception('تم تجاوز الحد الأقصى للأخطاء (10). توقفت عملية الاستعادة.');
                    }
                }

                // Reset the query
                $query = '';
            }
        }

        // Enable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS=1");
        file_put_contents($logFile, "تم إعادة تفعيل فحص المفاتيح الخارجية\n", FILE_APPEND);
        file_put_contents($logFile, "انتهت عملية الاستعادة. تم تنفيذ $queryCount استعلام بنجاح مع $errorCount أخطاء.\n", FILE_APPEND);

        // Log activity
        logActivity(
            $_SESSION['user_id'],
            'استعادة نسخة احتياطية',
            'backup',
            0,
            'تم استعادة النسخة الاحتياطية: ' . $uploadedFile['name']
        );

        flash('success_message', 'تم استعادة النسخة الاحتياطية بنجاح. تم تنفيذ ' . $queryCount . ' استعلام مع ' . $errorCount . ' أخطاء. يمكنك الاطلاع على سجل الاستعادة في ملف: ' . $logFile, 'alert alert-success');
    } catch (Exception $e) {
        error_log("Restore Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء استعادة النسخة الاحتياطية: ' . $e->getMessage(), 'alert alert-danger');
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-database me-2"></i> تصدير واستيراد قاعدة البيانات
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى الرئيسية
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2"></i> تصدير قاعدة البيانات
                </h5>
            </div>
            <div class="card-body">
                <p>قم بتصدير قاعدة البيانات الحالية إلى ملف SQL. يمكنك استخدام هذا الملف لاستعادة البيانات في حالة حدوث مشكلة.</p>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> سيتم تنزيل ملف SQL يحتوي على جميع البيانات الموجودة في قاعدة البيانات. احتفظ بهذا الملف في مكان آمن.
                </div>

                <a href="export_backup.php?action=export" class="btn btn-primary w-100 mb-3" id="exportBtn">
                    <i class="fas fa-download me-1"></i> تصدير قاعدة البيانات
                </a>

                <div class="alert alert-light border mt-4">
                    <h6><i class="fas fa-question-circle me-2"></i> هل تواجه مشكلة في استعادة النسخة الاحتياطية؟</h6>
                    <p class="small mb-2">إذا كنت تواجه مشكلة في استعادة النسخة الاحتياطية، يرجى التأكد من:</p>
                    <ul class="small mb-2">
                        <li>أن الملف الذي تحاول استعادته هو ملف SQL صالح</li>
                        <li>أن حجم الملف لا يتجاوز الحد المسموح به للرفع (عادة 2 ميجابايت)</li>
                        <li>أن لديك صلاحيات كافية لإجراء عمليات على قاعدة البيانات</li>
                    </ul>
                    <p class="small">يمكنك تجربة استعادة ملف SQL بسيط للتأكد من أن النظام يعمل بشكل صحيح:</p>
                    <a href="export_backup.php?action=test" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-vial me-1"></i> تنزيل ملف SQL للاختبار
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-upload me-2"></i> استيراد قاعدة البيانات
                </h5>
            </div>
            <div class="card-body">
                <p>قم باستيراد قاعدة البيانات من ملف SQL. سيتم استبدال جميع البيانات الحالية بالبيانات من الملف.</p>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> استيراد قاعدة البيانات سيؤدي إلى حذف جميع البيانات الحالية واستبدالها بالبيانات من الملف.
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>تعليمات الاستيراد:</strong>
                    <ol class="mb-0 small">
                        <li>تأكد من أن الملف الذي تريد استيراده هو ملف SQL تم تصديره من هذا النظام.</li>
                        <li>يجب أن يكون حجم الملف أقل من 2 ميجابايت (إذا كان أكبر، يرجى تقسيمه إلى ملفات أصغر).</li>
                        <li>قد تستغرق عملية الاستيراد بعض الوقت حسب حجم البيانات.</li>
                        <li>بعد الانتهاء، ستظهر رسالة تؤكد نجاح العملية أو توضح الخطأ الذي حدث.</li>
                        <li>يمكنك الاطلاع على سجل الاستعادة للتحقق من تفاصيل العملية.</li>
                    </ol>
                </div>

                <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" enctype="multipart/form-data" id="restoreForm">
                    <div class="mb-3">
                        <label for="backup_file" class="form-label">اختر ملف SQL</label>
                        <input type="file" class="form-control" id="backup_file" name="backup_file" accept=".sql" required>
                    </div>
                    <button type="submit" name="restore_backup" class="btn btn-warning w-100" id="restoreBtn">
                        <i class="fas fa-upload me-1"></i> استيراد قاعدة البيانات
                    </button>
                </form>

                <!-- Progress bar (hidden by default) -->
                <div class="progress mt-3" id="restoreProgress" style="display: none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" role="progressbar" style="width: 0%"></div>
                </div>
                <small class="text-muted mt-2" id="restoreStatus" style="display: none;">جاري استيراد قاعدة البيانات...</small>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>

<script>
// Restore progress simulation
document.addEventListener('DOMContentLoaded', function() {
    // Restore form elements
    const restoreForm = document.getElementById('restoreForm');
    const restoreBtn = document.getElementById('restoreBtn');
    const restoreProgress = document.getElementById('restoreProgress');
    const restoreProgressBar = restoreProgress.querySelector('.progress-bar');
    const restoreStatus = document.getElementById('restoreStatus');

    // Restore form submission
    if (restoreForm) {
        restoreForm.addEventListener('submit', function(e) {
            // Show confirmation dialog
            if (!confirm('هل أنت متأكد من رغبتك في استيراد قاعدة البيانات؟ سيتم حذف جميع البيانات الحالية.')) {
                e.preventDefault();
                return false;
            }

            // Show progress bar and disable button
            restoreProgress.style.display = 'flex';
            restoreStatus.style.display = 'block';
            restoreBtn.disabled = true;
            restoreBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري استيراد قاعدة البيانات...';

            // Simulate progress
            let progress = 0;
            const interval = setInterval(function() {
                progress += Math.random() * 10;
                if (progress > 100) progress = 100;

                restoreProgressBar.style.width = progress + '%';
                restoreProgressBar.setAttribute('aria-valuenow', progress);

                if (progress < 30) {
                    restoreStatus.textContent = 'جاري قراءة ملف قاعدة البيانات...';
                } else if (progress < 60) {
                    restoreStatus.textContent = 'جاري إعادة إنشاء هيكل الجداول...';
                } else if (progress < 90) {
                    restoreStatus.textContent = 'جاري استيراد البيانات...';
                } else {
                    restoreStatus.textContent = 'جاري إكمال عملية الاستيراد...';
                }

                if (progress === 100) {
                    clearInterval(interval);
                    // Form will submit normally and page will refresh
                }
            }, 500);
        });
    }
});
</script>
