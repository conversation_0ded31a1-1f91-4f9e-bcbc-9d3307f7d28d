<?php
/**
 * Direct Backup Page - تصدير قاعدة البيانات مباشرة
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include database configuration
require_once 'config/config.php';

// Require login and admin role
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

// Set headers for file download
header('Content-Type: text/plain');
header('Content-Disposition: attachment; filename="database_backup_' . date('Y-m-d_H-i-s') . '.sql"');

// Connect to database
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Output header
    echo "-- نظام إدارة العلاوات والترفيع وكتب الشكر - نسخة احتياطية مباشرة\n";
    echo "-- تاريخ النسخ الاحتياطي: " . date('Y-m-d H:i:s') . "\n\n";
    echo "SET FOREIGN_KEY_CHECKS=0;\n\n";
    
    // Get all tables
    $tables = [];
    $result = $pdo->query("SHOW TABLES");
    while ($row = $result->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }
    
    // Export each table structure and data
    foreach ($tables as $table) {
        // Get table structure
        $result = $pdo->query("SHOW CREATE TABLE `$table`");
        $row = $result->fetch(PDO::FETCH_NUM);
        echo "DROP TABLE IF EXISTS `$table`;\n";
        echo $row[1] . ";\n\n";
        
        // Get table data
        $result = $pdo->query("SELECT * FROM `$table`");
        $rows = $result->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($rows) > 0) {
            // Get column names
            $columns = array_keys($rows[0]);
            $columnList = '`' . implode('`, `', $columns) . '`';
            
            // Insert data in batches
            $batchSize = 100;
            $batchCount = ceil(count($rows) / $batchSize);
            
            for ($i = 0; $i < $batchCount; $i++) {
                $batchRows = array_slice($rows, $i * $batchSize, $batchSize);
                
                if (!empty($batchRows)) {
                    echo "INSERT INTO `$table` ($columnList) VALUES\n";
                    
                    $rowCount = count($batchRows);
                    foreach ($batchRows as $j => $row) {
                        $values = [];
                        foreach ($row as $value) {
                            if ($value === null) {
                                $values[] = 'NULL';
                            } else {
                                $values[] = $pdo->quote($value);
                            }
                        }
                        
                        echo "(" . implode(', ', $values) . ")";
                        if ($j < $rowCount - 1) {
                            echo ",\n";
                        } else {
                            echo ";\n\n";
                        }
                    }
                }
            }
        }
    }
    
    echo "SET FOREIGN_KEY_CHECKS=1;\n";
    
} catch (PDOException $e) {
    echo "-- Error: " . $e->getMessage();
}
?>
