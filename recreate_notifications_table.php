<?php
/**
 * Recreate Notifications Table
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

// Start HTML output
echo "<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعادة إنشاء جدول التنبيهات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #0066cc; margin-top: 30px; }
        p { margin: 10px 0; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .back-link { display: inline-block; margin-top: 20px; padding: 10px 15px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>إعادة إنشاء جدول التنبيهات</h1>";

// Recreate notifications table
echo "<h2>إعادة إنشاء جدول التنبيهات</h2>";

try {
    // Drop the table if it exists
    $pdo->exec("DROP TABLE IF EXISTS notifications");
    echo "<p class='success'>✓ تم حذف جدول التنبيهات القديم بنجاح</p>";

    // Create the table with a simple structure
    $sql = "
        CREATE TABLE `notifications` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) NOT NULL,
          `employee_id` int(11) DEFAULT NULL,
          `type` varchar(50) NOT NULL,
          `title` varchar(255) NOT NULL,
          `message` text NOT NULL,
          `link` varchar(255) DEFAULT NULL,
          `is_read` tinyint(1) NOT NULL DEFAULT 0,
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `priority` varchar(20) NOT NULL DEFAULT 'normal',
          PRIMARY KEY (`id`),
          KEY `user_id` (`user_id`),
          KEY `employee_id` (`employee_id`),
          KEY `type` (`type`),
          KEY `is_read` (`is_read`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql);
    echo "<p class='success'>✓ تم إنشاء جدول التنبيهات الجديد بنجاح</p>";

    // Check the table structure
    $columnsStmt = $pdo->query("DESCRIBE notifications");
    $columns = $columnsStmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h3>هيكل جدول التنبيهات الجديد:</h3>";
    echo "<pre>";
    print_r($columns);
    echo "</pre>";

    // Check if employee_id column exists
    $employeeIdExists = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'employee_id') {
            $employeeIdExists = true;
            break;
        }
    }

    if ($employeeIdExists) {
        echo "<p class='success'>✓ عمود employee_id موجود في جدول التنبيهات</p>";
    } else {
        echo "<p class='error'>✗ عمود employee_id غير موجود في جدول التنبيهات</p>";
    }

    // Check if is_read column exists
    $isReadExists = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'is_read') {
            $isReadExists = true;
            break;
        }
    }

    if ($isReadExists) {
        echo "<p class='success'>✓ عمود is_read موجود في جدول التنبيهات</p>";
    } else {
        echo "<p class='error'>✗ عمود is_read غير موجود في جدول التنبيهات</p>";
    }

    // Check if link column exists
    $linkExists = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'link') {
            $linkExists = true;
            break;
        }
    }

    if ($linkExists) {
        echo "<p class='success'>✓ عمود link موجود في جدول التنبيهات</p>";
    } else {
        echo "<p class='error'>✗ عمود link غير موجود في جدول التنبيهات</p>";
    }

    // Create a test notification
    $insertStmt = $pdo->prepare("
        INSERT INTO notifications
        (user_id, type, title, message, is_read, priority)
        VALUES (?, ?, ?, ?, ?, ?)
    ");

    $insertStmt->execute([
        1, // Assuming user ID 1 exists
        'system',
        'تنبيه اختبار',
        'هذا تنبيه اختبار للتأكد من عمل الجدول بشكل صحيح.',
        0,
        'normal'
    ]);

    echo "<p class='success'>✓ تم إنشاء تنبيه اختبار بنجاح</p>";

    // Show the test notification
    $testStmt = $pdo->query("SELECT * FROM notifications LIMIT 1");
    $testNotification = $testStmt->fetch(PDO::FETCH_ASSOC);

    echo "<h3>تنبيه الاختبار:</h3>";
    echo "<pre>";
    print_r($testNotification);
    echo "</pre>";

} catch (PDOException $e) {
    echo "<p class='error'>خطأ في إعادة إنشاء جدول التنبيهات: " . $e->getMessage() . "</p>";
}

// Create a new simple notification generator
echo "<h2>إنشاء ملف توليد التنبيهات الجديد</h2>";

$newGeneratorContent = '<?php
/**
 * مولد التنبيهات الجديد
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Set character encoding
header("Content-Type: text/html; charset=utf-8");

// Include configuration and database connection
require_once "config/config.php";
require_once "config/database.php";

// Start output
echo "<h1>توليد التنبيهات</h1>";

// Get all HR and admin users
$usersStmt = $pdo->query("
    SELECT u.id, u.full_name, u.email
    FROM users u
    WHERE u.role IN (\'admin\', \'hr\')
");
$users = $usersStmt->fetchAll(PDO::FETCH_ASSOC);

if (count($users) === 0) {
    echo "<p>لم يتم العثور على مستخدمين لإرسال التنبيهات إليهم.</p>";
    exit;
}

echo "<p>تم العثور على " . count($users) . " مستخدم لإرسال التنبيهات إليهم.</p>";

// Initialize counters
$promotionNotifications = 0;
$allowanceNotifications = 0;
$retirementNotifications = 0;

// Process each user
foreach ($users as $user) {
    // Generate promotion notifications
    try {
        $promotionDays = 30; // Default value

        $promotionStmt = $pdo->prepare("
            SELECT e.id, e.full_name, e.current_grade, e.last_promotion_date, d.name as department_name,
                   DATE_ADD(e.last_promotion_date, INTERVAL
                       CASE
                           WHEN e.current_grade BETWEEN 1 AND 4 THEN 5
                           ELSE 4
                       END YEAR) as next_promotion_date
            FROM employees e
            JOIN departments d ON e.department_id = d.id
            WHERE DATEDIFF(DATE_ADD(e.last_promotion_date, INTERVAL
                CASE
                    WHEN e.current_grade BETWEEN 1 AND 4 THEN 5
                    ELSE 4
                END YEAR), CURDATE()) BETWEEN 0 AND ?
            ORDER BY next_promotion_date ASC
        ");
        $promotionStmt->execute([$promotionDays]);
        $promotions = $promotionStmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($promotions as $promotion) {
            $title = "موظف مستحق للترقية: " . $promotion["full_name"];
            $message = "الموظف " . $promotion["full_name"] . " من قسم " . $promotion["department_name"] . " مستحق للترقية من الدرجة " . $promotion["current_grade"] . " بتاريخ " . date("Y-m-d", strtotime($promotion["next_promotion_date"])) . ".";

            $insertStmt = $pdo->prepare("
                INSERT INTO notifications
                (user_id, employee_id, type, title, message, is_read, priority)
                VALUES (?, ?, \'promotion\', ?, ?, 0, \'normal\')
            ");

            $insertStmt->execute([
                $user["id"],
                $promotion["id"],
                $title,
                $message
            ]);

            $promotionNotifications++;
        }
    } catch (PDOException $e) {
        echo "<p>خطأ في توليد تنبيهات الترقية للمستخدم " . $user["full_name"] . ": " . $e->getMessage() . "</p>";
    }

    // Generate allowance notifications
    try {
        $allowanceDays = 30; // Default value

        $allowanceStmt = $pdo->prepare("
            SELECT e.id, e.full_name, e.current_grade, e.last_allowance_date, d.name as department_name,
                   DATE_ADD(e.last_allowance_date, INTERVAL 1 YEAR) as next_allowance_date
            FROM employees e
            JOIN departments d ON e.department_id = d.id
            WHERE DATEDIFF(DATE_ADD(e.last_allowance_date, INTERVAL 1 YEAR), CURDATE()) BETWEEN 0 AND ?
            ORDER BY next_allowance_date ASC
        ");
        $allowanceStmt->execute([$allowanceDays]);
        $allowances = $allowanceStmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($allowances as $allowance) {
            $title = "موظف مستحق للعلاوة: " . $allowance["full_name"];
            $message = "الموظف " . $allowance["full_name"] . " من قسم " . $allowance["department_name"] . " مستحق للعلاوة السنوية بتاريخ " . date("Y-m-d", strtotime($allowance["next_allowance_date"])) . ".";

            $insertStmt = $pdo->prepare("
                INSERT INTO notifications
                (user_id, employee_id, type, title, message, is_read, priority)
                VALUES (?, ?, \'allowance\', ?, ?, 0, \'normal\')
            ");

            $insertStmt->execute([
                $user["id"],
                $allowance["id"],
                $title,
                $message
            ]);

            $allowanceNotifications++;
        }
    } catch (PDOException $e) {
        echo "<p>خطأ في توليد تنبيهات العلاوة للمستخدم " . $user["full_name"] . ": " . $e->getMessage() . "</p>";
    }
}

// Output summary
echo "<p>تم إنشاء " . $promotionNotifications . " تنبيه ترقية.</p>";
echo "<p>تم إنشاء " . $allowanceNotifications . " تنبيه علاوة.</p>";
echo "<p>اكتمل توليد التنبيهات: " . date("Y-m-d H:i:s") . "</p>";

// Add link to notifications page
echo "<p><a href=\'notifications.php\' class=\'btn btn-primary\'>عرض التنبيهات</a></p>";
?>';

// Save the new generator file
file_put_contents('new_notifications_generator.php', $newGeneratorContent);
echo "<p class='success'>✓ تم إنشاء ملف توليد التنبيهات الجديد بنجاح</p>";

// Add links to navigate
echo "
    <div style='margin-top: 30px;'>
        <a href='new_notifications_generator.php' class='back-link' style='margin-right: 10px;'>توليد التنبيهات</a>
        <a href='notifications.php' class='back-link'>عرض التنبيهات</a>
    </div>
</body>
</html>";
?>
