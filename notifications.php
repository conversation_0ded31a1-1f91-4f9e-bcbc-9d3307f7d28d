<?php
/**
 * Notifications Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();

// Set page title
$pageTitle = "التنبيهات";

// Get filter parameters
$type = isset($_GET['type']) ? sanitize($_GET['type']) : '';
$read = isset($_GET['read']) ? (int)$_GET['read'] : -1;

// Build query
$query = "
    SELECT n.*, u.username, u.full_name as user_full_name, e.full_name as employee_name
    FROM notifications n
    LEFT JOIN users u ON n.user_id = u.id
    LEFT JOIN employees e ON n.employee_id = e.id
    WHERE n.user_id = :user_id
";

$params = [':user_id' => $_SESSION['user_id']];

if (!empty($type)) {
    $query .= " AND n.type = :type";
    $params[':type'] = $type;
}

if ($read !== -1) {
    $query .= " AND n.is_read = :read";
    $params[':read'] = $read;
}

$query .= " ORDER BY n.priority = 'high' DESC, n.created_at DESC";

// Get notifications
try {
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $notifications = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Notifications Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع الإشعارات', 'alert alert-danger');
    $notifications = [];
}

// Mark notification as read
if (isset($_GET['mark_read']) && is_numeric($_GET['mark_read'])) {
    $notificationId = (int)$_GET['mark_read'];
    try {
        $stmt = $pdo->prepare("
            UPDATE notifications
            SET is_read = 1
            WHERE id = :id AND user_id = :user_id
        ");
        $stmt->execute([
            ':id' => $notificationId,
            ':user_id' => $_SESSION['user_id']
        ]);

        if ($stmt->rowCount() > 0) {
            flash('success_message', 'تم تحديد التنبيه كمقروء', 'alert alert-success');
        }
        redirect('notifications.php');
    } catch (PDOException $e) {
        error_log("Mark Notification Read Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء تحديث حالة التنبيه', 'alert alert-danger');
    }
}

// Mark all as read
if (isset($_GET['mark_all_read']) && $_GET['mark_all_read'] == 1) {
    try {
        $stmt = $pdo->prepare("
            UPDATE notifications
            SET is_read = 1
            WHERE user_id = :user_id AND is_read = 0
        ");
        $stmt->execute([':user_id' => $_SESSION['user_id']]);

        flash('success_message', 'تم تحديد جميع التنبيهات كمقروءة', 'alert alert-success');
        redirect('notifications.php');
    } catch (PDOException $e) {
        error_log("Mark All Read Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء تحديث التنبيهات', 'alert alert-danger');
    }
}

// Delete notification
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $notificationId = (int)$_GET['delete'];

    try {
        $stmt = $pdo->prepare("
            DELETE FROM notifications
            WHERE id = :id AND user_id = :user_id
        ");
        $stmt->execute([
            ':id' => $notificationId,
            ':user_id' => $_SESSION['user_id']
        ]);

        flash('success_message', 'تم حذف الإشعار بنجاح', 'alert alert-success');
        redirect('notifications.php');
    } catch (PDOException $e) {
        error_log("Delete Notification Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء حذف الإشعار', 'alert alert-danger');
    }
}

// Delete all read notifications
if (isset($_GET['delete_all_read']) && $_GET['delete_all_read'] == 1) {
    try {
        $stmt = $pdo->prepare("
            DELETE FROM notifications
            WHERE user_id = :user_id AND is_read = 1
        ");
        $stmt->execute([':user_id' => $_SESSION['user_id']]);

        flash('success_message', 'تم حذف جميع التنبيهات المقروءة بنجاح', 'alert alert-success');
        redirect('notifications.php');
    } catch (PDOException $e) {
        error_log("Delete All Read Notifications Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء حذف التنبيهات المقروءة', 'alert alert-danger');
    }
}

// Get notification settings
try {
    $settingsStmt = $pdo->prepare("
        SELECT * FROM notification_settings
        WHERE user_id = ?
    ");
    $settingsStmt->execute([$_SESSION['user_id']]);
    $settings = $settingsStmt->fetch(PDO::FETCH_ASSOC);

    // If no settings found, create default settings
    if (!$settings) {
        $insertStmt = $pdo->prepare("
            INSERT INTO notification_settings
            (user_id, promotion_notify_days, allowance_notify_days, retirement_notify_days, email_notifications, browser_notifications)
            VALUES (?, 30, 30, 180, 1, 1)
        ");
        $insertStmt->execute([$_SESSION['user_id']]);

        $settingsStmt->execute([$_SESSION['user_id']]);
        $settings = $settingsStmt->fetch(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    error_log("Get Notification Settings Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء جلب إعدادات التنبيهات', 'alert alert-danger');
    $settings = [
        'promotion_notify_days' => 30,
        'allowance_notify_days' => 30,
        'retirement_notify_days' => 180,
        'email_notifications' => 1,
        'browser_notifications' => 1
    ];
}

// Update notification settings
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_settings'])) {
    try {
        $promotionDays = filter_input(INPUT_POST, 'promotion_notify_days', FILTER_VALIDATE_INT);
        $allowanceDays = filter_input(INPUT_POST, 'allowance_notify_days', FILTER_VALIDATE_INT);
        $retirementDays = filter_input(INPUT_POST, 'retirement_notify_days', FILTER_VALIDATE_INT);
        $emailNotifications = isset($_POST['email_notifications']) ? 1 : 0;
        $browserNotifications = isset($_POST['browser_notifications']) ? 1 : 0;

        $updateStmt = $pdo->prepare("
            UPDATE notification_settings
            SET promotion_notify_days = ?,
                allowance_notify_days = ?,
                retirement_notify_days = ?,
                email_notifications = ?,
                browser_notifications = ?
            WHERE user_id = ?
        ");
        $updateStmt->execute([
            $promotionDays,
            $allowanceDays,
            $retirementDays,
            $emailNotifications,
            $browserNotifications,
            $_SESSION['user_id']
        ]);

        flash('success_message', 'تم تحديث إعدادات التنبيهات بنجاح', 'alert alert-success');

        // Update settings variable
        $settings['promotion_notify_days'] = $promotionDays;
        $settings['allowance_notify_days'] = $allowanceDays;
        $settings['retirement_notify_days'] = $retirementDays;
        $settings['email_notifications'] = $emailNotifications;
        $settings['browser_notifications'] = $browserNotifications;
    } catch (PDOException $e) {
        error_log("Update Notification Settings Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء تحديث إعدادات التنبيهات', 'alert alert-danger');
    }
}
?>

<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="display-5 mb-0">
                <i class="fas fa-bell me-2"></i> التنبيهات
                <?php
                    // Get unread count
                    $unreadStmt = $pdo->prepare("SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0");
                    $unreadStmt->execute([$_SESSION['user_id']]);
                    $unreadCount = $unreadStmt->fetch(PDO::FETCH_ASSOC)['count'];

                    if ($unreadCount > 0):
                ?>
                    <span class="badge bg-danger"><?php echo $unreadCount; ?></span>
                <?php endif; ?>
            </h1>
        </div>
        <div class="col-md-6 text-md-end">
            <div class="btn-group" role="group">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة إلى الرئيسية
                </a>
                <?php if ($unreadCount > 0): ?>
                    <a href="?mark_all_read=1" class="btn btn-success">
                        <i class="fas fa-check-double me-1"></i> تحديد الكل كمقروء
                    </a>
                <?php endif; ?>
                <a href="?delete_all_read=1" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف جميع التنبيهات المقروءة؟')">
                    <i class="fas fa-trash me-1"></i> حذف المقروءة
                </a>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#settingsModal">
                    <i class="fas fa-cog me-1"></i> الإعدادات
                </button>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i> تصفية التنبيهات
            </h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="type" class="form-label">نوع التنبيه</label>
                    <select class="form-select" id="type" name="type">
                        <option value="">جميع الأنواع</option>
                        <option value="allowance" <?php echo ($type === 'allowance') ? 'selected' : ''; ?>>العلاوات</option>
                        <option value="promotion" <?php echo ($type === 'promotion') ? 'selected' : ''; ?>>الترقيات</option>
                        <option value="retirement" <?php echo ($type === 'retirement') ? 'selected' : ''; ?>>التقاعد</option>
                        <option value="appreciation" <?php echo ($type === 'appreciation') ? 'selected' : ''; ?>>كتب الشكر</option>
                        <option value="employee" <?php echo ($type === 'employee') ? 'selected' : ''; ?>>الموظفين</option>
                        <option value="system" <?php echo ($type === 'system') ? 'selected' : ''; ?>>النظام</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="read" class="form-label">حالة القراءة</label>
                    <select class="form-select" id="read" name="read">
                        <option value="-1" <?php echo ($read === -1) ? 'selected' : ''; ?>>الكل</option>
                        <option value="0" <?php echo ($read === 0) ? 'selected' : ''; ?>>غير مقروءة</option>
                        <option value="1" <?php echo ($read === 1) ? 'selected' : ''; ?>>مقروءة</option>
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i> تصفية
                    </button>
                    <a href="notifications.php" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i> إعادة ضبط
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-bell me-2"></i> قائمة التنبيهات
            </h5>
        </div>
        <div class="card-body">
            <?php if (count($notifications) > 0): ?>
                <div class="list-group">
                    <?php foreach ($notifications as $notification): ?>
                        <?php
                            $isRead = $notification['is_read'] == 1;
                            $hasDueDate = false; // تم تعطيل حقل due_date
                            $isPastDue = false;
                            $priority = isset($notification['priority']) ? $notification['priority'] : 'normal';
                            $isHighPriority = $priority === 'high';
                        ?>
                        <div class="list-group-item list-group-item-action <?php echo $isRead ? '' : 'list-group-item-light'; ?> <?php echo $isPastDue ? 'border-danger' : ''; ?>">
                            <div class="d-flex w-100 justify-content-between align-items-center">
                                <h5 class="mb-1">
                                    <i class="<?php echo getNotificationIcon($notification['type']); ?> me-2 text-<?php echo getNotificationIconClass($notification['type']); ?>"></i>
                                    <?php echo $notification['title']; ?>
                                    <?php if (!$isRead): ?>
                                        <span class="badge bg-danger">جديد</span>
                                    <?php endif; ?>
                                    <?php if ($isHighPriority): ?>
                                        <span class="badge bg-danger">عالي الأهمية</span>
                                    <?php endif; ?>
                                </h5>
                                <small class="text-muted">
                                    <?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?>
                                </small>
                            </div>
                            <p class="mb-1"><?php echo $notification['message']; ?></p>
                            <?php if (!empty($notification['employee_name'])): ?>
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i> <?php echo $notification['employee_name']; ?>
                                </small>
                            <?php endif; ?>
                            <?php /* تم تعطيل عرض تاريخ الاستحقاق */ ?>
                            <div class="mt-2">
                                <?php if (!empty($notification['link'])): ?>
                                    <a href="<?php echo $notification['link']; ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-external-link-alt me-1"></i> عرض
                                    </a>
                                <?php endif; ?>
                                <?php if (!$isRead): ?>
                                    <a href="?mark_read=<?php echo $notification['id']; ?>" class="btn btn-sm btn-success">
                                        <i class="fas fa-check me-1"></i> تحديد كمقروء
                                    </a>
                                <?php endif; ?>
                                <a href="?delete=<?php echo $notification['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا التنبيه؟')">
                                    <i class="fas fa-trash me-1"></i> حذف
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> لا توجد تنبيهات متاحة.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Settings Modal -->
<div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="settingsModalLabel">
                    <i class="fas fa-cog me-2"></i> إعدادات التنبيهات
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="promotion_notify_days" class="form-label">أيام التنبيه قبل الترقية</label>
                            <input type="number" class="form-control" id="promotion_notify_days" name="promotion_notify_days" min="1" max="365" value="<?php echo $settings['promotion_notify_days']; ?>" required>
                            <div class="form-text">عدد الأيام قبل استحقاق الترقية للتنبيه</div>
                        </div>
                        <div class="col-md-4">
                            <label for="allowance_notify_days" class="form-label">أيام التنبيه قبل العلاوة</label>
                            <input type="number" class="form-control" id="allowance_notify_days" name="allowance_notify_days" min="1" max="365" value="<?php echo $settings['allowance_notify_days']; ?>" required>
                            <div class="form-text">عدد الأيام قبل استحقاق العلاوة للتنبيه</div>
                        </div>
                        <div class="col-md-4">
                            <label for="retirement_notify_days" class="form-label">أيام التنبيه قبل التقاعد</label>
                            <input type="number" class="form-control" id="retirement_notify_days" name="retirement_notify_days" min="1" max="730" value="<?php echo $settings['retirement_notify_days']; ?>" required>
                            <div class="form-text">عدد الأيام قبل سن التقاعد للتنبيه</div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" <?php echo $settings['email_notifications'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="email_notifications">تفعيل التنبيهات عبر البريد الإلكتروني</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="browser_notifications" name="browser_notifications" <?php echo $settings['browser_notifications'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="browser_notifications">تفعيل التنبيهات عبر المتصفح</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="update_settings" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> حفظ الإعدادات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
/**
 * Get notification icon based on type
 *
 * @param string $type Notification type
 * @return string Icon class
 */
function getNotificationIcon($type) {
    switch ($type) {
        case 'allowance':
            return 'fas fa-money-bill-wave';
        case 'promotion':
            return 'fas fa-level-up-alt';
        case 'retirement':
            return 'fas fa-user-clock';
        case 'appreciation':
            return 'fas fa-certificate';
        case 'employee':
            return 'fas fa-user';
        case 'system':
            return 'fas fa-cog';
        default:
            return 'fas fa-bell';
    }
}

/**
 * Get notification icon class based on type
 *
 * @param string $type Notification type
 * @return string Icon class
 */
function getNotificationIconClass($type) {
    switch ($type) {
        case 'allowance':
            return 'text-success';
        case 'promotion':
            return 'text-primary';
        case 'retirement':
            return 'text-warning';
        case 'appreciation':
            return 'text-info';
        case 'employee':
            return 'text-secondary';
        case 'system':
            return 'text-dark';
        default:
            return 'text-dark';
    }
}

// Include footer
require_once 'includes/footer.php';
?>
