<?php
/**
 * Check Notifications Structure
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

// Start HTML output
echo "<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>فحص هيكل جدول التنبيهات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #0066cc; margin-top: 30px; }
        p { margin: 10px 0; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .back-link { display: inline-block; margin-top: 20px; padding: 10px 15px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>فحص هيكل جدول التنبيهات</h1>";

// Check notifications table structure
echo "<h2>هيكل جدول التنبيهات</h2>";
try {
    // Get table structure
    $columnsStmt = $pdo->query("SHOW CREATE TABLE notifications");
    $tableStructure = $columnsStmt->fetch(PDO::FETCH_ASSOC);

    echo "<pre>";
    print_r($tableStructure);
    echo "</pre>";

    // Get columns
    $columnsStmt = $pdo->query("DESCRIBE notifications");
    $columns = $columnsStmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h3>أعمدة جدول التنبيهات:</h3>";
    echo "<pre>";
    print_r($columns);
    echo "</pre>";

    // Test insert
    echo "<h2>اختبار إدراج تنبيه</h2>";

    try {
        // Begin transaction
        $pdo->beginTransaction();

        // Insert test notification
        $insertStmt = $pdo->prepare("
            INSERT INTO notifications
            (user_id, employee_id, type, title, message, is_read, created_at, due_date, priority)
            VALUES (1, 1, 'system', 'تنبيه اختبار', 'هذا تنبيه اختبار', 0, NOW(), CURDATE(), 'normal')
        ");
        $insertStmt->execute();

        $insertId = $pdo->lastInsertId();
        echo "<p class='success'>✓ تم إدراج تنبيه اختبار بنجاح (ID: $insertId)</p>";

        // Get the inserted notification
        $selectStmt = $pdo->prepare("SELECT * FROM notifications WHERE id = ?");
        $selectStmt->execute([$insertId]);
        $notification = $selectStmt->fetch(PDO::FETCH_ASSOC);

        echo "<h3>التنبيه المدرج:</h3>";
        echo "<pre>";
        print_r($notification);
        echo "</pre>";

        // Rollback transaction
        $pdo->rollBack();
        echo "<p class='info'>ℹ️ تم التراجع عن الإدراج (لأغراض الاختبار فقط)</p>";
    } catch (PDOException $e) {
        // Rollback transaction
        $pdo->rollBack();
        echo "<p class='error'>خطأ في اختبار إدراج التنبيه: " . $e->getMessage() . "</p>";
    }

} catch (PDOException $e) {
    echo "<p class='error'>خطأ في جلب هيكل جدول التنبيهات: " . $e->getMessage() . "</p>";
}

// Add links to navigate
echo "
    <div style='margin-top: 30px;'>
        <a href='generate_notifications.php' class='back-link'>العودة إلى صفحة توليد التنبيهات</a>
    </div>
</body>
</html>";
?>
