<?php
/**
 * Reports Data Module
 * وحدة بيانات التقارير
 */

// Initialize report data
$reportData = [];
$reportTitle = '';

// Define constants if not already defined
if (!defined('ALLOWANCES_PER_GRADE')) {
    define('ALLOWANCES_PER_GRADE', 11);
}
if (!defined('PROMOTION_YEARS_LOWER_GRADES')) {
    define('PROMOTION_YEARS_LOWER_GRADES', 4);
}
if (!defined('PROMOTION_YEARS_UPPER_GRADES')) {
    define('PROMOTION_YEARS_UPPER_GRADES', 5);
}

// Generate report based on type
if (!empty($reportType)) {
    try {
        switch ($reportType) {
            case 'allowances_eligible':
                $reportTitle = 'تقرير الموظفين المستحقين للعلاوة';

                $query = "
                    SELECT e.*, d.name as department_name
                    FROM employees e
                    JOIN departments d ON e.department_id = d.id
                    WHERE e.allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
                    AND e.next_allowance_date <= CURDATE()
                ";

                if ($department > 0) {
                    $query .= " AND e.department_id = :department";
                }

                $query .= " ORDER BY e.next_allowance_date ASC, e.full_name ASC";

                $stmt = $pdo->prepare($query);

                if ($department > 0) {
                    $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                }

                $stmt->execute();
                $reportData = $stmt->fetchAll();
                break;

            case 'upcoming_allowances':
                $reportTitle = 'تقرير الموظفين المستحقين للعلاوة خلال ' . $period . ' يوم';

                $query = "
                    SELECT e.*, d.name as department_name
                    FROM employees e
                    JOIN departments d ON e.department_id = d.id
                    WHERE e.allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . "
                    AND e.next_allowance_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL :period DAY)
                ";

                if ($department > 0) {
                    $query .= " AND e.department_id = :department";
                }

                if ($grade > 0) {
                    $query .= " AND e.current_grade = :grade";
                }

                $query .= " ORDER BY e.next_allowance_date ASC, e.full_name ASC";

                $stmt = $pdo->prepare($query);
                $stmt->bindParam(':period', $period, PDO::PARAM_INT);

                if ($department > 0) {
                    $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                }

                if ($grade > 0) {
                    $stmt->bindParam(':grade', $grade, PDO::PARAM_INT);
                }

                $stmt->execute();
                $reportData = $stmt->fetchAll();
                break;

            case 'upcoming_retirement':
                $reportTitle = 'تقرير الموظفين المقبلين على التقاعد خلال ' . $period . ' يوم';

                // Get retirement age from settings
                $retirementAge = 60; // Default
                $settingStmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'retirement_age'");
                $settingStmt->execute();
                $setting = $settingStmt->fetch();
                if ($setting) {
                    $retirementAge = (int)$setting['setting_value'];
                }

                $query = "
                    SELECT e.*, d.name as department_name,
                           TIMESTAMPDIFF(YEAR, e.birth_date, CURDATE()) as current_age,
                           DATE_ADD(e.birth_date, INTERVAL $retirementAge YEAR) as retirement_date
                    FROM employees e
                    JOIN departments d ON e.department_id = d.id
                    WHERE DATE_ADD(e.birth_date, INTERVAL $retirementAge YEAR)
                          BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL :period DAY)
                ";

                if ($department > 0) {
                    $query .= " AND e.department_id = :department";
                }

                $query .= " ORDER BY retirement_date ASC, e.full_name ASC";

                $stmt = $pdo->prepare($query);
                $stmt->bindParam(':period', $period, PDO::PARAM_INT);

                if ($department > 0) {
                    $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                }

                $stmt->execute();
                $reportData = $stmt->fetchAll();
                break;

            case 'promotions_eligible':
                $reportTitle = 'تقرير الموظفين المستحقين للترفيع';

                $query = "
                    SELECT e.*, d.name as department_name
                    FROM employees e
                    JOIN departments d ON e.department_id = d.id
                    WHERE e.next_promotion_date <= CURDATE()
                ";

                if ($department > 0) {
                    $query .= " AND e.department_id = :department";
                }

                $query .= " ORDER BY e.next_promotion_date ASC, e.full_name ASC";

                $stmt = $pdo->prepare($query);

                if ($department > 0) {
                    $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                }

                $stmt->execute();
                $reportData = $stmt->fetchAll();
                break;

            case 'allowances_monthly':
                $reportTitle = 'تقرير العلاوات الشهرية لشهر ' . $month . '/' . $year;

                $startDate = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01';
                $endDate = date('Y-m-t', strtotime($startDate));

                try {
                    $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'allowance_history'");
                    $tableExists = $tableCheckStmt->rowCount() > 0;

                    if ($tableExists) {
                        $query = "
                            SELECT ah.*, e.employee_number, e.full_name, d.name as department_name,
                                   u.full_name as created_by_name
                            FROM allowance_history ah
                            JOIN employees e ON ah.employee_id = e.id
                            JOIN departments d ON e.department_id = d.id
                            JOIN users u ON ah.created_by = u.id
                            WHERE ah.created_at BETWEEN :start_date AND :end_date
                        ";

                        if ($department > 0) {
                            $query .= " AND e.department_id = :department";
                        }

                        $query .= " ORDER BY ah.created_at ASC, e.full_name ASC";

                        $stmt = $pdo->prepare($query);
                        $stmt->bindParam(':start_date', $startDate);
                        $stmt->bindParam(':end_date', $endDate);

                        if ($department > 0) {
                            $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                        }

                        $stmt->execute();
                        $reportData = $stmt->fetchAll();
                    }
                } catch (Exception $ex) {
                    error_log("Allowances Monthly Report Error: " . $ex->getMessage());
                    $reportData = [];
                }
                break;

            case 'promotions_monthly':
                $reportTitle = 'تقرير الترفيعات الشهرية لشهر ' . $month . '/' . $year;

                $startDate = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01';
                $endDate = date('Y-m-t', strtotime($startDate));

                try {
                    $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'promotion_history'");
                    $tableExists = $tableCheckStmt->rowCount() > 0;

                    if ($tableExists) {
                        $query = "
                            SELECT ph.*, e.employee_number, e.full_name, d.name as department_name,
                                   u.full_name as created_by_name
                            FROM promotion_history ph
                            JOIN employees e ON ph.employee_id = e.id
                            JOIN departments d ON e.department_id = d.id
                            JOIN users u ON ph.created_by = u.id
                            WHERE ph.created_at BETWEEN :start_date AND :end_date
                        ";

                        if ($department > 0) {
                            $query .= " AND e.department_id = :department";
                        }

                        $query .= " ORDER BY ph.created_at ASC, e.full_name ASC";

                        $stmt = $pdo->prepare($query);
                        $stmt->bindParam(':start_date', $startDate);
                        $stmt->bindParam(':end_date', $endDate);

                        if ($department > 0) {
                            $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                        }

                        $stmt->execute();
                        $reportData = $stmt->fetchAll();
                    }
                } catch (Exception $ex) {
                    error_log("Promotions Monthly Report Error: " . $ex->getMessage());
                    $reportData = [];
                }
                break;

            case 'appreciation_yearly':
                $reportTitle = 'تقرير كتب الشكر السنوية لعام ' . $year;

                $startDate = $year . '-01-01';
                $endDate = $year . '-12-31';

                try {
                    $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'appreciation_letters'");
                    $tableExists = $tableCheckStmt->rowCount() > 0;

                    if ($tableExists) {
                        $query = "
                            SELECT a.*, e.employee_number, e.full_name, d.name as department_name,
                                   u.full_name as created_by_name
                            FROM appreciation_letters a
                            JOIN employees e ON a.employee_id = e.id
                            JOIN departments d ON e.department_id = d.id
                            JOIN users u ON a.created_by = u.id
                            WHERE a.issue_date BETWEEN :start_date AND :end_date
                        ";

                        if ($department > 0) {
                            $query .= " AND e.department_id = :department";
                        }

                        $query .= " ORDER BY a.issue_date ASC, e.full_name ASC";

                        $stmt = $pdo->prepare($query);
                        $stmt->bindParam(':start_date', $startDate);
                        $stmt->bindParam(':end_date', $endDate);

                        if ($department > 0) {
                            $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                        }

                        $stmt->execute();
                        $reportData = $stmt->fetchAll();
                    }
                } catch (Exception $ex) {
                    error_log("Appreciation Letters Yearly Report Error: " . $ex->getMessage());
                    $reportData = [];
                }
                break;

            case 'employees_summary':
                $reportTitle = 'تقرير ملخص بيانات الموظفين';

                $query = "
                    SELECT e.*, d.name as department_name
                    FROM employees e
                    JOIN departments d ON e.department_id = d.id
                ";

                if ($department > 0) {
                    $query .= " WHERE e.department_id = :department";
                }

                $query .= " ORDER BY e.current_grade ASC, e.full_name ASC";

                $stmt = $pdo->prepare($query);

                if ($department > 0) {
                    $stmt->bindParam(':department', $department, PDO::PARAM_INT);
                }

                $stmt->execute();
                $reportData = $stmt->fetchAll();
                break;

            case 'department_summary':
                $reportTitle = 'تقرير ملخص الأقسام';

                try {
                    $query = "
                        SELECT
                            d.id,
                            d.name,
                            COUNT(e.id) as total_employees,
                            IFNULL(SUM(CASE WHEN e.allowances_in_current_grade < " . ALLOWANCES_PER_GRADE . " AND e.next_allowance_date <= CURDATE() THEN 1 ELSE 0 END), 0) as eligible_allowances,
                            IFNULL(SUM(CASE WHEN e.next_promotion_date <= CURDATE() THEN 1 ELSE 0 END), 0) as eligible_promotions,
                            IFNULL(AVG(e.years_of_service), 0) as avg_years_service,
                            0 as total_appreciation
                        FROM departments d
                        LEFT JOIN employees e ON d.id = e.department_id
                        GROUP BY d.id, d.name
                        ORDER BY d.name ASC
                    ";

                    $stmt = $pdo->prepare($query);
                    $stmt->execute();
                    $reportData = $stmt->fetchAll();

                } catch (Exception $ex) {
                    error_log("Department Summary Report Error: " . $ex->getMessage());
                    $reportData = [];
                }
                break;
        }

    } catch (PDOException $e) {
        error_log("Reports Error: " . $e->getMessage());

        // Check if it's a table not found error
        if (strpos($e->getMessage(), "doesn't exist") !== false) {
            flash('error_message', 'الجداول المطلوبة غير موجودة في قاعدة البيانات. يرجى تشغيل سكريپت إنشاء قاعدة البيانات أولاً.', 'alert alert-warning');
        } else {
            flash('error_message', 'حدث خطأ أثناء إنشاء التقرير: ' . $e->getMessage(), 'alert alert-danger');
        }

        $reportData = [];
    } catch (Exception $e) {
        error_log("Reports General Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ عام أثناء تحميل البيانات: ' . $e->getMessage(), 'alert alert-danger');
        $reportData = [];
    }
}
?>
