<?php
/**
 * Users Management Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require admin role
requireRole(['admin']);

// Process form submission for adding/editing user
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if it's an edit or add operation
    $isEdit = isset($_POST['edit_id']) && !empty($_POST['edit_id']);
    
    // Sanitize inputs
    $userId = $isEdit ? (int)$_POST['edit_id'] : 0;
    $username = sanitize($_POST['username']);
    $fullName = sanitize($_POST['full_name']);
    $email = sanitize($_POST['email']);
    $role = sanitize($_POST['role']);
    $password = $_POST['password']; // Don't sanitize password
    
    // Validate inputs
    $errors = [];
    
    if (empty($username)) {
        $errors[] = 'اسم المستخدم مطلوب';
    }
    
    if (empty($fullName)) {
        $errors[] = 'الاسم الكامل مطلوب';
    }
    
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صالح';
    }
    
    if (!in_array($role, ['admin', 'hr', 'viewer'])) {
        $errors[] = 'الصلاحية غير صالحة';
    }
    
    if (!$isEdit && empty($password)) {
        $errors[] = 'كلمة المرور مطلوبة';
    }
    
    // Check if username already exists (for new users or when changing username)
    try {
        $stmt = $pdo->prepare("
            SELECT id FROM users 
            WHERE username = :username 
            " . ($isEdit ? "AND id != :id" : "") . "
        ");
        
        $params = [':username' => $username];
        if ($isEdit) {
            $params[':id'] = $userId;
        }
        
        $stmt->execute($params);
        
        if ($stmt->rowCount() > 0) {
            $errors[] = 'اسم المستخدم موجود بالفعل';
        }
    } catch (PDOException $e) {
        error_log("Check Username Error: " . $e->getMessage());
        $errors[] = 'حدث خطأ أثناء التحقق من اسم المستخدم';
    }
    
    // If no errors, add or update user
    if (empty($errors)) {
        try {
            if ($isEdit) {
                // Update existing user
                if (!empty($password)) {
                    // Update with new password
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                    
                    $stmt = $pdo->prepare("
                        UPDATE users SET
                            username = :username,
                            password = :password,
                            full_name = :full_name,
                            email = :email,
                            role = :role
                        WHERE id = :id
                    ");
                    
                    $stmt->execute([
                        ':username' => $username,
                        ':password' => $hashedPassword,
                        ':full_name' => $fullName,
                        ':email' => $email,
                        ':role' => $role,
                        ':id' => $userId
                    ]);
                } else {
                    // Update without changing password
                    $stmt = $pdo->prepare("
                        UPDATE users SET
                            username = :username,
                            full_name = :full_name,
                            email = :email,
                            role = :role
                        WHERE id = :id
                    ");
                    
                    $stmt->execute([
                        ':username' => $username,
                        ':full_name' => $fullName,
                        ':email' => $email,
                        ':role' => $role,
                        ':id' => $userId
                    ]);
                }
                
                // Log activity
                logActivity(
                    $_SESSION['user_id'],
                    'تعديل مستخدم',
                    'users',
                    $userId,
                    'تم تعديل بيانات المستخدم: ' . $username
                );
                
                flash('success_message', 'تم تعديل المستخدم بنجاح', 'alert alert-success');
            } else {
                // Add new user
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, password, full_name, email, role)
                    VALUES (:username, :password, :full_name, :email, :role)
                ");
                
                $stmt->execute([
                    ':username' => $username,
                    ':password' => $hashedPassword,
                    ':full_name' => $fullName,
                    ':email' => $email,
                    ':role' => $role
                ]);
                
                $newUserId = $pdo->lastInsertId();
                
                // Log activity
                logActivity(
                    $_SESSION['user_id'],
                    'إضافة مستخدم',
                    'users',
                    $newUserId,
                    'تمت إضافة مستخدم جديد: ' . $username
                );
                
                flash('success_message', 'تمت إضافة المستخدم بنجاح', 'alert alert-success');
            }
        } catch (PDOException $e) {
            error_log("User Operation Error: " . $e->getMessage());
            flash('error_message', 'حدث خطأ أثناء ' . ($isEdit ? 'تعديل' : 'إضافة') . ' المستخدم', 'alert alert-danger');
        }
    } else {
        // Display errors
        $errorMessage = '<ul class="mb-0">';
        foreach ($errors as $error) {
            $errorMessage .= '<li>' . $error . '</li>';
        }
        $errorMessage .= '</ul>';
        
        flash('error_message', $errorMessage, 'alert alert-danger');
    }
}

// Process user deletion
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $deleteId = (int)$_GET['delete'];
    
    // Prevent deleting self
    if ($deleteId === (int)$_SESSION['user_id']) {
        flash('error_message', 'لا يمكنك حذف حسابك الحالي', 'alert alert-danger');
    } else {
        try {
            // Get username for log
            $nameStmt = $pdo->prepare("SELECT username FROM users WHERE id = :id");
            $nameStmt->execute([':id' => $deleteId]);
            $username = $nameStmt->fetch()['username'];
            
            // Delete user
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = :id");
            $stmt->execute([':id' => $deleteId]);
            
            // Log activity
            logActivity(
                $_SESSION['user_id'],
                'حذف مستخدم',
                'users',
                $deleteId,
                'تم حذف المستخدم: ' . $username
            );
            
            flash('success_message', 'تم حذف المستخدم بنجاح', 'alert alert-success');
        } catch (PDOException $e) {
            error_log("Delete User Error: " . $e->getMessage());
            flash('error_message', 'حدث خطأ أثناء حذف المستخدم', 'alert alert-danger');
        }
    }
}

// Get user to edit
$editUser = null;
if (isset($_GET['edit']) && !empty($_GET['edit'])) {
    $editId = (int)$_GET['edit'];
    
    try {
        $stmt = $pdo->prepare("SELECT id, username, full_name, email, role FROM users WHERE id = :id");
        $stmt->execute([':id' => $editId]);
        $editUser = $stmt->fetch();
        
        if (!$editUser) {
            flash('error_message', 'المستخدم غير موجود', 'alert alert-danger');
        }
    } catch (PDOException $e) {
        error_log("Get User Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء استرجاع بيانات المستخدم', 'alert alert-danger');
    }
}

// Get all users
try {
    $stmt = $pdo->query("SELECT id, username, full_name, email, role, created_at FROM users ORDER BY username ASC");
    $users = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Users Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات المستخدمين', 'alert alert-danger');
    $users = [];
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-user-cog me-2"></i> إدارة المستخدمين
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="fas fa-user-plus me-1"></i> إضافة مستخدم جديد
        </button>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-body">
        <?php if (count($users) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم المستخدم</th>
                            <th>الاسم الكامل</th>
                            <th>البريد الإلكتروني</th>
                            <th>الصلاحية</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?php echo $user['username']; ?></td>
                                <td><?php echo $user['full_name']; ?></td>
                                <td><?php echo $user['email'] ?: '-'; ?></td>
                                <td>
                                    <?php if ($user['role'] === 'admin'): ?>
                                        <span class="badge bg-danger">مدير النظام</span>
                                    <?php elseif ($user['role'] === 'hr'): ?>
                                        <span class="badge bg-primary">موظف الموارد البشرية</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">مستخدم عادي</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('d/m/Y', strtotime($user['created_at'])); ?></td>
                                <td>
                                    <a href="?edit=<?php echo $user['id']; ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if ($user['id'] !== $_SESSION['user_id']): ?>
                                        <a href="?delete=<?php echo $user['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد بيانات للعرض
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addUserModalLabel">
                    <i class="fas fa-user-plus me-2"></i> إضافة مستخدم جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" class="needs-validation" novalidate>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="username" name="username" required>
                        <div class="invalid-feedback">يرجى إدخال اسم المستخدم</div>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <div class="invalid-feedback">يرجى إدخال كلمة المرور</div>
                    </div>
                    <div class="mb-3">
                        <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                        <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" name="email">
                        <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صالح</div>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">الصلاحية <span class="text-danger">*</span></label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="viewer">مستخدم عادي</option>
                            <option value="hr">موظف الموارد البشرية</option>
                            <option value="admin">مدير النظام</option>
                        </select>
                        <div class="invalid-feedback">يرجى اختيار الصلاحية</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<?php if ($editUser): ?>
    <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="editUserModalLabel">
                        <i class="fas fa-user-edit me-2"></i> تعديل المستخدم: <?php echo $editUser['username']; ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" class="needs-validation" novalidate>
                    <input type="hidden" name="edit_id" value="<?php echo $editUser['id']; ?>">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="edit_username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_username" name="username" value="<?php echo $editUser['username']; ?>" required>
                            <div class="invalid-feedback">يرجى إدخال اسم المستخدم</div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_password" class="form-label">كلمة المرور <small class="text-muted">(اتركها فارغة إذا لم ترغب في تغييرها)</small></label>
                            <input type="password" class="form-control" id="edit_password" name="password">
                        </div>
                        <div class="mb-3">
                            <label for="edit_full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_full_name" name="full_name" value="<?php echo $editUser['full_name']; ?>" required>
                            <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="edit_email" name="email" value="<?php echo $editUser['email']; ?>">
                            <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صالح</div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_role" class="form-label">الصلاحية <span class="text-danger">*</span></label>
                            <select class="form-select" id="edit_role" name="role" required>
                                <option value="viewer" <?php echo ($editUser['role'] === 'viewer') ? 'selected' : ''; ?>>مستخدم عادي</option>
                                <option value="hr" <?php echo ($editUser['role'] === 'hr') ? 'selected' : ''; ?>>موظف الموارد البشرية</option>
                                <option value="admin" <?php echo ($editUser['role'] === 'admin') ? 'selected' : ''; ?>>مدير النظام</option>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار الصلاحية</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-secondary">إلغاء</a>
                        <button type="submit" class="btn btn-warning">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script>
        // Show edit modal automatically when edit parameter is present
        document.addEventListener('DOMContentLoaded', function() {
            var editModal = new bootstrap.Modal(document.getElementById('editUserModal'));
            editModal.show();
        });
    </script>
<?php endif; ?>

<?php
// Include footer
require_once 'includes/footer.php';
?>
