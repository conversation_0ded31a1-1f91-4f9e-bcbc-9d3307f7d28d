<?php
/**
 * Session Role Fix Script
 * إصلاح مشكلة الصلاحيات في الجلسة
 */

// Include configuration
require_once 'includes/header.php';
requireLogin();

// Only admin can run this script
if (!hasRole(['admin'])) {
    die('ليس لديك صلاحية لتشغيل هذا السكريبت');
}

echo "<h1>إصلاح مشكلة الصلاحيات في الجلسة</h1>";

// Check current session
echo "<h2>معلومات الجلسة الحالية:</h2>";
echo "<pre>";
echo "user_id: " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'غير موجود') . "\n";
echo "username: " . (isset($_SESSION['username']) ? $_SESSION['username'] : 'غير موجود') . "\n";
echo "full_name: " . (isset($_SESSION['full_name']) ? $_SESSION['full_name'] : 'غير موجود') . "\n";
echo "role: " . (isset($_SESSION['role']) ? $_SESSION['role'] : 'غير موجود') . "\n";
echo "user_role: " . (isset($_SESSION['user_role']) ? $_SESSION['user_role'] : 'غير موجود') . "\n";
echo "</pre>";

// Fix session if needed
if (isset($_SESSION['user_id']) && !isset($_SESSION['user_role'])) {
    echo "<h2>إصلاح الجلسة...</h2>";
    
    try {
        // Get user data from database
        $stmt = $pdo->prepare("SELECT role FROM users WHERE id = :id");
        $stmt->execute([':id' => $_SESSION['user_id']]);
        $user = $stmt->fetch();
        
        if ($user) {
            $_SESSION['user_role'] = $user['role'];
            echo "<p style='color: green;'>تم إصلاح الجلسة بنجاح. الصلاحية: " . $user['role'] . "</p>";
        } else {
            echo "<p style='color: red;'>لم يتم العثور على المستخدم في قاعدة البيانات</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    }
}

// Check all files that might have the issue
echo "<h2>فحص الملفات للمشاكل المحتملة:</h2>";

$filesToCheck = [
    'dashboard_unified.php',
    'reports_analytics_unified.php',
    'index.php',
    'dashboard.php'
];

foreach ($filesToCheck as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // Check for $_SESSION['role'] usage
        if (strpos($content, "\$_SESSION['role']") !== false) {
            echo "<p style='color: orange;'>تحذير: الملف $file يستخدم \$_SESSION['role'] بدلاً من \$_SESSION['user_role']</p>";
        } else {
            echo "<p style='color: green;'>الملف $file سليم</p>";
        }
    } else {
        echo "<p style='color: gray;'>الملف $file غير موجود</p>";
    }
}

echo "<h2>معلومات الجلسة بعد الإصلاح:</h2>";
echo "<pre>";
echo "user_id: " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'غير موجود') . "\n";
echo "username: " . (isset($_SESSION['username']) ? $_SESSION['username'] : 'غير موجود') . "\n";
echo "full_name: " . (isset($_SESSION['full_name']) ? $_SESSION['full_name'] : 'غير موجود') . "\n";
echo "role: " . (isset($_SESSION['role']) ? $_SESSION['role'] : 'غير موجود') . "\n";
echo "user_role: " . (isset($_SESSION['user_role']) ? $_SESSION['user_role'] : 'غير موجود') . "\n";
echo "</pre>";

echo "<h2>اختبار وظائف الصلاحيات:</h2>";

// Test hasRole function
$testRoles = ['admin', 'hr', 'viewer'];
foreach ($testRoles as $role) {
    $hasRole = hasRole([$role]);
    echo "<p>hasRole(['$role']): " . ($hasRole ? 'true' : 'false') . "</p>";
}

echo "<h2>الإصلاح مكتمل!</h2>";
echo "<p><a href='dashboard_unified.php'>العودة إلى لوحة التحكم</a></p>";
?>
