<?php
/**
 * Simple Notifications Generator
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>توليد التنبيهات البسيط</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .btn { display: inline-block; margin: 10px 0; padding: 8px 15px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>توليد التنبيهات البسيط</h1>";

try {
    // Check if notifications table exists
    $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'notifications'");
    $tableExists = $tableCheckStmt->rowCount() > 0;

    if (!$tableExists) {
        // Create notifications table
        $pdo->exec("
            CREATE TABLE `notifications` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) NOT NULL,
              `employee_id` int(11) DEFAULT NULL,
              `type` varchar(50) NOT NULL,
              `title` varchar(255) NOT NULL,
              `message` text NOT NULL,
              `link` varchar(255) DEFAULT NULL,
              `is_read` tinyint(1) NOT NULL DEFAULT 0,
              `read_at` datetime DEFAULT NULL,
              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `priority` varchar(20) NOT NULL DEFAULT 'normal',
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `employee_id` (`employee_id`),
              KEY `type` (`type`),
              KEY `is_read` (`is_read`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ");
        echo "<p class='success'>تم إنشاء جدول التنبيهات.</p>";
    }

    // Get all HR and admin users
    $usersStmt = $pdo->query("
        SELECT u.id, u.full_name, u.email
        FROM users u
        WHERE u.role IN ('admin', 'hr')
    ");
    $users = $usersStmt->fetchAll(PDO::FETCH_ASSOC);

    if (count($users) === 0) {
        echo "<p class='error'>لم يتم العثور على مستخدمين لإرسال التنبيهات إليهم.</p>";
        exit;
    }

    echo "<p class='success'>تم العثور على " . count($users) . " مستخدم لإرسال التنبيهات إليهم.</p>";

    // Initialize counters
    $promotionNotifications = 0;
    $allowanceNotifications = 0;
    $retirementNotifications = 0;

    // Process each user
    foreach ($users as $user) {
        echo "<h2>توليد التنبيهات للمستخدم: {$user['full_name']}</h2>";

        // Generate promotion notifications
        try {
            $promotionDays = 30; // Default value

            $promotionStmt = $pdo->prepare("
                SELECT e.id, e.full_name, e.current_grade, e.last_promotion_date, d.name as department_name,
                       DATE_ADD(e.last_promotion_date, INTERVAL
                           CASE
                               WHEN e.current_grade BETWEEN 1 AND 4 THEN 5
                               ELSE 4
                           END YEAR) as next_promotion_date
                FROM employees e
                JOIN departments d ON e.department_id = d.id
                WHERE DATEDIFF(DATE_ADD(e.last_promotion_date, INTERVAL
                    CASE
                        WHEN e.current_grade BETWEEN 1 AND 4 THEN 5
                        ELSE 4
                    END YEAR), CURDATE()) BETWEEN 0 AND ?
                ORDER BY next_promotion_date ASC
            ");
            $promotionStmt->execute([$promotionDays]);
            $promotions = $promotionStmt->fetchAll(PDO::FETCH_ASSOC);

            echo "<p>تم العثور على " . count($promotions) . " موظف مستحق للترقية.</p>";

            foreach ($promotions as $promotion) {
                $title = "موظف مستحق للترقية: {$promotion['full_name']}";
                $message = "الموظف {$promotion['full_name']} من قسم {$promotion['department_name']} مستحق للترقية من الدرجة {$promotion['current_grade']} بتاريخ " . date('Y-m-d', strtotime($promotion['next_promotion_date'])) . ".";

                $insertStmt = $pdo->prepare("
                    INSERT INTO notifications
                    (user_id, employee_id, type, title, message, is_read, created_at, priority)
                    VALUES (?, ?, 'promotion', ?, ?, 0, NOW(), 'normal')
                ");

                $insertStmt->execute([
                    $user['id'],
                    $promotion['id'],
                    $title,
                    $message
                ]);

                $promotionNotifications++;
            }

            echo "<p class='success'>تم إنشاء $promotionNotifications تنبيه ترقية للمستخدم {$user['full_name']}.</p>";
        } catch (PDOException $e) {
            echo "<p class='error'>خطأ في توليد تنبيهات الترقية للمستخدم {$user['full_name']}: " . $e->getMessage() . "</p>";
        }

        // Generate allowance notifications
        try {
            $allowanceDays = 30; // Default value

            $allowanceStmt = $pdo->prepare("
                SELECT e.id, e.full_name, e.current_grade, e.last_allowance_date, d.name as department_name,
                       DATE_ADD(e.last_allowance_date, INTERVAL 1 YEAR) as next_allowance_date
                FROM employees e
                JOIN departments d ON e.department_id = d.id
                WHERE DATEDIFF(DATE_ADD(e.last_allowance_date, INTERVAL 1 YEAR), CURDATE()) BETWEEN 0 AND ?
                ORDER BY next_allowance_date ASC
            ");
            $allowanceStmt->execute([$allowanceDays]);
            $allowances = $allowanceStmt->fetchAll(PDO::FETCH_ASSOC);

            echo "<p>تم العثور على " . count($allowances) . " موظف مستحق للعلاوة.</p>";

            foreach ($allowances as $allowance) {
                $title = "موظف مستحق للعلاوة: {$allowance['full_name']}";
                $message = "الموظف {$allowance['full_name']} من قسم {$allowance['department_name']} مستحق للعلاوة السنوية بتاريخ " . date('Y-m-d', strtotime($allowance['next_allowance_date'])) . ".";

                $insertStmt = $pdo->prepare("
                    INSERT INTO notifications
                    (user_id, employee_id, type, title, message, is_read, created_at, priority)
                    VALUES (?, ?, 'allowance', ?, ?, 0, NOW(), 'normal')
                ");

                $insertStmt->execute([
                    $user['id'],
                    $allowance['id'],
                    $title,
                    $message
                ]);

                $allowanceNotifications++;
            }

            echo "<p class='success'>تم إنشاء $allowanceNotifications تنبيه علاوة للمستخدم {$user['full_name']}.</p>";
        } catch (PDOException $e) {
            echo "<p class='error'>خطأ في توليد تنبيهات العلاوة للمستخدم {$user['full_name']}: " . $e->getMessage() . "</p>";
        }
    }

    // Output summary
    echo "<h2>ملخص التنبيهات</h2>";
    echo "<p>تم إنشاء $promotionNotifications تنبيه ترقية.</p>";
    echo "<p>تم إنشاء $allowanceNotifications تنبيه علاوة.</p>";
    echo "<p>اكتمل توليد التنبيهات: " . date('Y-m-d H:i:s') . "</p>";

    // Add link to notifications page
    echo "<p><a href='notifications.php' class='btn'>عرض التنبيهات</a></p>";

} catch (PDOException $e) {
    echo "<p class='error'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
