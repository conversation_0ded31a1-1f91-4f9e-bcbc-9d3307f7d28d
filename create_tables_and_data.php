<?php
/**
 * Create Tables and Sample Data
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

// Function to check if table exists
function tableExists($pdo, $tableName) {
    $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
    return $stmt->rowCount() > 0;
}

// Function to get employee count
function getEmployeeCount($pdo) {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM employees");
    return $stmt->fetch(PDO::FETCH_ASSOC)['count'];
}

// Function to get user count
function getUserCount($pdo) {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    return $stmt->fetch(PDO::FETCH_ASSOC)['count'];
}

// Function to create table
function createTable($pdo, $tableName, $sql) {
    try {
        $pdo->exec($sql);
        return true;
    } catch (PDOException $e) {
        echo "<p style='color: red;'>خطأ في إنشاء جدول $tableName: " . $e->getMessage() . "</p>";
        return false;
    }
}

// Function to insert sample data
function insertSampleData($pdo, $tableName, $sql) {
    try {
        $pdo->exec($sql);
        return true;
    } catch (PDOException $e) {
        echo "<p style='color: red;'>خطأ في إدخال بيانات تجريبية لجدول $tableName: " . $e->getMessage() . "</p>";
        return false;
    }
}

// Start HTML output
echo "<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إنشاء الجداول والبيانات التجريبية</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #0066cc; margin-top: 30px; }
        p { margin: 10px 0; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .back-link { display: inline-block; margin-top: 20px; padding: 10px 15px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>إنشاء الجداول والبيانات التجريبية</h1>";

// Check if employees and users exist
$employeeCount = getEmployeeCount($pdo);
$userCount = getUserCount($pdo);

if ($employeeCount == 0) {
    echo "<p class='warning'>⚠️ لا يوجد موظفين في النظام. يجب إضافة موظفين أولاً قبل إنشاء البيانات التجريبية.</p>";
} else {
    echo "<p class='info'>ℹ️ يوجد $employeeCount موظف في النظام.</p>";
}

if ($userCount == 0) {
    echo "<p class='warning'>⚠️ لا يوجد مستخدمين في النظام. يجب إضافة مستخدمين أولاً قبل إنشاء البيانات التجريبية.</p>";
} else {
    echo "<p class='info'>ℹ️ يوجد $userCount مستخدم في النظام.</p>";
}

// Create allowances table if it doesn't exist
if (!tableExists($pdo, 'allowances')) {
    echo "<h2>إنشاء جدول العلاوات (allowances)</h2>";
    
    $sql = "
        CREATE TABLE `allowances` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `employee_id` int(11) NOT NULL,
          `allowance_date` date NOT NULL,
          `allowance_number` varchar(50) DEFAULT NULL,
          `grade_at_time` int(11) NOT NULL,
          `notes` text DEFAULT NULL,
          `created_by` int(11) NOT NULL,
          `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `employee_id` (`employee_id`),
          KEY `created_by` (`created_by`),
          KEY `allowance_date` (`allowance_date`),
          CONSTRAINT `allowances_employee_fk` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
          CONSTRAINT `allowances_user_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if (createTable($pdo, 'allowances', $sql)) {
        echo "<p class='success'>✓ تم إنشاء جدول العلاوات بنجاح</p>";
    }
} else {
    echo "<h2>جدول العلاوات (allowances)</h2>";
    echo "<p class='info'>ℹ️ جدول العلاوات موجود بالفعل</p>";
    
    // Check if table has data
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM allowances");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($count == 0) {
        echo "<p class='warning'>⚠️ لا توجد بيانات في جدول العلاوات</p>";
    } else {
        echo "<p class='success'>✓ يوجد $count سجل في جدول العلاوات</p>";
    }
}

// Create promotions table if it doesn't exist
if (!tableExists($pdo, 'promotions')) {
    echo "<h2>إنشاء جدول الترفيعات (promotions)</h2>";
    
    $sql = "
        CREATE TABLE `promotions` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `employee_id` int(11) NOT NULL,
          `promotion_date` date NOT NULL,
          `from_grade` int(11) NOT NULL,
          `to_grade` int(11) NOT NULL,
          `years_in_previous_grade` int(11) NOT NULL,
          `notes` text DEFAULT NULL,
          `created_by` int(11) NOT NULL,
          `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `employee_id` (`employee_id`),
          KEY `created_by` (`created_by`),
          KEY `promotion_date` (`promotion_date`),
          CONSTRAINT `promotions_employee_fk` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
          CONSTRAINT `promotions_user_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if (createTable($pdo, 'promotions', $sql)) {
        echo "<p class='success'>✓ تم إنشاء جدول الترفيعات بنجاح</p>";
    }
} else {
    echo "<h2>جدول الترفيعات (promotions)</h2>";
    echo "<p class='info'>ℹ️ جدول الترفيعات موجود بالفعل</p>";
    
    // Check if table has data
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM promotions");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($count == 0) {
        echo "<p class='warning'>⚠️ لا توجد بيانات في جدول الترفيعات</p>";
    } else {
        echo "<p class='success'>✓ يوجد $count سجل في جدول الترفيعات</p>";
    }
}

// Create appreciation_letters table if it doesn't exist
if (!tableExists($pdo, 'appreciation_letters')) {
    echo "<h2>إنشاء جدول كتب الشكر (appreciation_letters)</h2>";
    
    $sql = "
        CREATE TABLE `appreciation_letters` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `employee_id` int(11) NOT NULL,
          `letter_date` date NOT NULL,
          `letter_number` varchar(50) DEFAULT NULL,
          `issuer` enum('normal','prime_minister') NOT NULL DEFAULT 'normal',
          `months_reduction` int(11) NOT NULL DEFAULT 0,
          `notes` text DEFAULT NULL,
          `created_by` int(11) NOT NULL,
          `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `employee_id` (`employee_id`),
          KEY `created_by` (`created_by`),
          KEY `letter_date` (`letter_date`),
          CONSTRAINT `appreciation_letters_employee_fk` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
          CONSTRAINT `appreciation_letters_user_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if (createTable($pdo, 'appreciation_letters', $sql)) {
        echo "<p class='success'>✓ تم إنشاء جدول كتب الشكر بنجاح</p>";
    }
} else {
    echo "<h2>جدول كتب الشكر (appreciation_letters)</h2>";
    echo "<p class='info'>ℹ️ جدول كتب الشكر موجود بالفعل</p>";
    
    // Check if table has data
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM appreciation_letters");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($count == 0) {
        echo "<p class='warning'>⚠️ لا توجد بيانات في جدول كتب الشكر</p>";
    } else {
        echo "<p class='success'>✓ يوجد $count سجل في جدول كتب الشكر</p>";
    }
}

// Insert sample data if tables are empty and there are employees and users
if ($employeeCount > 0 && $userCount > 0) {
    echo "<h2>إنشاء بيانات تجريبية</h2>";
    
    // Get a sample employee and user
    $employeeStmt = $pdo->query("SELECT id, current_grade FROM employees LIMIT 1");
    $employee = $employeeStmt->fetch(PDO::FETCH_ASSOC);
    
    $userStmt = $pdo->query("SELECT id FROM users LIMIT 1");
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);
    
    $employeeId = $employee['id'];
    $currentGrade = $employee['current_grade'];
    $userId = $user['id'];
    $currentYear = date('Y');
    $lastYear = $currentYear - 1;
    
    // Insert sample allowances if table is empty
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM allowances");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($count == 0) {
        $sql = "
            INSERT INTO `allowances` (`employee_id`, `allowance_date`, `allowance_number`, `grade_at_time`, `notes`, `created_by`) VALUES
            ($employeeId, '$lastYear-03-15', 'A-$lastYear-001', $currentGrade, 'علاوة سنوية', $userId),
            ($employeeId, '$currentYear-03-15', 'A-$currentYear-001', $currentGrade, 'علاوة سنوية', $userId),
            ($employeeId, '$currentYear-" . date('m-d') . "', 'A-$currentYear-002', $currentGrade, 'علاوة إضافية', $userId)
        ";
        
        if (insertSampleData($pdo, 'allowances', $sql)) {
            echo "<p class='success'>✓ تم إنشاء بيانات تجريبية لجدول العلاوات بنجاح</p>";
        }
    }
    
    // Insert sample promotions if table is empty
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM promotions");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($count == 0) {
        $prevGrade = max(1, $currentGrade - 1);
        $sql = "
            INSERT INTO `promotions` (`employee_id`, `promotion_date`, `from_grade`, `to_grade`, `years_in_previous_grade`, `notes`, `created_by`) VALUES
            ($employeeId, '$lastYear-06-01', " . ($prevGrade - 1) . ", $prevGrade, 4, 'ترفيع دوري', $userId),
            ($employeeId, '$currentYear-06-01', $prevGrade, $currentGrade, 4, 'ترفيع دوري', $userId)
        ";
        
        if (insertSampleData($pdo, 'promotions', $sql)) {
            echo "<p class='success'>✓ تم إنشاء بيانات تجريبية لجدول الترفيعات بنجاح</p>";
        }
    }
    
    // Insert sample appreciation letters if table is empty
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM appreciation_letters");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($count == 0) {
        $sql = "
            INSERT INTO `appreciation_letters` (`employee_id`, `letter_date`, `letter_number`, `issuer`, `months_reduction`, `notes`, `created_by`) VALUES
            ($employeeId, '$lastYear-05-10', 'L-$lastYear-001', 'normal', 0, 'كتاب شكر لجهود متميزة', $userId),
            ($employeeId, '$currentYear-02-15', 'L-$currentYear-001', 'normal', 0, 'كتاب شكر لجهود متميزة', $userId),
            ($employeeId, '$currentYear-" . date('m-d') . "', 'L-$currentYear-002', 'prime_minister', 3, 'كتاب شكر من رئيس الوزراء', $userId)
        ";
        
        if (insertSampleData($pdo, 'appreciation_letters', $sql)) {
            echo "<p class='success'>✓ تم إنشاء بيانات تجريبية لجدول كتب الشكر بنجاح</p>";
        }
    }
}

// Add links to navigate
echo "
    <div style='margin-top: 30px;'>
        <a href='reports.php' class='back-link'>العودة إلى صفحة التقارير</a>
        <a href='check_tables.php' class='back-link' style='margin-right: 10px;'>فحص هيكل الجداول</a>
    </div>
</body>
</html>";
?>
