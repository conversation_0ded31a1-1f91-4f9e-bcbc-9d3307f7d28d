<?php
/**
 * Create User Page
 * صفحة إنشاء مستخدم جديد
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once 'config/database.php';

// Include functions if available
if (file_exists('includes/functions.php')) {
    require_once 'includes/functions.php';
}

// CSS styles
$styles = '
<style>
    body {
        font-family: Arial, sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 20px;
        direction: rtl;
    }
    .container {
        max-width: 800px;
        margin: 0 auto;
        background-color: #fff;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    h1, h2 {
        color: #007bff;
    }
    .alert-success {
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
    table, th, td {
        border: 1px solid #ddd;
    }
    th, td {
        padding: 10px;
        text-align: right;
    }
    th {
        background-color: #f2f2f2;
    }
    .btn {
        display: inline-block;
        padding: 8px 16px;
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.5;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        cursor: pointer;
        border: 1px solid transparent;
        border-radius: 4px;
        text-decoration: none;
    }
    .btn-primary {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }
    .btn-success {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    .form-group {
        margin-bottom: 15px;
    }
    label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    input[type="text"],
    input[type="password"],
    input[type="email"],
    select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
    }
    .login-info {
        background-color: #f8f9fa;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-top: 20px;
    }
</style>
';

// Function to sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Start HTML output
echo '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء مستخدم جديد - نظام إدارة العلاوات والترفيع</title>
    ' . $styles . '
</head>
<body>
    <div class="container">
        <h1>إنشاء مستخدم جديد</h1>';

// Check if users table exists
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo '<div class="alert-danger">
            <p>جدول المستخدمين غير موجود!</p>
        </div>';
        
        // Create users table
        echo '<h2>إنشاء جدول المستخدمين</h2>';
        echo '<form method="post">
            <button type="submit" name="create_table" class="btn btn-primary">إنشاء جدول المستخدمين</button>
        </form>';
        
        if (isset($_POST['create_table'])) {
            try {
                $createTableSQL = "
                    CREATE TABLE IF NOT EXISTS users (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        username VARCHAR(50) NOT NULL UNIQUE,
                        password VARCHAR(255) NOT NULL,
                        full_name VARCHAR(100) NOT NULL,
                        email VARCHAR(100) NOT NULL,
                        role ENUM('admin', 'hr', 'manager', 'viewer') NOT NULL DEFAULT 'viewer',
                        status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
                        last_login DATETIME,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                ";
                
                $pdo->exec($createTableSQL);
                
                echo '<div class="alert-success">
                    <p>تم إنشاء جدول المستخدمين بنجاح!</p>
                </div>';
                
                // Refresh the page
                echo '<meta http-equiv="refresh" content="2">';
            } catch (PDOException $e) {
                echo '<div class="alert-danger">
                    <p>خطأ في إنشاء جدول المستخدمين: ' . $e->getMessage() . '</p>
                </div>';
            }
        }
    } else {
        // Process form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_user'])) {
            $username = sanitizeInput($_POST['username']);
            $password = sanitizeInput($_POST['password']);
            $fullName = sanitizeInput($_POST['full_name']);
            $email = sanitizeInput($_POST['email']);
            $role = sanitizeInput($_POST['role']);
            
            // Validate input
            $errors = [];
            
            if (empty($username)) {
                $errors[] = "يرجى إدخال اسم المستخدم";
            }
            
            if (empty($password)) {
                $errors[] = "يرجى إدخال كلمة المرور";
            } elseif (strlen($password) < 6) {
                $errors[] = "يجب أن تكون كلمة المرور على الأقل 6 أحرف";
            }
            
            if (empty($fullName)) {
                $errors[] = "يرجى إدخال الاسم الكامل";
            }
            
            if (empty($email)) {
                $errors[] = "يرجى إدخال البريد الإلكتروني";
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errors[] = "البريد الإلكتروني غير صالح";
            }
            
            if (empty($errors)) {
                try {
                    // Check if username already exists
                    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
                    $stmt->execute([$username]);
                    $userExists = $stmt->rowCount() > 0;
                    
                    if ($userExists) {
                        echo '<div class="alert-danger">
                            <p>اسم المستخدم موجود بالفعل!</p>
                        </div>';
                    } else {
                        // Hash the password
                        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                        
                        // Insert new user
                        $stmt = $pdo->prepare("
                            INSERT INTO users (username, password, full_name, email, role, status)
                            VALUES (?, ?, ?, ?, ?, 'active')
                        ");
                        
                        $stmt->execute([$username, $hashedPassword, $fullName, $email, $role]);
                        
                        echo '<div class="alert-success">
                            <p>تم إنشاء المستخدم بنجاح!</p>
                        </div>';
                        
                        // Show login details
                        echo '<div class="login-info">
                            <h2>معلومات تسجيل الدخول:</h2>
                            <p><strong>اسم المستخدم:</strong> ' . $username . '</p>
                            <p><strong>كلمة المرور:</strong> ' . $password . '</p>
                            <p><a href="login.php" class="btn btn-primary">انتقل إلى صفحة تسجيل الدخول</a></p>
                        </div>';
                    }
                } catch (PDOException $e) {
                    echo '<div class="alert-danger">
                        <p>خطأ في قاعدة البيانات: ' . $e->getMessage() . '</p>
                    </div>';
                }
            } else {
                echo '<div class="alert-danger">
                    <ul>';
                foreach ($errors as $error) {
                    echo '<li>' . $error . '</li>';
                }
                echo '</ul>
                </div>';
            }
        }
        
        // Show list of users
        try {
            $stmt = $pdo->query("SELECT id, username, full_name, email, role, status FROM users");
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($users) > 0) {
                echo '<h2>المستخدمون الحاليون:</h2>
                <table>
                    <tr>
                        <th>ID</th>
                        <th>اسم المستخدم</th>
                        <th>الاسم الكامل</th>
                        <th>البريد الإلكتروني</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                    </tr>';
                
                foreach ($users as $user) {
                    echo '<tr>
                        <td>' . $user['id'] . '</td>
                        <td>' . $user['username'] . '</td>
                        <td>' . $user['full_name'] . '</td>
                        <td>' . $user['email'] . '</td>
                        <td>' . $user['role'] . '</td>
                        <td>' . $user['status'] . '</td>
                    </tr>';
                }
                
                echo '</table>';
            } else {
                echo '<div class="alert-danger">
                    <p>لا يوجد مستخدمون في النظام!</p>
                </div>';
            }
        } catch (PDOException $e) {
            echo '<div class="alert-danger">
                <p>خطأ في قاعدة البيانات: ' . $e->getMessage() . '</p>
            </div>';
        }
        
        // Create user form
        echo '<h2>إنشاء مستخدم جديد</h2>
        <form method="post">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label for="full_name">الاسم الكامل:</label>
                <input type="text" id="full_name" name="full_name" required>
            </div>
            
            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="role">الدور:</label>
                <select id="role" name="role" required>
                    <option value="admin">مدير</option>
                    <option value="hr">موارد بشرية</option>
                    <option value="manager">مدير قسم</option>
                    <option value="viewer">مشاهد</option>
                </select>
            </div>
            
            <button type="submit" name="create_user" class="btn btn-primary">إنشاء مستخدم</button>
        </form>';
    }
} catch (PDOException $e) {
    echo '<div class="alert-danger">
        <p>خطأ في قاعدة البيانات: ' . $e->getMessage() . '</p>
    </div>';
}

echo '<p><a href="index.php" class="btn btn-success">العودة إلى الصفحة الرئيسية</a></p>
<p><a href="login.php" class="btn btn-success">الذهاب إلى صفحة تسجيل الدخول</a></p>
<p><a href="reset_password.php" class="btn btn-success">إعادة تعيين كلمة المرور</a></p>

</div>
</body>
</html>';
?>
