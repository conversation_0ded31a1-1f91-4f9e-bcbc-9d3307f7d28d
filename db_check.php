<?php
/**
 * Database Check File
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database credentials
define('DB_HOST', 'localhost');
define('DB_NAME', 'employee_promotions_db');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// PDO connection options
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

echo "<h1>فحص قاعدة البيانات</h1>";

try {
    // Create PDO instance
    echo "<p>محاولة الاتصال بقاعدة البيانات...</p>";
    
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        $options
    );
    
    echo "<p style='color:green;'>تم الاتصال بقاعدة البيانات بنجاح!</p>";
    
    // Check if users table exists
    echo "<p>التحقق من وجود جدول المستخدمين...</p>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color:green;'>جدول المستخدمين موجود!</p>";
        
        // Check if admin user exists
        echo "<p>التحقق من وجود مستخدم المدير...</p>";
        
        $stmt = $pdo->query("SELECT id, username, password, full_name, role FROM users WHERE username = 'admin'");
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "<p style='color:green;'>مستخدم المدير موجود!</p>";
            echo "<pre>";
            print_r($admin);
            echo "</pre>";
            
            // Test password verification
            echo "<p>اختبار التحقق من كلمة المرور...</p>";
            
            $testPassword = 'admin123';
            $passwordVerified = password_verify($testPassword, $admin['password']);
            
            echo "<p>كلمة المرور للاختبار: " . $testPassword . "</p>";
            echo "<p>نتيجة التحقق: " . ($passwordVerified ? 'صحيحة' : 'غير صحيحة') . "</p>";
            
            if (!$passwordVerified) {
                echo "<p style='color:red;'>فشل التحقق من كلمة المرور!</p>";
                
                // Create a new admin user with a known password hash
                echo "<p>إنشاء مستخدم مدير جديد...</p>";
                
                $newUsername = 'admin2';
                $newPassword = 'admin123';
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, password, full_name, email, role)
                    VALUES (:username, :password, :full_name, :email, :role)
                ");
                
                $stmt->execute([
                    ':username' => $newUsername,
                    ':password' => $hashedPassword,
                    ':full_name' => 'مدير النظام الجديد',
                    ':email' => '<EMAIL>',
                    ':role' => 'admin'
                ]);
                
                echo "<p style='color:green;'>تم إنشاء مستخدم مدير جديد!</p>";
                echo "<p>اسم المستخدم: " . $newUsername . "</p>";
                echo "<p>كلمة المرور: " . $newPassword . "</p>";
                
                // Verify the new password
                $stmt = $pdo->prepare("SELECT password FROM users WHERE username = :username");
                $stmt->execute([':username' => $newUsername]);
                $newAdmin = $stmt->fetch();
                
                $newPasswordVerified = password_verify($newPassword, $newAdmin['password']);
                echo "<p>نتيجة التحقق من كلمة المرور الجديدة: " . ($newPasswordVerified ? 'صحيحة' : 'غير صحيحة') . "</p>";
            }
        } else {
            echo "<p style='color:red;'>مستخدم المدير غير موجود!</p>";
            
            // Create admin user
            echo "<p>إنشاء مستخدم مدير...</p>";
            
            $username = 'admin';
            $password = 'admin123';
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            $stmt = $pdo->prepare("
                INSERT INTO users (username, password, full_name, email, role)
                VALUES (:username, :password, :full_name, :email, :role)
            ");
            
            $stmt->execute([
                ':username' => $username,
                ':password' => $hashedPassword,
                ':full_name' => 'مدير النظام',
                ':email' => '<EMAIL>',
                ':role' => 'admin'
            ]);
            
            echo "<p style='color:green;'>تم إنشاء مستخدم المدير!</p>";
        }
        
        // Show all users
        echo "<h2>جميع المستخدمين:</h2>";
        
        $stmt = $pdo->query("SELECT id, username, full_name, role FROM users");
        $users = $stmt->fetchAll();
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>الصلاحية</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['username'] . "</td>";
            echo "<td>" . $user['full_name'] . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p style='color:red;'>جدول المستخدمين غير موجود!</p>";
        echo "<p>يرجى استيراد ملف schema.sql أولاً.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color:red;'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}
?>
