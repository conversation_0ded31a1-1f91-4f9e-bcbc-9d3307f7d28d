<?php
/**
 * Departments Management Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require admin role
requireRole(['admin']);

// Process form submission for adding/editing department
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if it's an edit or add operation
    $isEdit = isset($_POST['edit_id']) && !empty($_POST['edit_id']);
    
    // Sanitize inputs
    $departmentId = $isEdit ? (int)$_POST['edit_id'] : 0;
    $name = sanitize($_POST['name']);
    $description = sanitize($_POST['description']);
    
    // Validate inputs
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'اسم القسم مطلوب';
    }
    
    // Check if department name already exists (for new departments or when changing name)
    try {
        $stmt = $pdo->prepare("
            SELECT id FROM departments 
            WHERE name = :name 
            " . ($isEdit ? "AND id != :id" : "") . "
        ");
        
        $params = [':name' => $name];
        if ($isEdit) {
            $params[':id'] = $departmentId;
        }
        
        $stmt->execute($params);
        
        if ($stmt->rowCount() > 0) {
            $errors[] = 'اسم القسم موجود بالفعل';
        }
    } catch (PDOException $e) {
        error_log("Check Department Name Error: " . $e->getMessage());
        $errors[] = 'حدث خطأ أثناء التحقق من اسم القسم';
    }
    
    // If no errors, add or update department
    if (empty($errors)) {
        try {
            if ($isEdit) {
                // Update existing department
                $stmt = $pdo->prepare("
                    UPDATE departments SET
                        name = :name,
                        description = :description
                    WHERE id = :id
                ");
                
                $stmt->execute([
                    ':name' => $name,
                    ':description' => $description,
                    ':id' => $departmentId
                ]);
                
                // Log activity
                logActivity(
                    $_SESSION['user_id'],
                    'تعديل قسم',
                    'departments',
                    $departmentId,
                    'تم تعديل بيانات القسم: ' . $name
                );
                
                flash('success_message', 'تم تعديل القسم بنجاح', 'alert alert-success');
            } else {
                // Add new department
                $stmt = $pdo->prepare("
                    INSERT INTO departments (name, description)
                    VALUES (:name, :description)
                ");
                
                $stmt->execute([
                    ':name' => $name,
                    ':description' => $description
                ]);
                
                $newDepartmentId = $pdo->lastInsertId();
                
                // Log activity
                logActivity(
                    $_SESSION['user_id'],
                    'إضافة قسم',
                    'departments',
                    $newDepartmentId,
                    'تمت إضافة قسم جديد: ' . $name
                );
                
                flash('success_message', 'تمت إضافة القسم بنجاح', 'alert alert-success');
            }
        } catch (PDOException $e) {
            error_log("Department Operation Error: " . $e->getMessage());
            flash('error_message', 'حدث خطأ أثناء ' . ($isEdit ? 'تعديل' : 'إضافة') . ' القسم', 'alert alert-danger');
        }
    } else {
        // Display errors
        $errorMessage = '<ul class="mb-0">';
        foreach ($errors as $error) {
            $errorMessage .= '<li>' . $error . '</li>';
        }
        $errorMessage .= '</ul>';
        
        flash('error_message', $errorMessage, 'alert alert-danger');
    }
}

// Process department deletion
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $deleteId = (int)$_GET['delete'];
    
    try {
        // Check if department has employees
        $checkStmt = $pdo->prepare("SELECT COUNT(*) as count FROM employees WHERE department_id = :id");
        $checkStmt->execute([':id' => $deleteId]);
        $employeeCount = $checkStmt->fetch()['count'];
        
        if ($employeeCount > 0) {
            flash('error_message', 'لا يمكن حذف القسم لأنه يحتوي على موظفين', 'alert alert-danger');
        } else {
            // Get department name for log
            $nameStmt = $pdo->prepare("SELECT name FROM departments WHERE id = :id");
            $nameStmt->execute([':id' => $deleteId]);
            $departmentName = $nameStmt->fetch()['name'];
            
            // Delete department
            $stmt = $pdo->prepare("DELETE FROM departments WHERE id = :id");
            $stmt->execute([':id' => $deleteId]);
            
            // Log activity
            logActivity(
                $_SESSION['user_id'],
                'حذف قسم',
                'departments',
                $deleteId,
                'تم حذف القسم: ' . $departmentName
            );
            
            flash('success_message', 'تم حذف القسم بنجاح', 'alert alert-success');
        }
    } catch (PDOException $e) {
        error_log("Delete Department Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء حذف القسم', 'alert alert-danger');
    }
}

// Get department to edit
$editDepartment = null;
if (isset($_GET['edit']) && !empty($_GET['edit'])) {
    $editId = (int)$_GET['edit'];
    
    try {
        $stmt = $pdo->prepare("SELECT id, name, description FROM departments WHERE id = :id");
        $stmt->execute([':id' => $editId]);
        $editDepartment = $stmt->fetch();
        
        if (!$editDepartment) {
            flash('error_message', 'القسم غير موجود', 'alert alert-danger');
        }
    } catch (PDOException $e) {
        error_log("Get Department Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء استرجاع بيانات القسم', 'alert alert-danger');
    }
}

// Get all departments
try {
    $stmt = $pdo->query("
        SELECT d.*, 
            (SELECT COUNT(*) FROM employees WHERE department_id = d.id) as employee_count
        FROM departments d
        ORDER BY d.name ASC
    ");
    $departments = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Departments Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الأقسام', 'alert alert-danger');
    $departments = [];
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-building me-2"></i> إدارة الأقسام
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDepartmentModal">
            <i class="fas fa-plus me-1"></i> إضافة قسم جديد
        </button>
    </div>
</div>

<!-- Departments Table -->
<div class="card">
    <div class="card-body">
        <?php if (count($departments) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم القسم</th>
                            <th>الوصف</th>
                            <th>عدد الموظفين</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($departments as $department): ?>
                            <tr>
                                <td><?php echo $department['name']; ?></td>
                                <td><?php echo $department['description'] ?: '-'; ?></td>
                                <td><?php echo $department['employee_count']; ?></td>
                                <td><?php echo date('d/m/Y', strtotime($department['created_at'])); ?></td>
                                <td>
                                    <a href="?edit=<?php echo $department['id']; ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if ($department['employee_count'] == 0): ?>
                                        <a href="?delete=<?php echo $department['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    <?php else: ?>
                                        <button class="btn btn-sm btn-danger" disabled data-bs-toggle="tooltip" title="لا يمكن حذف قسم يحتوي على موظفين">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد بيانات للعرض
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add Department Modal -->
<div class="modal fade" id="addDepartmentModal" tabindex="-1" aria-labelledby="addDepartmentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addDepartmentModalLabel">
                    <i class="fas fa-plus me-2"></i> إضافة قسم جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" class="needs-validation" novalidate>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم القسم <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <div class="invalid-feedback">يرجى إدخال اسم القسم</div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Department Modal -->
<?php if ($editDepartment): ?>
    <div class="modal fade" id="editDepartmentModal" tabindex="-1" aria-labelledby="editDepartmentModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="editDepartmentModalLabel">
                        <i class="fas fa-edit me-2"></i> تعديل القسم: <?php echo $editDepartment['name']; ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" class="needs-validation" novalidate>
                    <input type="hidden" name="edit_id" value="<?php echo $editDepartment['id']; ?>">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="edit_name" class="form-label">اسم القسم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_name" name="name" value="<?php echo $editDepartment['name']; ?>" required>
                            <div class="invalid-feedback">يرجى إدخال اسم القسم</div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"><?php echo $editDepartment['description']; ?></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-secondary">إلغاء</a>
                        <button type="submit" class="btn btn-warning">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script>
        // Show edit modal automatically when edit parameter is present
        document.addEventListener('DOMContentLoaded', function() {
            var editModal = new bootstrap.Modal(document.getElementById('editDepartmentModal'));
            editModal.show();
        });
    </script>
<?php endif; ?>

<?php
// Include footer
require_once 'includes/footer.php';
?>
