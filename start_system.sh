#!/bin/bash

echo "======================================================"
echo "    نظام إدارة العلاوات والترقية وكتب الشكر"
echo "======================================================"
echo ""
echo "جاري تشغيل خدمات النظام..."
echo ""

# تحديد المسار الحالي
CURRENT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
XAMPP_DIR="$CURRENT_DIR/xampp"

# التحقق من وجود مجلد xampp
if [ ! -d "$XAMPP_DIR" ]; then
    echo "خطأ: مجلد xampp غير موجود في المسار $CURRENT_DIR"
    echo "يرجى التأكد من وجود XAMPP في المسار الصحيح."
    echo ""
    read -p "اضغط Enter للاستمرار..."
    exit 1
fi

# تشغيل خدمات XAMPP
cd "$XAMPP_DIR"
echo "جاري تشغيل خدمات XAMPP..."
./xampp start

echo ""
echo "جاري انتظار بدء تشغيل الخدمات..."
sleep 5

echo ""
echo "التحقق من حالة الخدمات:"
echo "------------------------"

# التحقق من خدمة Apache
if netstat -an | grep -q ":80 "; then
    echo "Apache: تعمل"
else
    echo "Apache: لا تعمل - محاولة إعادة التشغيل..."
    ./xampp startapache
    sleep 3
fi

# التحقق من خدمة MySQL
if netstat -an | grep -q ":3306 "; then
    echo "MySQL: تعمل"
else
    echo "MySQL: لا تعمل - محاولة إعادة التشغيل..."
    ./xampp startmysql
    sleep 3
fi

echo ""
echo "======================================================"
echo "تم تشغيل خدمات النظام بنجاح!"
echo ""
echo "جاري فتح النظام في المتصفح..."

# فتح المتصفح حسب نظام التشغيل
if [ "$(uname)" == "Darwin" ]; then
    # macOS
    open "http://localhost/ترقيات/"
elif [ "$(expr substr $(uname -s) 1 5)" == "Linux" ]; then
    # Linux
    xdg-open "http://localhost/ترقيات/" || firefox "http://localhost/ترقيات/" || google-chrome "http://localhost/ترقيات/"
fi

echo ""
echo "ملاحظات هامة:"
echo "- إذا لم يفتح المتصفح تلقائيًا، افتح المتصفح يدويًا وانتقل إلى:"
echo "  http://localhost/ترقيات/"
echo ""
echo "- إذا كانت هذه هي المرة الأولى لتشغيل النظام، قم بزيارة:"
echo "  http://localhost/ترقيات/create_database.php"
echo "  لإنشاء قاعدة البيانات."
echo ""
echo "- لإيقاف تشغيل النظام، استخدم ملف stop_system.sh"
echo "======================================================"
echo ""
