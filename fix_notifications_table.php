<?php
/**
 * إصلاح جدول التنبيهات
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح جدول التنبيهات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        table, th, td { border: 1px solid #ddd; }
        th, td { padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        .btn { display: inline-block; margin: 10px 0; padding: 8px 15px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
        .btn-danger { background-color: #f44336; }
    </style>
</head>
<body>
    <h1>إصلاح جدول التنبيهات</h1>";

try {
    // Check if notifications table exists
    $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'notifications'");
    $tableExists = $tableCheckStmt->rowCount() > 0;

    if ($tableExists) {
        echo "<p class='success'>✓ جدول التنبيهات موجود</p>";

        // Get table structure
        $columnsStmt = $pdo->query("DESCRIBE notifications");
        $columns = $columnsStmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<h2>هيكل جدول التنبيهات الحالي:</h2>";
        echo "<table>";
        echo "<tr><th>الحقل</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";

        // Check if required columns exist
        $employeeIdExists = false;
        $dueDateExists = false;
        $linkExists = false;

        foreach ($columns as $column) {
            if ($column['Field'] === 'employee_id') {
                $employeeIdExists = true;
            }
            if ($column['Field'] === 'due_date') {
                $dueDateExists = true;
            }
            if ($column['Field'] === 'link') {
                $linkExists = true;
            }
        }

        $missingColumns = [];
        if (!$employeeIdExists) $missingColumns[] = 'employee_id';
        if (!$dueDateExists) $missingColumns[] = 'due_date';
        if (!$linkExists) $missingColumns[] = 'link';

        if (count($missingColumns) > 0) {
            echo "<p class='error'>✗ الحقول التالية غير موجودة في جدول التنبيهات: " . implode(', ', $missingColumns) . "</p>";

            // Force recreate the table
            echo "<form method='post'>";
            echo "<input type='hidden' name='recreate_table' value='1'>";
            echo "<button type='submit' class='btn btn-danger'>إعادة إنشاء جدول التنبيهات بالكامل</button>";
            echo "</form>";
        } else {
            echo "<p class='success'>✓ جميع الحقول المطلوبة موجودة في جدول التنبيهات</p>";
        }
    } else {
        echo "<p class='error'>✗ جدول التنبيهات غير موجود</p>";

        // Create the table
        echo "<form method='post'>";
        echo "<input type='hidden' name='create_table' value='1'>";
        echo "<button type='submit' class='btn'>إنشاء جدول التنبيهات</button>";
        echo "</form>";
    }

    // Handle table recreation
    if (isset($_POST['recreate_table']) || isset($_POST['create_table'])) {
        // Drop the table if it exists
        $pdo->exec("DROP TABLE IF EXISTS notifications");

        // Create the table with all required columns
        $sql = "
            CREATE TABLE `notifications` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) NOT NULL,
              `employee_id` int(11) DEFAULT NULL,
              `type` varchar(50) NOT NULL,
              `title` varchar(255) NOT NULL,
              `message` text NOT NULL,
              `link` varchar(255) DEFAULT NULL,
              `is_read` tinyint(1) NOT NULL DEFAULT 0,
              `read_at` datetime DEFAULT NULL,
              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `due_date` date DEFAULT NULL,
              `priority` varchar(20) NOT NULL DEFAULT 'normal',
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `employee_id` (`employee_id`),
              KEY `type` (`type`),
              KEY `is_read` (`is_read`),
              KEY `due_date` (`due_date`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        $pdo->exec($sql);

        echo "<p class='success'>✓ تم " . (isset($_POST['recreate_table']) ? "إعادة إنشاء" : "إنشاء") . " جدول التنبيهات بنجاح</p>";

        // Get the new table structure
        $columnsStmt = $pdo->query("DESCRIBE notifications");
        $columns = $columnsStmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<h2>هيكل جدول التنبيهات الجديد:</h2>";
        echo "<table>";
        echo "<tr><th>الحقل</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // Add links to navigate
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='generate_notifications.php' class='btn'>العودة إلى صفحة توليد التنبيهات</a>";
    echo "</div>";

} catch (PDOException $e) {
    echo "<p class='error'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
