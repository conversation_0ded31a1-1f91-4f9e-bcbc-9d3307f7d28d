/**
 * Mobile Responsive Styles
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

/* Adjust font sizes for mobile */
body {
    font-size: 14px;
}

h1, .display-5 {
    font-size: 1.8rem !important;
}

h2 {
    font-size: 1.5rem !important;
}

h3 {
    font-size: 1.3rem !important;
}

/* Improve navbar for mobile */
.navbar-brand {
    font-size: 1.2rem;
}

.navbar-toggler {
    padding: 0.25rem 0.5rem;
    font-size: 1rem;
}

/* Make cards more mobile-friendly */
.card {
    margin-bottom: 1rem;
}

.card-header {
    padding: 0.75rem;
}

.card-body {
    padding: 0.75rem;
}

/* Adjust dashboard cards */
.dashboard-card {
    padding: 10px;
    margin-bottom: 15px;
}

.dashboard-card .icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.dashboard-card .count {
    font-size: 1.5rem;
}

.dashboard-card .title {
    font-size: 1rem;
}

/* Improve table display on mobile */
.table-responsive {
    border: 0;
}

.table th, .table td {
    padding: 0.5rem;
    font-size: 0.9rem;
}

/* Adjust form elements */
.form-label {
    margin-bottom: 0.25rem;
}

.form-control, .form-select {
    padding: 0.375rem 0.5rem;
    font-size: 0.9rem;
}

.btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.9rem;
}

/* Improve modal display */
.modal-header {
    padding: 0.75rem;
}

.modal-body {
    padding: 0.75rem;
}

.modal-footer {
    padding: 0.5rem;
}

/* Adjust spacing */
.mb-4 {
    margin-bottom: 1rem !important;
}

.mt-4 {
    margin-top: 1rem !important;
}

.py-3 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
}

/* Make buttons stack on mobile */
@media (max-width: 576px) {
    .btn-group {
        display: flex;
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 0.5rem;
        border-radius: 0.25rem !important;
    }
    
    .text-md-end {
        text-align: center !important;
    }
    
    .col-md-6 {
        margin-bottom: 1rem;
    }
    
    /* Stack action buttons in tables */
    td .btn {
        margin-bottom: 0.25rem;
        display: block;
        width: 100%;
    }
}

/* Improve charts on mobile */
canvas {
    max-width: 100% !important;
    height: auto !important;
}

/* Adjust footer */
footer {
    padding: 0.75rem 0;
    font-size: 0.8rem;
}

/* Improve tabs on mobile */
.nav-tabs .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
}

/* Improve filters section */
.filters-section {
    padding: 0.75rem;
}

/* Make DataTables responsive */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    float: none;
    text-align: center;
    margin-bottom: 0.5rem;
}

/* Improve notifications display */
.notification-item {
    padding: 0.5rem;
}

/* Adjust charts container */
.chart-container {
    height: 250px !important;
}
