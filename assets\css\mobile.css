/**
 * Mobile Responsive Styles
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

/* Adjust font sizes for mobile */
body {
    font-size: 14px;
}

h1, .display-5 {
    font-size: 1.8rem !important;
}

h2 {
    font-size: 1.5rem !important;
}

h3 {
    font-size: 1.3rem !important;
}

/* Improve navbar for mobile */
.navbar-brand {
    font-size: 1.2rem;
}

.navbar-toggler {
    padding: 0.25rem 0.5rem;
    font-size: 1rem;
}

/* Make cards more mobile-friendly */
.card {
    margin-bottom: 1rem;
}

.card-header {
    padding: 0.75rem;
}

.card-body {
    padding: 0.75rem;
}

/* Adjust dashboard cards */
.dashboard-card {
    padding: 10px;
    margin-bottom: 15px;
}

.dashboard-card .icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.dashboard-card .count {
    font-size: 1.5rem;
}

.dashboard-card .title {
    font-size: 1rem;
}

/* Improve table display on mobile */
.table-responsive {
    border: 0;
}

.table th, .table td {
    padding: 0.5rem;
    font-size: 0.9rem;
}

/* Adjust form elements */
.form-label {
    margin-bottom: 0.25rem;
}

.form-control, .form-select {
    padding: 0.375rem 0.5rem;
    font-size: 0.9rem;
}

.btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.9rem;
}

/* Improve modal display */
.modal-header {
    padding: 0.75rem;
}

.modal-body {
    padding: 0.75rem;
}

.modal-footer {
    padding: 0.5rem;
}

/* Adjust spacing */
.mb-4 {
    margin-bottom: 1rem !important;
}

.mt-4 {
    margin-top: 1rem !important;
}

.py-3 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
}

/* Make buttons stack on mobile */
@media (max-width: 576px) {
    .btn-group {
        display: flex;
        flex-direction: column;
    }

    .btn-group .btn {
        margin-bottom: 0.5rem;
        border-radius: 0.25rem !important;
    }

    .text-md-end {
        text-align: center !important;
    }

    .col-md-6 {
        margin-bottom: 1rem;
    }

    /* Stack action buttons in tables */
    td .btn {
        margin-bottom: 0.25rem;
        display: block;
        width: 100%;
    }
}

/* Improve charts on mobile */
canvas {
    max-width: 100% !important;
    height: auto !important;
}

/* Adjust footer */
footer {
    padding: 0.75rem 0;
    font-size: 0.8rem;
}

/* Improve tabs on mobile */
.nav-tabs .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
}

/* Improve filters section */
.filters-section {
    padding: 0.75rem;
}

/* Make DataTables responsive */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    float: none;
    text-align: center;
    margin-bottom: 0.5rem;
}

/* Improve notifications display */
.notification-item {
    padding: 0.5rem;
}

/* Adjust charts container */
.chart-container {
    height: 250px !important;
}

/* Enhanced Mobile Navbar Dropdown Fixes */
@media (max-width: 768px) {
    /* Enhanced Navbar Mobile */
    .modern-navbar {
        padding: 0.75rem 0 !important;
    }

    .brand-icon {
        width: 35px !important;
        height: 35px !important;
        font-size: 1rem !important;
    }

    .brand-text {
        font-size: 0.9rem !important;
    }

    /* Mobile Dropdown Improvements */
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem !important;
        margin: 0.25rem 0 !important;
        border-radius: 8px !important;
        text-align: center !important;
        min-height: 44px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .dropdown-menu {
        margin-top: 0.25rem !important;
        border-radius: 12px !important;
        width: 100% !important;
        min-width: auto !important;
        left: 0 !important;
        right: 0 !important;
        position: absolute !important;
        transform: none !important;
    }

    .dropdown-item {
        padding: 0.75rem 1rem !important;
        text-align: center !important;
        font-size: 0.9rem !important;
        min-height: 44px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Mobile Notifications Dropdown */
    #notificationsContainer {
        width: 100% !important;
        max-width: 300px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        right: auto !important;
        position: absolute !important;
    }

    /* Mobile Form Improvements */
    .form-control {
        font-size: 16px !important; /* Prevent zoom on iOS */
        min-height: 44px !important;
    }

    /* Mobile Button Improvements */
    .btn-icon {
        flex-direction: row !important;
        gap: 0.5rem !important;
        min-height: 44px !important;
    }

    .btn-icon i {
        margin: 0 !important;
    }

    /* Touch-friendly improvements */
    .nav-link,
    .dropdown-item,
    .btn {
        min-height: 44px !important;
        touch-action: manipulation !important;
    }

    /* Disable hover effects on mobile */
    .navbar-nav .nav-link:hover {
        transform: none !important;
    }

    .dropdown-item:hover {
        transform: none !important;
    }

    .dashboard-card:hover {
        transform: none !important;
    }

    /* Mobile-specific navbar toggler */
    .navbar-toggler {
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
        border-radius: 8px !important;
        padding: 0.5rem !important;
        min-height: 44px !important;
        min-width: 44px !important;
    }

    .navbar-toggler:focus {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
    }

    /* Mobile dropdown menu positioning */
    .dropdown-menu-end {
        left: 0 !important;
        right: 0 !important;
        transform: none !important;
    }

    /* Improve mobile table display */
    .table-responsive {
        border: none !important;
        box-shadow: none !important;
    }

    .table {
        font-size: 0.8rem !important;
        margin-bottom: 0 !important;
    }

    .table th,
    .table td {
        padding: 0.5rem 0.25rem !important;
        border-width: 1px !important;
    }

    /* Mobile modal improvements */
    .modal-dialog {
        margin: 0.5rem !important;
        max-width: calc(100% - 1rem) !important;
    }

    .modal-content {
        border-radius: 15px !important;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem !important;
    }

    /* Mobile alert improvements */
    .alert {
        padding: 0.75rem !important;
        margin-bottom: 1rem !important;
        border-radius: 10px !important;
    }

    /* Mobile card improvements */
    .card {
        border-radius: 12px !important;
        margin-bottom: 1rem !important;
    }

    .card-header {
        padding: 1rem !important;
        border-radius: 12px 12px 0 0 !important;
    }

    .card-body {
        padding: 1rem !important;
    }

    /* Mobile dashboard specific */
    .dashboard-card {
        padding: 1rem !important;
        margin-bottom: 1rem !important;
        border-radius: 12px !important;
    }

    .dashboard-card .icon {
        font-size: 2.5rem !important;
        margin-bottom: 0.75rem !important;
    }

    .dashboard-card .count {
        font-size: 2rem !important;
        margin-bottom: 0.5rem !important;
    }

    .dashboard-card .title {
        font-size: 0.9rem !important;
    }
}
