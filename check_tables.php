<?php
/**
 * Check Tables Structure
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

// Function to check table existence and structure
function checkTable($pdo, $tableName) {
    echo "<h2>فحص جدول $tableName</h2>";
    
    try {
        // Check if table exists
        $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
        $tableExists = $stmt->rowCount() > 0;
        
        if ($tableExists) {
            echo "<p style='color: green;'>✓ الجدول موجود</p>";
            
            // Get table structure
            $stmt = $pdo->query("DESCRIBE $tableName");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>هيكل الجدول:</h3>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . $column['Field'] . "</td>";
                echo "<td>" . $column['Type'] . "</td>";
                echo "<td>" . $column['Null'] . "</td>";
                echo "<td>" . $column['Key'] . "</td>";
                echo "<td>" . $column['Default'] . "</td>";
                echo "<td>" . $column['Extra'] . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
            
            // Get sample data
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $tableName");
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            echo "<h3>عدد السجلات: $count</h3>";
            
            if ($count > 0) {
                $stmt = $pdo->query("SELECT * FROM $tableName LIMIT 5");
                $sampleData = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<h3>نموذج بيانات (أول 5 سجلات):</h3>";
                echo "<table border='1' cellpadding='5'>";
                
                // Table header
                echo "<tr>";
                foreach (array_keys($sampleData[0]) as $key) {
                    echo "<th>$key</th>";
                }
                echo "</tr>";
                
                // Table data
                foreach ($sampleData as $row) {
                    echo "<tr>";
                    foreach ($row as $value) {
                        echo "<td>" . (is_null($value) ? "NULL" : htmlspecialchars($value)) . "</td>";
                    }
                    echo "</tr>";
                }
                
                echo "</table>";
                
                // Check date ranges for date columns
                $dateColumns = [];
                foreach ($columns as $column) {
                    if (strpos($column['Type'], 'date') !== false || strpos($column['Type'], 'timestamp') !== false) {
                        $dateColumns[] = $column['Field'];
                    }
                }
                
                if (!empty($dateColumns)) {
                    echo "<h3>نطاق التواريخ:</h3>";
                    echo "<ul>";
                    
                    foreach ($dateColumns as $dateColumn) {
                        $stmt = $pdo->query("SELECT MIN($dateColumn) as min_date, MAX($dateColumn) as max_date FROM $tableName WHERE $dateColumn IS NOT NULL");
                        $dateRange = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        echo "<li>$dateColumn: من " . ($dateRange['min_date'] ?: 'N/A') . " إلى " . ($dateRange['max_date'] ?: 'N/A') . "</li>";
                    }
                    
                    echo "</ul>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ لا توجد بيانات في الجدول</p>";
                
                // Create sample data button
                echo "<form method='post'>";
                echo "<input type='hidden' name='create_sample_data' value='$tableName'>";
                echo "<button type='submit' style='padding: 5px 10px; background-color: #4CAF50; color: white; border: none; cursor: pointer;'>إنشاء بيانات تجريبية</button>";
                echo "</form>";
            }
        } else {
            echo "<p style='color: red;'>✗ الجدول غير موجود</p>";
            
            // Create table button
            echo "<form method='post'>";
            echo "<input type='hidden' name='create_table' value='$tableName'>";
            echo "<button type='submit' style='padding: 5px 10px; background-color: #4CAF50; color: white; border: none; cursor: pointer;'>إنشاء الجدول</button>";
            echo "</form>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

// Create tables if requested
if (isset($_POST['create_table'])) {
    $tableName = $_POST['create_table'];
    
    try {
        switch ($tableName) {
            case 'allowances':
                $sql = "
                    CREATE TABLE `allowances` (
                      `id` int(11) NOT NULL AUTO_INCREMENT,
                      `employee_id` int(11) NOT NULL,
                      `allowance_date` date NOT NULL,
                      `allowance_number` varchar(50) DEFAULT NULL,
                      `grade_at_time` int(11) NOT NULL,
                      `notes` text DEFAULT NULL,
                      `created_by` int(11) NOT NULL,
                      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                      PRIMARY KEY (`id`),
                      KEY `employee_id` (`employee_id`),
                      KEY `created_by` (`created_by`),
                      KEY `allowance_date` (`allowance_date`),
                      CONSTRAINT `allowances_employee_fk` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
                      CONSTRAINT `allowances_user_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                ";
                break;
                
            case 'promotions':
                $sql = "
                    CREATE TABLE `promotions` (
                      `id` int(11) NOT NULL AUTO_INCREMENT,
                      `employee_id` int(11) NOT NULL,
                      `promotion_date` date NOT NULL,
                      `from_grade` int(11) NOT NULL,
                      `to_grade` int(11) NOT NULL,
                      `years_in_previous_grade` int(11) NOT NULL,
                      `notes` text DEFAULT NULL,
                      `created_by` int(11) NOT NULL,
                      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                      PRIMARY KEY (`id`),
                      KEY `employee_id` (`employee_id`),
                      KEY `created_by` (`created_by`),
                      KEY `promotion_date` (`promotion_date`),
                      CONSTRAINT `promotions_employee_fk` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
                      CONSTRAINT `promotions_user_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                ";
                break;
                
            case 'appreciation_letters':
                $sql = "
                    CREATE TABLE `appreciation_letters` (
                      `id` int(11) NOT NULL AUTO_INCREMENT,
                      `employee_id` int(11) NOT NULL,
                      `letter_date` date NOT NULL,
                      `letter_number` varchar(50) DEFAULT NULL,
                      `issuer` enum('normal','prime_minister') NOT NULL DEFAULT 'normal',
                      `months_reduction` int(11) NOT NULL DEFAULT 0,
                      `notes` text DEFAULT NULL,
                      `created_by` int(11) NOT NULL,
                      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                      PRIMARY KEY (`id`),
                      KEY `employee_id` (`employee_id`),
                      KEY `created_by` (`created_by`),
                      KEY `letter_date` (`letter_date`),
                      CONSTRAINT `appreciation_letters_employee_fk` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
                      CONSTRAINT `appreciation_letters_user_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                ";
                break;
                
            default:
                echo "<p style='color: red;'>جدول غير معروف: $tableName</p>";
                $sql = "";
        }
        
        if (!empty($sql)) {
            $pdo->exec($sql);
            echo "<p style='color: green;'>تم إنشاء جدول $tableName بنجاح</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>خطأ في إنشاء الجدول: " . $e->getMessage() . "</p>";
    }
}

// Create sample data if requested
if (isset($_POST['create_sample_data'])) {
    $tableName = $_POST['create_sample_data'];
    
    try {
        // Get a sample employee and user
        $employeeStmt = $pdo->query("SELECT id, current_grade FROM employees LIMIT 1");
        $employee = $employeeStmt->fetch(PDO::FETCH_ASSOC);
        
        $userStmt = $pdo->query("SELECT id FROM users LIMIT 1");
        $user = $userStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$employee || !$user) {
            echo "<p style='color: red;'>لا يمكن إنشاء بيانات تجريبية: لا يوجد موظفين أو مستخدمين في النظام</p>";
            return;
        }
        
        $employeeId = $employee['id'];
        $currentGrade = $employee['current_grade'];
        $userId = $user['id'];
        $currentYear = date('Y');
        $lastYear = $currentYear - 1;
        
        switch ($tableName) {
            case 'allowances':
                // Create 3 sample allowances
                $sql = "
                    INSERT INTO `allowances` (`employee_id`, `allowance_date`, `allowance_number`, `grade_at_time`, `notes`, `created_by`) VALUES
                    ($employeeId, '$lastYear-03-15', 'A-$lastYear-001', $currentGrade, 'علاوة سنوية', $userId),
                    ($employeeId, '$currentYear-03-15', 'A-$currentYear-001', $currentGrade, 'علاوة سنوية', $userId),
                    ($employeeId, '$currentYear-" . date('m-d') . "', 'A-$currentYear-002', $currentGrade, 'علاوة إضافية', $userId)
                ";
                break;
                
            case 'promotions':
                // Create 2 sample promotions
                $prevGrade = max(1, $currentGrade - 1);
                $sql = "
                    INSERT INTO `promotions` (`employee_id`, `promotion_date`, `from_grade`, `to_grade`, `years_in_previous_grade`, `notes`, `created_by`) VALUES
                    ($employeeId, '$lastYear-06-01', " . ($prevGrade - 1) . ", $prevGrade, 4, 'ترفيع دوري', $userId),
                    ($employeeId, '$currentYear-06-01', $prevGrade, $currentGrade, 4, 'ترفيع دوري', $userId)
                ";
                break;
                
            case 'appreciation_letters':
                // Create 3 sample appreciation letters
                $sql = "
                    INSERT INTO `appreciation_letters` (`employee_id`, `letter_date`, `letter_number`, `issuer`, `months_reduction`, `notes`, `created_by`) VALUES
                    ($employeeId, '$lastYear-05-10', 'L-$lastYear-001', 'normal', 0, 'كتاب شكر لجهود متميزة', $userId),
                    ($employeeId, '$currentYear-02-15', 'L-$currentYear-001', 'normal', 0, 'كتاب شكر لجهود متميزة', $userId),
                    ($employeeId, '$currentYear-" . date('m-d') . "', 'L-$currentYear-002', 'prime_minister', 3, 'كتاب شكر من رئيس الوزراء', $userId)
                ";
                break;
                
            default:
                echo "<p style='color: red;'>جدول غير معروف: $tableName</p>";
                $sql = "";
        }
        
        if (!empty($sql)) {
            $pdo->exec($sql);
            echo "<p style='color: green;'>تم إنشاء بيانات تجريبية لجدول $tableName بنجاح</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>خطأ في إنشاء البيانات التجريبية: " . $e->getMessage() . "</p>";
    }
}

// Check tables
$tablesToCheck = ['allowances', 'promotions', 'appreciation_letters'];

echo "<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>فحص هيكل الجداول</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #0066cc; margin-top: 30px; }
        h3 { color: #666; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        hr { margin: 30px 0; border: 0; border-top: 1px solid #ddd; }
        .back-link { display: inline-block; margin-top: 20px; padding: 10px 15px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>فحص هيكل الجداول</h1>
    <p>هذه الصفحة تعرض معلومات عن هيكل الجداول المستخدمة في التقارير.</p>";

foreach ($tablesToCheck as $table) {
    checkTable($pdo, $table);
}

echo "<a href='reports.php' class='back-link'>العودة إلى صفحة التقارير</a>
</body>
</html>";
?>
