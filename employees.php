<?php
/**
 * Employees List Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();

// Get search parameters
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$department = isset($_GET['department']) ? (int)$_GET['department'] : 0;
$grade = isset($_GET['grade']) ? (int)$_GET['grade'] : 0;

// Build query
$query = "
    SELECT e.*, d.name as department_name, el.name as education_level_name
    FROM employees e
    JOIN departments d ON e.department_id = d.id
    JOIN education_levels el ON e.education_level_id = el.id
    WHERE 1=1
";

$params = [];

if (!empty($search)) {
    $query .= " AND (e.full_name LIKE :search OR e.employee_number LIKE :search)";
    $params[':search'] = "%$search%";
}

if ($department > 0) {
    $query .= " AND e.department_id = :department";
    $params[':department'] = $department;
}

if ($grade > 0) {
    $query .= " AND e.current_grade = :grade";
    $params[':grade'] = $grade;
}

$query .= " ORDER BY e.full_name ASC";

// Get departments for filter
try {
    $deptStmt = $pdo->query("SELECT id, name FROM departments ORDER BY name ASC");
    $departments = $deptStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Departments Error: " . $e->getMessage());
    $departments = [];
}

// Get employees
try {
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $employees = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Employees Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الموظفين', 'alert alert-danger');
    $employees = [];
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-users me-2"></i> إدارة الموظفين
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="add_employee.php" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> إضافة موظف جديد
        </a>
        <button class="btn btn-outline-secondary btn-print">
            <i class="fas fa-print me-1"></i> طباعة
        </button>
        <button class="btn btn-outline-success btn-export" data-table-target="#employeesTable" data-filename="employees">
            <i class="fas fa-file-excel me-1"></i> تصدير
        </button>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="get" class="row g-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" name="search" placeholder="بحث بالاسم أو الرقم الوظيفي" value="<?php echo $search; ?>">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="department">
                    <option value="0">جميع الأقسام</option>
                    <?php foreach ($departments as $dept): ?>
                        <option value="<?php echo $dept['id']; ?>" <?php echo ($department == $dept['id']) ? 'selected' : ''; ?>>
                            <?php echo $dept['name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="grade">
                    <option value="0">جميع الدرجات</option>
                    <?php for ($i = 1; $i <= 10; $i++): ?>
                        <option value="<?php echo $i; ?>" <?php echo ($grade == $i) ? 'selected' : ''; ?>>
                            الدرجة <?php echo $i; ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">تطبيق</button>
            </div>
        </form>
    </div>
</div>

<!-- Employees Table -->
<div class="card">
    <div class="card-body">
        <?php if (count($employees) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover" id="employeesTable">
                    <thead>
                        <tr>
                            <th>الرقم الوظيفي</th>
                            <th>الاسم</th>
                            <th>الدرجة</th>
                            <th>العنوان الوظيفي</th>
                            <th>القسم</th>
                            <th>التحصيل الدراسي</th>
                            <th>تاريخ التعيين</th>
                            <th>العلاوة القادمة</th>
                            <th>الترفيع القادم</th>
                            <th class="no-print">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($employees as $employee): ?>
                            <tr>
                                <td><?php echo $employee['employee_number']; ?></td>
                                <td><?php echo $employee['full_name']; ?></td>
                                <td><?php echo $employee['current_grade']; ?></td>
                                <td><?php echo $employee['job_title']; ?></td>
                                <td><?php echo $employee['department_name']; ?></td>
                                <td><?php echo $employee['education_level_name']; ?></td>
                                <td><?php echo formatArabicDate($employee['hire_date']); ?></td>
                                <td>
                                    <?php if ($employee['allowances_in_current_grade'] >= ALLOWANCES_PER_GRADE): ?>
                                        <span class="badge bg-danger">اكتملت العلاوات</span>
                                    <?php else: ?>
                                        <?php echo formatArabicDate($employee['next_allowance_date']); ?>
                                        <?php if (isEligibleForAllowance($employee)): ?>
                                            <span class="badge bg-success">مستحق</span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo formatArabicDate($employee['next_promotion_date']); ?>
                                    <?php if (isEligibleForPromotion($employee)): ?>
                                        <span class="badge bg-success">مستحق</span>
                                    <?php endif; ?>
                                </td>
                                <td class="no-print">
                                    <a href="employee_details.php?id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="edit_employee.php?id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="delete_employee.php?id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد بيانات للعرض
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
