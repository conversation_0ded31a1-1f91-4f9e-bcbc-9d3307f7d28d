<?php
/**
 * Check Database
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

// Function to check if table exists
function tableExists($pdo, $tableName) {
    $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
    return $stmt->rowCount() > 0;
}

// Start HTML output
echo "<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>فحص قاعدة البيانات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #0066cc; margin-top: 30px; }
        p { margin: 10px 0; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .back-link { display: inline-block; margin-top: 20px; padding: 10px 15px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>فحص قاعدة البيانات</h1>";

// Check database connection
echo "<h2>اتصال قاعدة البيانات</h2>";
try {
    $testStmt = $pdo->query("SELECT 1");
    echo "<p class='success'>✓ تم الاتصال بقاعدة البيانات بنجاح</p>";
    echo "<p>اسم قاعدة البيانات: " . DB_NAME . "</p>";
    echo "<p>المضيف: " . DB_HOST . "</p>";
    echo "<p>المستخدم: " . DB_USER . "</p>";
} catch (PDOException $e) {
    echo "<p class='error'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

// Get all tables in the database
echo "<h2>جداول قاعدة البيانات</h2>";
try {
    $tablesStmt = $pdo->query("SHOW TABLES");
    $tables = $tablesStmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (count($tables) > 0) {
        echo "<p>عدد الجداول: " . count($tables) . "</p>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
    } else {
        echo "<p class='warning'>⚠️ لا توجد جداول في قاعدة البيانات</p>";
    }
} catch (PDOException $e) {
    echo "<p class='error'>خطأ في جلب جداول قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// Check notifications table
echo "<h2>جدول التنبيهات (notifications)</h2>";
if (tableExists($pdo, 'notifications')) {
    echo "<p class='success'>✓ جدول التنبيهات موجود</p>";
    
    // Check table structure
    try {
        $columnsStmt = $pdo->query("DESCRIBE notifications");
        $columns = $columnsStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>هيكل الجدول:</p>";
        echo "<pre>";
        print_r($columns);
        echo "</pre>";
        
        // Check if is_read column exists
        $isReadExists = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'is_read') {
                $isReadExists = true;
                break;
            }
        }
        
        if ($isReadExists) {
            echo "<p class='success'>✓ عمود is_read موجود في جدول التنبيهات</p>";
        } else {
            echo "<p class='error'>✗ عمود is_read غير موجود في جدول التنبيهات</p>";
            
            // Add is_read column
            try {
                $pdo->exec("ALTER TABLE notifications ADD COLUMN is_read tinyint(1) NOT NULL DEFAULT 0 AFTER message");
                $pdo->exec("ALTER TABLE notifications ADD INDEX is_read (is_read)");
                echo "<p class='success'>✓ تم إضافة عمود is_read إلى جدول التنبيهات بنجاح</p>";
            } catch (PDOException $e) {
                echo "<p class='error'>خطأ في إضافة عمود is_read: " . $e->getMessage() . "</p>";
            }
        }
    } catch (PDOException $e) {
        echo "<p class='error'>خطأ في جلب هيكل جدول التنبيهات: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p class='error'>✗ جدول التنبيهات غير موجود</p>";
    
    // Create notifications table
    echo "<p>إنشاء جدول التنبيهات...</p>";
    try {
        $sql = "
            CREATE TABLE `notifications` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) NOT NULL,
              `employee_id` int(11) DEFAULT NULL,
              `type` enum('promotion','allowance','retirement','system') NOT NULL,
              `title` varchar(255) NOT NULL,
              `message` text NOT NULL,
              `is_read` tinyint(1) NOT NULL DEFAULT 0,
              `read_at` datetime DEFAULT NULL,
              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `due_date` date DEFAULT NULL,
              `priority` enum('low','normal','high') NOT NULL DEFAULT 'normal',
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `employee_id` (`employee_id`),
              KEY `type` (`type`),
              KEY `is_read` (`is_read`),
              KEY `due_date` (`due_date`),
              CONSTRAINT `notifications_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
              CONSTRAINT `notifications_employee_fk` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        $pdo->exec($sql);
        echo "<p class='success'>✓ تم إنشاء جدول التنبيهات بنجاح</p>";
    } catch (PDOException $e) {
        echo "<p class='error'>خطأ في إنشاء جدول التنبيهات: " . $e->getMessage() . "</p>";
    }
}

// Check notification_settings table
echo "<h2>جدول إعدادات التنبيهات (notification_settings)</h2>";
if (tableExists($pdo, 'notification_settings')) {
    echo "<p class='success'>✓ جدول إعدادات التنبيهات موجود</p>";
    
    // Check table structure
    try {
        $columnsStmt = $pdo->query("DESCRIBE notification_settings");
        $columns = $columnsStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>هيكل الجدول:</p>";
        echo "<pre>";
        print_r($columns);
        echo "</pre>";
    } catch (PDOException $e) {
        echo "<p class='error'>خطأ في جلب هيكل جدول إعدادات التنبيهات: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p class='error'>✗ جدول إعدادات التنبيهات غير موجود</p>";
    
    // Create notification_settings table
    echo "<p>إنشاء جدول إعدادات التنبيهات...</p>";
    try {
        $sql = "
            CREATE TABLE `notification_settings` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) NOT NULL,
              `promotion_notify_days` int(11) NOT NULL DEFAULT 30,
              `allowance_notify_days` int(11) NOT NULL DEFAULT 30,
              `retirement_notify_days` int(11) NOT NULL DEFAULT 180,
              `email_notifications` tinyint(1) NOT NULL DEFAULT 1,
              `browser_notifications` tinyint(1) NOT NULL DEFAULT 1,
              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              UNIQUE KEY `user_id` (`user_id`),
              CONSTRAINT `notification_settings_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        $pdo->exec($sql);
        echo "<p class='success'>✓ تم إنشاء جدول إعدادات التنبيهات بنجاح</p>";
        
        // Create default notification settings for all users
        try {
            $usersStmt = $pdo->query("SELECT id FROM users WHERE role IN ('admin', 'hr') AND status = 'active'");
            $users = $usersStmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($users) > 0) {
                $insertValues = [];
                foreach ($users as $user) {
                    $insertValues[] = "({$user['id']}, 30, 30, 180, 1, 1)";
                }
                
                $insertSql = "
                    INSERT INTO notification_settings 
                    (user_id, promotion_notify_days, allowance_notify_days, retirement_notify_days, email_notifications, browser_notifications) 
                    VALUES " . implode(", ", $insertValues);
                
                $pdo->exec($insertSql);
                
                echo "<p class='success'>✓ تم إنشاء إعدادات تنبيهات افتراضية لـ " . count($users) . " مستخدم</p>";
            }
        } catch (PDOException $e) {
            echo "<p class='error'>خطأ في إنشاء إعدادات التنبيهات الافتراضية: " . $e->getMessage() . "</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='error'>خطأ في إنشاء جدول إعدادات التنبيهات: " . $e->getMessage() . "</p>";
    }
}

// Check system_settings table
echo "<h2>جدول إعدادات النظام (system_settings)</h2>";
if (tableExists($pdo, 'system_settings')) {
    echo "<p class='success'>✓ جدول إعدادات النظام موجود</p>";
    
    // Check table structure
    try {
        $columnsStmt = $pdo->query("DESCRIBE system_settings");
        $columns = $columnsStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>هيكل الجدول:</p>";
        echo "<pre>";
        print_r($columns);
        echo "</pre>";
    } catch (PDOException $e) {
        echo "<p class='error'>خطأ في جلب هيكل جدول إعدادات النظام: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p class='error'>✗ جدول إعدادات النظام غير موجود</p>";
    
    // Create system_settings table
    echo "<p>إنشاء جدول إعدادات النظام...</p>";
    try {
        $sql = "
            CREATE TABLE `system_settings` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `setting_key` varchar(100) NOT NULL,
              `setting_value` text DEFAULT NULL,
              `setting_description` text DEFAULT NULL,
              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              UNIQUE KEY `setting_key` (`setting_key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        $pdo->exec($sql);
        echo "<p class='success'>✓ تم إنشاء جدول إعدادات النظام بنجاح</p>";
        
        // Insert default system settings
        try {
            $defaultSettings = [
                ['notification_check_interval', '24', 'الفترة الزمنية بالساعات بين عمليات فحص التنبيهات'],
                ['notification_max_age_days', '30', 'عدد الأيام التي يتم الاحتفاظ بالتنبيهات المقروءة قبل حذفها'],
                ['enable_notifications', '1', 'تفعيل نظام التنبيهات'],
                ['system_name', 'نظام إدارة العلاوات والترفيع وكتب الشكر', 'اسم النظام'],
                ['system_version', '1.0', 'إصدار النظام'],
                ['retirement_age', '60', 'سن التقاعد']
            ];
            
            $insertSql = "INSERT INTO system_settings (setting_key, setting_value, setting_description) VALUES (?, ?, ?)";
            $stmt = $pdo->prepare($insertSql);
            
            foreach ($defaultSettings as $setting) {
                $stmt->execute($setting);
            }
            
            echo "<p class='success'>✓ تم إنشاء إعدادات النظام الافتراضية بنجاح</p>";
        } catch (PDOException $e) {
            echo "<p class='error'>خطأ في إنشاء إعدادات النظام الافتراضية: " . $e->getMessage() . "</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='error'>خطأ في إنشاء جدول إعدادات النظام: " . $e->getMessage() . "</p>";
    }
}

// Add links to navigate
echo "
    <div style='margin-top: 30px;'>
        <a href='generate_notifications.php' class='back-link'>العودة إلى صفحة توليد التنبيهات</a>
    </div>
</body>
</html>";
?>
