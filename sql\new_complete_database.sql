-- نظام إدارة العلاوات والترفيع وكتب الشكر
-- قاعدة بيانات كاملة محدثة

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS employee_promotions_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE employee_promotions_db;

-- إنشاء جدول المستخدمين
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `role` enum('admin','hr','viewer') NOT NULL DEFAULT 'viewer',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY <PERSON>EY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول الأقسام
CREATE TABLE IF NOT EXISTS `departments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول المستويات التعليمية
CREATE TABLE IF NOT EXISTS `education_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `max_grade` int(11) NOT NULL,
  `min_grade` int(11) NOT NULL DEFAULT 10,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول الدرجات
CREATE TABLE IF NOT EXISTS `grades` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول المراحل
CREATE TABLE IF NOT EXISTS `stages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `grade_id` int(11) NOT NULL,
  `stage_number` int(11) NOT NULL,
  `nominal_salary` decimal(10,2) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `grade_stage` (`grade_id`, `stage_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول مدة الخدمة للدرجات
CREATE TABLE IF NOT EXISTS `grade_service_years` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `grade_id` int(11) NOT NULL,
  `service_years` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `grade_id` (`grade_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول الموظفين
CREATE TABLE IF NOT EXISTS `employees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_number` varchar(50) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `current_grade` int(11) NOT NULL,
  `current_stage` int(11) NOT NULL DEFAULT 1,
  `nominal_salary` decimal(10,2) DEFAULT NULL,
  `job_title` varchar(100) NOT NULL,
  `department_id` int(11) NOT NULL,
  `education_level_id` int(11) NOT NULL,
  `hire_date` date NOT NULL,
  `birth_date` date DEFAULT NULL,
  `retirement_date` date DEFAULT NULL,
  `years_of_service` int(11) NOT NULL DEFAULT 0,
  `years_in_current_grade` int(11) NOT NULL DEFAULT 0,
  `allowances_in_current_grade` int(11) NOT NULL DEFAULT 0,
  `appreciation_letters_count` int(11) NOT NULL DEFAULT 0,
  `last_allowance_date` date DEFAULT NULL,
  `last_promotion_date` date DEFAULT NULL,
  `next_allowance_date` date DEFAULT NULL,
  `next_promotion_date` date DEFAULT NULL,
  `max_grade_by_education` int(11) DEFAULT NULL,
  `law_103_applied` tinyint(1) NOT NULL DEFAULT 0,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `employee_number` (`employee_number`),
  KEY `department_id` (`department_id`),
  KEY `education_level_id` (`education_level_id`),
  CONSTRAINT `employees_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`),
  CONSTRAINT `employees_ibfk_2` FOREIGN KEY (`education_level_id`) REFERENCES `education_levels` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول العلاوات
CREATE TABLE IF NOT EXISTS `allowances` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `allowance_date` date NOT NULL,
  `grade_at_time` int(11) NOT NULL,
  `allowance_number` int(11) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `allowances_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `allowances_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول الترفيعات
CREATE TABLE IF NOT EXISTS `promotions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `promotion_date` date NOT NULL,
  `from_grade` int(11) NOT NULL,
  `to_grade` int(11) NOT NULL,
  `years_in_previous_grade` int(11) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `promotions_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `promotions_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول كتب الشكر
CREATE TABLE IF NOT EXISTS `appreciation_letters` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `letter_number` varchar(50) NOT NULL,
  `letter_date` date NOT NULL,
  `year` int(11) NOT NULL,
  `issuer` enum('regular','prime_minister') NOT NULL DEFAULT 'regular',
  `months_reduction` int(11) NOT NULL DEFAULT 0,
  `file_path` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `appreciation_letters_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `appreciation_letters_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول تاريخ الراتب
CREATE TABLE IF NOT EXISTS `salary_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `grade` int(11) NOT NULL,
  `stage` int(11) NOT NULL,
  `nominal_salary` decimal(10,2) NOT NULL,
  `effective_date` date NOT NULL,
  `reason` enum('initial','allowance','promotion','higher_degree') NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `salary_history_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `salary_history_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول تاريخ الشهادات العليا
CREATE TABLE IF NOT EXISTS `higher_degree_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `degree_date` date NOT NULL,
  `from_education_level_id` int(11) NOT NULL,
  `to_education_level_id` int(11) NOT NULL,
  `from_grade` int(11) NOT NULL,
  `to_grade` int(11) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `from_education_level_id` (`from_education_level_id`),
  KEY `to_education_level_id` (`to_education_level_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `higher_degree_history_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `higher_degree_history_ibfk_2` FOREIGN KEY (`from_education_level_id`) REFERENCES `education_levels` (`id`),
  CONSTRAINT `higher_degree_history_ibfk_3` FOREIGN KEY (`to_education_level_id`) REFERENCES `education_levels` (`id`),
  CONSTRAINT `higher_degree_history_ibfk_4` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول سجل النشاطات
CREATE TABLE IF NOT EXISTS `activity_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `action` varchar(100) NOT NULL,
  `entity_type` varchar(50) NOT NULL,
  `entity_id` int(11) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `activity_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول التنبيهات
CREATE TABLE IF NOT EXISTS `alerts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('allowance','promotion','retirement') NOT NULL,
  `employee_id` int(11) NOT NULL,
  `alert_date` date NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  CONSTRAINT `alerts_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول الإشعارات
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` enum('allowance','promotion','retirement','system') NOT NULL,
  `title` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `link` varchar(255) DEFAULT NULL,
  `read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدخال بيانات المستخدمين (فقط إذا لم تكن موجودة)
INSERT IGNORE INTO `users` (`username`, `password`, `full_name`, `email`, `role`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', '<EMAIL>', 'admin'),
('hr', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مسؤول الموارد البشرية', '<EMAIL>', 'hr'),
('viewer', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مستخدم عادي', '<EMAIL>', 'viewer');

-- إدخال بيانات الأقسام (فقط إذا لم تكن موجودة)
INSERT IGNORE INTO `departments` (`name`, `description`) VALUES
('قسم الموارد البشرية', 'إدارة شؤون الموظفين والتوظيف'),
('قسم تكنولوجيا المعلومات', 'إدارة البنية التحتية التقنية والدعم الفني'),
('قسم المالية', 'إدارة الشؤون المالية والمحاسبة'),
('قسم الإدارة', 'الإدارة العامة والشؤون الإدارية'),
('قسم الخدمات', 'تقديم الخدمات العامة والدعم اللوجستي');

-- إدخال بيانات المستويات التعليمية (فقط إذا لم تكن موجودة)
INSERT IGNORE INTO `education_levels` (`name`, `max_grade`, `min_grade`) VALUES
('دكتوراه', 1, 10),
('ماجستير', 2, 10),
('بكالوريوس', 3, 10),
('دبلوم عالي', 4, 10),
('دبلوم', 5, 10),
('إعدادية', 7, 10),
('متوسطة', 8, 10),
('ابتدائية', 9, 10);

-- إدخال بيانات الدرجات (فقط إذا لم تكن موجودة)
INSERT IGNORE INTO `grades` (`name`) VALUES
('الدرجة الأولى'),
('الدرجة الثانية'),
('الدرجة الثالثة'),
('الدرجة الرابعة'),
('الدرجة الخامسة'),
('الدرجة السادسة'),
('الدرجة السابعة'),
('الدرجة الثامنة'),
('الدرجة التاسعة'),
('الدرجة العاشرة');

-- إدخال بيانات إعدادات النظام (فقط إذا لم تكن موجودة)
INSERT IGNORE INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('allowances_per_grade', '11', 'عدد العلاوات في كل درجة'),
('promotion_years_lower_grades', '4', 'عدد سنوات الترفيع للدرجات الدنيا (10-5)'),
('promotion_years_upper_grades', '5', 'عدد سنوات الترفيع للدرجات العليا (5-1)'),
('regular_letter_months', '1', 'عدد أشهر تخفيض كتاب الشكر العادي'),
('pm_letter_months', '6', 'عدد أشهر تخفيض كتاب شكر رئيس الوزراء'),
('enable_notifications', '1', 'تفعيل نظام الإشعارات'),
('enable_reminders', '1', 'تفعيل نظام التذكيرات'),
('reminder_days', '30', 'عدد أيام التذكير المسبق'),
('retirement_age', '60', 'العمر القانوني للتقاعد'),
('early_retirement_years', '25', 'عدد سنوات الخدمة المطلوبة للتقاعد المبكر'),
('max_appreciation_letters', '3', 'الحد الأقصى لكتب الشكر سنوياً'),
('law_103_promotion_years', '2', 'عدد سنوات الترفيع وفق قانون 103');

-- إدخال بيانات المراحل والرواتب الاسمية (حسب جدول الرواتب المعتمد 2015)
-- حذف البيانات القديمة أولاً
DELETE FROM `stages`;
-- ثم إدخال البيانات الجديدة
INSERT INTO `stages` (`grade_id`, `stage_number`, `nominal_salary`) VALUES
-- الدرجة 1
(1, 1, 910),
(1, 2, 930),
(1, 3, 950),
(1, 4, 970),
(1, 5, 990),
(1, 6, 1010),
(1, 7, 1030),
(1, 8, 1050),
(1, 9, 1070),
(1, 10, 1090),
(1, 11, 1110),
-- الدرجة 2
(2, 1, 723),
(2, 2, 740),
(2, 3, 757),
(2, 4, 774),
(2, 5, 791),
(2, 6, 808),
(2, 7, 825),
(2, 8, 842),
(2, 9, 859),
(2, 10, 876),
(2, 11, 893),
-- الدرجة 3
(3, 1, 600),
(3, 2, 610),
(3, 3, 620),
(3, 4, 630),
(3, 5, 640),
(3, 6, 650),
(3, 7, 660),
(3, 8, 670),
(3, 9, 680),
(3, 10, 690),
(3, 11, 700),
-- الدرجة 4
(4, 1, 509),
(4, 2, 517),
(4, 3, 525),
(4, 4, 533),
(4, 5, 541),
(4, 6, 549),
(4, 7, 557),
(4, 8, 565),
(4, 9, 573),
(4, 10, 581),
(4, 11, 589),
-- الدرجة 5
(5, 1, 429),
(5, 2, 435),
(5, 3, 441),
(5, 4, 447),
(5, 5, 453),
(5, 6, 459),
(5, 7, 465),
(5, 8, 471),
(5, 9, 477),
(5, 10, 483),
(5, 11, 489),
-- الدرجة 6
(6, 1, 362),
(6, 2, 368),
(6, 3, 374),
(6, 4, 380),
(6, 5, 386),
(6, 6, 392),
(6, 7, 398),
(6, 8, 404),
(6, 9, 410),
(6, 10, 416),
(6, 11, 422),
-- الدرجة 7
(7, 1, 296),
(7, 2, 302),
(7, 3, 308),
(7, 4, 314),
(7, 5, 320),
(7, 6, 326),
(7, 7, 332),
(7, 8, 338),
(7, 9, 344),
(7, 10, 350),
(7, 11, 356),
-- الدرجة 8
(8, 1, 260),
(8, 2, 263),
(8, 3, 266),
(8, 4, 269),
(8, 5, 272),
(8, 6, 275),
(8, 7, 278),
(8, 8, 281),
(8, 9, 284),
(8, 10, 287),
(8, 11, 290),
-- الدرجة 9
(9, 1, 210),
(9, 2, 213),
(9, 3, 216),
(9, 4, 219),
(9, 5, 222),
(9, 6, 225),
(9, 7, 228),
(9, 8, 231),
(9, 9, 234),
(9, 10, 237),
(9, 11, 240),
-- الدرجة 10
(10, 1, 170),
(10, 2, 173),
(10, 3, 176),
(10, 4, 179),
(10, 5, 182),
(10, 6, 185),
(10, 7, 188),
(10, 8, 191),
(10, 9, 194),
(10, 10, 197),
(10, 11, 200);

-- إدخال بيانات مدة الخدمة للدرجات (حسب جدول الرواتب المعتمد 2015)
-- حذف البيانات القديمة أولاً
DELETE FROM `grade_service_years`;
-- ثم إدخال البيانات الجديدة
INSERT INTO `grade_service_years` (`grade_id`, `service_years`) VALUES
(1, 5),
(2, 5),
(3, 5),
(4, 5),
(5, 5),
(6, 4),
(7, 4),
(8, 4),
(9, 4),
(10, 4);
