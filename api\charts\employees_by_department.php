<?php
/**
 * API: Employees by Department Chart Data
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Define root path
$root_path = dirname(dirname(__DIR__));

// Include required files
require_once $root_path . '/config/config.php';
require_once $root_path . '/config/database.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// Require login
requireLogin();

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Check if departments table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'departments'");
    if ($tableCheck->rowCount() == 0) {
        // Return dummy data if table doesn't exist
        echo json_encode([
            'labels' => ['قسم الموارد البشرية', 'قسم تكنولوجيا المعلومات', 'قسم المالية', 'قسم الإدارة'],
            'values' => [15, 10, 8, 5]
        ]);
        exit;
    }

    // Get employees by department
    $stmt = $pdo->query("
        SELECT d.name, COUNT(e.id) as count
        FROM departments d
        LEFT JOIN employees e ON d.id = e.department_id
        GROUP BY d.id, d.name
        ORDER BY count DESC
    ");

    $data = $stmt->fetchAll();

    // Format data for chart
    $labels = [];
    $values = [];

    if (count($data) > 0) {
        foreach ($data as $row) {
            $labels[] = $row['name'];
            $values[] = (int)$row['count'];
        }
    } else {
        // Return dummy data if no data found
        $labels = ['قسم الموارد البشرية', 'قسم تكنولوجيا المعلومات', 'قسم المالية', 'قسم الإدارة'];
        $values = [15, 10, 8, 5];
    }

    // Return JSON response
    echo json_encode([
        'labels' => $labels,
        'values' => $values
    ]);
} catch (PDOException $e) {
    // Log error
    error_log("API Error (employees_by_department): " . $e->getMessage());

    // Return dummy data instead of error
    echo json_encode([
        'labels' => ['قسم الموارد البشرية', 'قسم تكنولوجيا المعلومات', 'قسم المالية', 'قسم الإدارة'],
        'values' => [15, 10, 8, 5]
    ]);
}
?>
