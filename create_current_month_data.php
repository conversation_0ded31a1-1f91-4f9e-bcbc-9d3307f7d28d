<?php
/**
 * Create Current Month Data
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

// Function to check if table exists
function tableExists($pdo, $tableName) {
    $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
    return $stmt->rowCount() > 0;
}

// Function to get employee count
function getEmployeeCount($pdo) {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM employees");
    return $stmt->fetch(PDO::FETCH_ASSOC)['count'];
}

// Function to get user count
function getUserCount($pdo) {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    return $stmt->fetch(PDO::FETCH_ASSOC)['count'];
}

// Function to insert sample data
function insertSampleData($pdo, $tableName, $sql) {
    try {
        $pdo->exec($sql);
        return true;
    } catch (PDOException $e) {
        echo "<p style='color: red;'>خطأ في إدخال بيانات تجريبية لجدول $tableName: " . $e->getMessage() . "</p>";
        return false;
    }
}

// Start HTML output
echo "<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إنشاء بيانات للشهر الحالي</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #0066cc; margin-top: 30px; }
        p { margin: 10px 0; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .back-link { display: inline-block; margin-top: 20px; padding: 10px 15px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>إنشاء بيانات للشهر الحالي</h1>";

// Check if employees and users exist
$employeeCount = getEmployeeCount($pdo);
$userCount = getUserCount($pdo);

if ($employeeCount == 0) {
    echo "<p class='warning'>⚠️ لا يوجد موظفين في النظام. يجب إضافة موظفين أولاً قبل إنشاء البيانات التجريبية.</p>";
} else {
    echo "<p class='info'>ℹ️ يوجد $employeeCount موظف في النظام.</p>";
}

if ($userCount == 0) {
    echo "<p class='warning'>⚠️ لا يوجد مستخدمين في النظام. يجب إضافة مستخدمين أولاً قبل إنشاء البيانات التجريبية.</p>";
} else {
    echo "<p class='info'>ℹ️ يوجد $userCount مستخدم في النظام.</p>";
}

// Insert sample data if tables exist and there are employees and users
if ($employeeCount > 0 && $userCount > 0) {
    echo "<h2>إنشاء بيانات للشهر الحالي</h2>";
    
    // Get current year and month
    $currentYear = date('Y');
    $currentMonth = date('m');
    $currentDay = date('d');
    
    // Get all employees
    $employeeStmt = $pdo->query("SELECT id, current_grade FROM employees LIMIT 5");
    $employees = $employeeStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get a user
    $userStmt = $pdo->query("SELECT id FROM users LIMIT 1");
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);
    $userId = $user['id'];
    
    // Insert allowances for current month
    if (tableExists($pdo, 'allowances')) {
        echo "<h3>إنشاء علاوات للشهر الحالي</h3>";
        
        // Check if there are already allowances for the current month
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM allowances 
            WHERE YEAR(allowance_date) = ? AND MONTH(allowance_date) = ?
        ");
        $stmt->execute([$currentYear, $currentMonth]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($count > 0) {
            echo "<p class='info'>ℹ️ يوجد بالفعل $count علاوة في الشهر الحالي.</p>";
        } else {
            // Insert allowances for each employee
            $sql = "INSERT INTO `allowances` (`employee_id`, `allowance_date`, `allowance_number`, `grade_at_time`, `notes`, `created_by`) VALUES ";
            $values = [];
            
            foreach ($employees as $index => $employee) {
                $employeeId = $employee['id'];
                $currentGrade = $employee['current_grade'];
                $allowanceDate = "$currentYear-$currentMonth-" . str_pad(($index + 1), 2, '0', STR_PAD_LEFT);
                $allowanceNumber = "A-$currentYear$currentMonth-" . str_pad(($index + 1), 3, '0', STR_PAD_LEFT);
                
                $values[] = "($employeeId, '$allowanceDate', '$allowanceNumber', $currentGrade, 'علاوة سنوية للشهر الحالي', $userId)";
            }
            
            $sql .= implode(", ", $values);
            
            if (insertSampleData($pdo, 'allowances', $sql)) {
                echo "<p class='success'>✓ تم إنشاء " . count($employees) . " علاوة للشهر الحالي بنجاح</p>";
            }
        }
    }
    
    // Insert promotions for current month
    if (tableExists($pdo, 'promotions')) {
        echo "<h3>إنشاء ترفيعات للشهر الحالي</h3>";
        
        // Check if there are already promotions for the current month
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM promotions 
            WHERE YEAR(promotion_date) = ? AND MONTH(promotion_date) = ?
        ");
        $stmt->execute([$currentYear, $currentMonth]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($count > 0) {
            echo "<p class='info'>ℹ️ يوجد بالفعل $count ترفيع في الشهر الحالي.</p>";
        } else {
            // Insert promotions for each employee
            $sql = "INSERT INTO `promotions` (`employee_id`, `promotion_date`, `from_grade`, `to_grade`, `years_in_previous_grade`, `notes`, `created_by`) VALUES ";
            $values = [];
            
            foreach ($employees as $index => $employee) {
                $employeeId = $employee['id'];
                $currentGrade = $employee['current_grade'];
                $fromGrade = max(1, $currentGrade - 1);
                $promotionDate = "$currentYear-$currentMonth-" . str_pad(($index + 1), 2, '0', STR_PAD_LEFT);
                
                $values[] = "($employeeId, '$promotionDate', $fromGrade, $currentGrade, 4, 'ترفيع دوري للشهر الحالي', $userId)";
            }
            
            $sql .= implode(", ", $values);
            
            if (insertSampleData($pdo, 'promotions', $sql)) {
                echo "<p class='success'>✓ تم إنشاء " . count($employees) . " ترفيع للشهر الحالي بنجاح</p>";
            }
        }
    }
    
    // Insert appreciation letters for current month
    if (tableExists($pdo, 'appreciation_letters')) {
        echo "<h3>إنشاء كتب شكر للشهر الحالي</h3>";
        
        // Check if there are already appreciation letters for the current month
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM appreciation_letters 
            WHERE YEAR(letter_date) = ? AND MONTH(letter_date) = ?
        ");
        $stmt->execute([$currentYear, $currentMonth]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($count > 0) {
            echo "<p class='info'>ℹ️ يوجد بالفعل $count كتاب شكر في الشهر الحالي.</p>";
        } else {
            // Insert appreciation letters for each employee
            $sql = "INSERT INTO `appreciation_letters` (`employee_id`, `letter_date`, `letter_number`, `issuer`, `months_reduction`, `notes`, `created_by`) VALUES ";
            $values = [];
            
            foreach ($employees as $index => $employee) {
                $employeeId = $employee['id'];
                $letterDate = "$currentYear-$currentMonth-" . str_pad(($index + 1), 2, '0', STR_PAD_LEFT);
                $letterNumber = "L-$currentYear$currentMonth-" . str_pad(($index + 1), 3, '0', STR_PAD_LEFT);
                $issuer = ($index % 2 == 0) ? "'normal'" : "'prime_minister'";
                $monthsReduction = ($index % 2 == 0) ? 0 : 3;
                
                $values[] = "($employeeId, '$letterDate', '$letterNumber', $issuer, $monthsReduction, 'كتاب شكر للشهر الحالي', $userId)";
            }
            
            $sql .= implode(", ", $values);
            
            if (insertSampleData($pdo, 'appreciation_letters', $sql)) {
                echo "<p class='success'>✓ تم إنشاء " . count($employees) . " كتاب شكر للشهر الحالي بنجاح</p>";
            }
        }
    }
}

// Add links to navigate
echo "
    <div style='margin-top: 30px;'>
        <a href='reports.php' class='back-link'>العودة إلى صفحة التقارير</a>
        <a href='debug_reports.php' class='back-link' style='margin-right: 10px;'>تشخيص مشاكل التقارير</a>
    </div>
</body>
</html>";
?>
