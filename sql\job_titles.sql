-- إنشاء جدول العناوين الوظيفية
CREATE TABLE IF NOT EXISTS `job_titles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `grade_id` int(11) NOT NULL,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `grade_id` (`grade_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدخال بيانات العناوين الوظيفية
INSERT INTO `job_titles` (`grade_id`, `title`, `description`) VALUES
-- الدرجة 1 (الأعلى)
(1, 'مدير عام', 'المدير العام للمؤسسة أو الدائرة'),
(1, 'خبير', 'خبير متخصص في مجال معين'),
(1, 'مستشار', 'مستشار في مجال التخصص'),

-- الدرجة 2
(2, 'مدير', 'مدير قسم أو إدارة'),
(2, 'خبير أول', 'خبير أول في مجال التخصص'),
(2, 'مستشار مساعد', 'مستشار مساعد في مجال التخصص'),

-- الدرجة 3
(3, 'رئيس قسم', 'رئيس قسم في الإدارة'),
(3, 'مهندس أقدم', 'مهندس بخبرة عالية'),
(3, 'محاسب أقدم', 'محاسب بخبرة عالية'),
(3, 'مبرمج أقدم', 'مبرمج بخبرة عالية'),

-- الدرجة 4
(4, 'رئيس شعبة', 'رئيس شعبة في القسم'),
(4, 'مهندس أول', 'مهندس بخبرة متوسطة'),
(4, 'محاسب أول', 'محاسب بخبرة متوسطة'),
(4, 'مبرمج أول', 'مبرمج بخبرة متوسطة'),

-- الدرجة 5
(5, 'مسؤول وحدة', 'مسؤول وحدة في الشعبة'),
(5, 'مهندس', 'مهندس'),
(5, 'محاسب', 'محاسب'),
(5, 'مبرمج', 'مبرمج'),
(5, 'إداري أول', 'إداري بخبرة متوسطة'),

-- الدرجة 6
(6, 'مهندس مساعد', 'مهندس حديث التخرج'),
(6, 'محاسب مساعد', 'محاسب حديث التخرج'),
(6, 'مبرمج مساعد', 'مبرمج حديث التخرج'),
(6, 'إداري', 'موظف إداري'),

-- الدرجة 7
(7, 'فني أول', 'فني بخبرة متوسطة'),
(7, 'كاتب أول', 'كاتب بخبرة متوسطة'),
(7, 'سكرتير أول', 'سكرتير بخبرة متوسطة'),

-- الدرجة 8
(8, 'فني', 'فني'),
(8, 'كاتب', 'كاتب'),
(8, 'سكرتير', 'سكرتير'),

-- الدرجة 9
(9, 'فني مساعد', 'فني مساعد'),
(9, 'كاتب مساعد', 'كاتب مساعد'),
(9, 'مساعد إداري', 'مساعد إداري'),

-- الدرجة 10 (الأدنى)
(10, 'موظف خدمة', 'موظف خدمة'),
(10, 'حارس', 'حارس'),
(10, 'سائق', 'سائق'),
(10, 'مراسل', 'مراسل');
