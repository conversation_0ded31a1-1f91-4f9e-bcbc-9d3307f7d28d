<?php
/**
 * Add Allowance Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and proper role
requireRole(['admin', 'hr']);

// Check if employee ID is provided
if (!isset($_GET['employee_id']) || empty($_GET['employee_id'])) {
    flash('error_message', 'معرف الموظف غير صالح', 'alert alert-danger');
    redirect('allowances.php');
}

$employeeId = (int)$_GET['employee_id'];

// Get employee details
try {
    $stmt = $pdo->prepare("
        SELECT e.*, d.name as department_name
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        WHERE e.id = :id
    ");
    $stmt->execute([':id' => $employeeId]);
    $employee = $stmt->fetch();

    if (!$employee) {
        flash('error_message', 'الموظف غير موجود', 'alert alert-danger');
        redirect('allowances.php');
    }

    // Check if employee has reached maximum allowances
    if ($employee['allowances_in_current_grade'] >= ALLOWANCES_PER_GRADE) {
        flash('error_message', 'لقد وصل الموظف إلى الحد الأقصى من العلاوات في الدرجة الحالية', 'alert alert-danger');
        redirect('employee_details.php?id=' . $employeeId);
    }

} catch (PDOException $e) {
    error_log("Get Employee Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الموظف', 'alert alert-danger');
    redirect('allowances.php');
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $allowanceDate = sanitize($_POST['allowance_date']);
    $notes = sanitize($_POST['notes']);

    // Validate inputs
    $errors = [];

    if (empty($allowanceDate)) {
        $errors[] = 'تاريخ العلاوة مطلوب';
    }

    // If no errors, add allowance
    if (empty($errors)) {
        try {
            // Start transaction
            $pdo->beginTransaction();

            // Calculate allowance number
            $allowanceNumber = $employee['allowances_in_current_grade'] + 1;

            // Calculate new stage number
            $newStage = $employee['current_stage'] + 1;

            // Get new nominal salary
            $nominalSalary = getNominalSalary($employee['current_grade'], $newStage);

            // Insert allowance record
            $stmt = $pdo->prepare("
                INSERT INTO allowances (
                    employee_id, allowance_date, grade_at_time, allowance_number, notes, created_by
                ) VALUES (
                    :employee_id, :allowance_date, :grade_at_time, :allowance_number, :notes, :created_by
                )
            ");

            $stmt->execute([
                ':employee_id' => $employeeId,
                ':allowance_date' => $allowanceDate,
                ':grade_at_time' => $employee['current_grade'],
                ':allowance_number' => $allowanceNumber,
                ':notes' => $notes,
                ':created_by' => $_SESSION['user_id']
            ]);

            // Add salary history record
            addSalaryHistory(
                $employeeId,
                $employee['current_grade'],
                $newStage,
                $nominalSalary,
                $allowanceDate,
                'allowance',
                'علاوة سنوية - المرحلة ' . $newStage
            );

            // Update employee record
            $nextAllowanceDate = calculateNextAllowanceDate($allowanceDate);

            $stmt = $pdo->prepare("
                UPDATE employees SET
                    allowances_in_current_grade = :allowances_in_current_grade,
                    current_stage = :current_stage,
                    nominal_salary = :nominal_salary,
                    last_allowance_date = :last_allowance_date,
                    next_allowance_date = :next_allowance_date
                WHERE id = :id
            ");

            $stmt->execute([
                ':allowances_in_current_grade' => $allowanceNumber,
                ':current_stage' => $newStage,
                ':nominal_salary' => $nominalSalary,
                ':last_allowance_date' => $allowanceDate,
                ':next_allowance_date' => $nextAllowanceDate,
                ':id' => $employeeId
            ]);

            // Log activity
            logActivity(
                $_SESSION['user_id'],
                'إضافة علاوة',
                'allowances',
                $pdo->lastInsertId(),
                'تمت إضافة علاوة للموظف: ' . $employee['full_name']
            );

            // Commit transaction
            $pdo->commit();

            // Redirect to employee details
            flash('success_message', 'تمت إضافة العلاوة بنجاح', 'alert alert-success');
            redirect('employee_details.php?id=' . $employeeId);

        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();

            error_log("Add Allowance Error: " . $e->getMessage());
            flash('error_message', 'حدث خطأ أثناء إضافة العلاوة', 'alert alert-danger');
        }
    } else {
        // Display errors
        $errorMessage = '<ul class="mb-0">';
        foreach ($errors as $error) {
            $errorMessage .= '<li>' . $error . '</li>';
        }
        $errorMessage .= '</ul>';

        flash('error_message', $errorMessage, 'alert alert-danger');
    }
}

// Set default allowance date to today
$defaultAllowanceDate = date('Y-m-d');
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-money-bill-wave me-2"></i> إضافة علاوة
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="employee_details.php?id=<?php echo $employeeId; ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى تفاصيل الموظف
        </a>
    </div>
</div>

<!-- Employee Information Card -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-user me-2"></i> معلومات الموظف
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الرقم الوظيفي:</div>
                <div><?php echo $employee['employee_number']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الاسم:</div>
                <div><?php echo $employee['full_name']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">القسم:</div>
                <div><?php echo $employee['department_name']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الدرجة الحالية:</div>
                <div>الدرجة <?php echo $employee['current_grade']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">العلاوات المستلمة:</div>
                <div><?php echo $employee['allowances_in_current_grade']; ?> من <?php echo ALLOWANCES_PER_GRADE; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">تاريخ آخر علاوة:</div>
                <div><?php echo $employee['last_allowance_date'] ? formatArabicDate($employee['last_allowance_date']) : 'لا يوجد'; ?></div>
            </div>
        </div>
    </div>
</div>

<!-- Add Allowance Form -->
<div class="card">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">
            <i class="fas fa-plus-circle me-2"></i> إضافة علاوة جديدة
        </h5>
    </div>
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF'] . '?employee_id=' . $employeeId; ?>" method="post" class="needs-validation" novalidate>
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="allowance_date" class="form-label">تاريخ العلاوة <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="allowance_date" name="allowance_date" value="<?php echo $defaultAllowanceDate; ?>" required>
                    <div class="invalid-feedback">يرجى إدخال تاريخ العلاوة</div>
                </div>
                <div class="col-md-6">
                    <label for="allowance_number" class="form-label">رقم العلاوة</label>
                    <input type="text" class="form-control" id="allowance_number" value="<?php echo $employee['allowances_in_current_grade'] + 1; ?>" readonly>
                    <div class="form-text">رقم العلاوة في الدرجة الحالية</div>
                </div>
            </div>

            <div class="mb-3">
                <label for="notes" class="form-label">ملاحظات</label>
                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
            </div>

            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                سيتم تحديث تاريخ العلاوة القادمة تلقائياً بعد إضافة هذه العلاوة.
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-success px-5">
                    <i class="fas fa-save me-1"></i> حفظ العلاوة
                </button>
                <a href="employee_details.php?id=<?php echo $employeeId; ?>" class="btn btn-secondary px-5">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
