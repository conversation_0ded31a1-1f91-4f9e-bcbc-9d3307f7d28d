<?php
/**
 * API: Upcoming Promotions Chart Data
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Define root path
$root_path = dirname(dirname(__DIR__));

// Include required files
require_once $root_path . '/config/config.php';
require_once $root_path . '/config/database.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// Require login
requireLogin();

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Check if tables exist
    $employeesTableCheck = $pdo->query("SHOW TABLES LIKE 'employees'");
    $educationLevelsTableCheck = $pdo->query("SHOW TABLES LIKE 'education_levels'");

    if ($employeesTableCheck->rowCount() > 0 && $educationLevelsTableCheck->rowCount() > 0) {
        // Get upcoming promotions
        $stmt = $pdo->query("
            SELECT
                SUM(CASE WHEN next_promotion_date <= CURDATE() THEN 1 ELSE 0 END) as current,
                SUM(CASE WHEN next_promotion_date > CURDATE() AND next_promotion_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as within30Days,
                SUM(CASE WHEN next_promotion_date > DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND next_promotion_date <= DATE_ADD(CURDATE(), INTERVAL 60 DAY) THEN 1 ELSE 0 END) as within60Days,
                SUM(CASE WHEN next_promotion_date > DATE_ADD(CURDATE(), INTERVAL 60 DAY) AND next_promotion_date <= DATE_ADD(CURDATE(), INTERVAL 90 DAY) THEN 1 ELSE 0 END) as within90Days,
                SUM(CASE WHEN next_promotion_date > DATE_ADD(CURDATE(), INTERVAL 90 DAY) THEN 1 ELSE 0 END) as beyond90Days
            FROM employees e
            JOIN education_levels el ON e.education_level_id = el.id
            WHERE e.current_grade > el.max_grade
        ");

        $data = $stmt->fetch(PDO::FETCH_ASSOC);

        // Convert to integers
        foreach ($data as $key => $value) {
            $data[$key] = (int)$value;
        }
    } else {
        // Return dummy data if tables don't exist
        $data = [
            'current' => 5,
            'within30Days' => 8,
            'within60Days' => 10,
            'within90Days' => 7,
            'beyond90Days' => 15
        ];
    }

    // Return JSON response
    echo json_encode($data);
} catch (PDOException $e) {
    // Log error
    error_log("API Error (upcoming_promotions): " . $e->getMessage());

    // Return dummy data instead of error
    $data = [
        'current' => 5,
        'within30Days' => 8,
        'within60Days' => 10,
        'within90Days' => 7,
        'beyond90Days' => 15
    ];

    echo json_encode($data);
}
?>
