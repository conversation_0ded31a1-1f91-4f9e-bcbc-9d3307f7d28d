<?php
/**
 * Dropdown Position Test
 * اختبار موضع القوائم المنسدلة
 */

require_once 'includes/header.php';
requireLogin();
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4 text-gradient-primary">
                <i class="fas fa-tools me-2"></i>
                اختبار موضع القوائم المنسدلة
            </h1>
            
            <!-- تعليمات الاختبار -->
            <div class="alert alert-info shadow-modern rounded-modern">
                <h5><i class="fas fa-info-circle me-2"></i>تعليمات الاختبار:</h5>
                <ol class="mb-0">
                    <li>انقر على قائمة "الإدارة" في شريط التنقل أعلاه</li>
                    <li>تأكد من أن القائمة تظهر <strong>خارج</strong> شريط التنقل وليس داخله</li>
                    <li>انقر على قائمة الملف الشخصي (اسم المستخدم) في شريط التنقل</li>
                    <li>تأكد من أن القائمة تظهر <strong>خارج</strong> شريط التنقل وليس داخله</li>
                    <li>انقر على أيقونة الإشعارات في شريط التنقل</li>
                    <li>تأكد من أن قائمة الإشعارات تظهر <strong>خارج</strong> شريط التنقل</li>
                </ol>
            </div>
            
            <!-- حالة الإصلاحات -->
            <div class="card mb-4 shadow-modern rounded-modern">
                <div class="card-header bg-gradient-primary">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-wrench me-2"></i>
                        حالة الإصلاحات المطبقة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إصلاحات CSS:</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>overflow: visible للـ navbar</span>
                                    <span class="badge bg-success">✓</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>position: absolute للقوائم</span>
                                    <span class="badge bg-success">✓</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>top: calc(100% + 0.5rem)</span>
                                    <span class="badge bg-success">✓</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>z-index: 9999</span>
                                    <span class="badge bg-success">✓</span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>إصلاحات JavaScript:</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>fixDropdownPositioning()</span>
                                    <span class="badge bg-success">✓</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Bootstrap reinitialization</span>
                                    <span class="badge bg-success">✓</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Custom event handlers</span>
                                    <span class="badge bg-success">✓</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Position validation</span>
                                    <span class="badge bg-success">✓</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- اختبار مرئي -->
            <div class="card mb-4 shadow-modern rounded-modern">
                <div class="card-header bg-gradient-success">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-eye me-2"></i>
                        اختبار مرئي للقوائم المنسدلة
                    </h5>
                </div>
                <div class="card-body">
                    <p>استخدم الأزرار أدناه لاختبار القوائم المنسدلة:</p>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <button class="btn btn-primary w-100" onclick="testAdminDropdown()">
                                <i class="fas fa-cog me-2"></i>
                                اختبار قائمة الإدارة
                            </button>
                        </div>
                        <div class="col-md-4 mb-3">
                            <button class="btn btn-success w-100" onclick="testUserDropdown()">
                                <i class="fas fa-user me-2"></i>
                                اختبار قائمة المستخدم
                            </button>
                        </div>
                        <div class="col-md-4 mb-3">
                            <button class="btn btn-warning w-100" onclick="testNotificationsDropdown()">
                                <i class="fas fa-bell me-2"></i>
                                اختبار قائمة الإشعارات
                            </button>
                        </div>
                    </div>
                    
                    <div id="testResults" class="mt-3"></div>
                </div>
            </div>
            
            <!-- معلومات تشخيصية -->
            <div class="card mb-4 shadow-modern rounded-modern">
                <div class="card-header" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-stethoscope me-2"></i>
                        معلومات تشخيصية
                    </h5>
                </div>
                <div class="card-body">
                    <div id="diagnosticInfo">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        جاري جمع المعلومات التشخيصية...
                    </div>
                </div>
            </div>
            
            <!-- نتائج الاختبار -->
            <div class="card mb-4 shadow-modern rounded-modern">
                <div class="card-header" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-check-circle me-2"></i>
                        نتائج الاختبار
                    </h5>
                </div>
                <div class="card-body">
                    <div id="testResultsSummary">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            قم بتشغيل الاختبارات أعلاه لرؤية النتائج هنا.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // جمع المعلومات التشخيصية
    collectDiagnosticInfo();
    
    // فحص موضع القوائم المنسدلة
    setTimeout(checkDropdownPositions, 1000);
});

function testAdminDropdown() {
    const adminDropdown = document.getElementById('adminDropdown');
    const resultsDiv = document.getElementById('testResults');
    
    if (adminDropdown) {
        // محاكاة النقر
        adminDropdown.click();
        
        setTimeout(() => {
            const dropdownMenu = adminDropdown.parentElement.querySelector('.dropdown-menu');
            if (dropdownMenu && dropdownMenu.classList.contains('show')) {
                const rect = dropdownMenu.getBoundingClientRect();
                const navbarRect = document.querySelector('.navbar').getBoundingClientRect();
                
                if (rect.top > navbarRect.bottom) {
                    resultsDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>قائمة الإدارة تظهر خارج شريط التنقل بنجاح!</div>';
                } else {
                    resultsDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>قائمة الإدارة لا تزال تظهر داخل شريط التنقل.</div>';
                }
            } else {
                resultsDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>لم تظهر قائمة الإدارة.</div>';
            }
        }, 500);
    } else {
        resultsDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>لم يتم العثور على قائمة الإدارة.</div>';
    }
}

function testUserDropdown() {
    const userDropdown = document.getElementById('userDropdown');
    const resultsDiv = document.getElementById('testResults');
    
    if (userDropdown) {
        userDropdown.click();
        
        setTimeout(() => {
            const dropdownMenu = userDropdown.parentElement.querySelector('.dropdown-menu');
            if (dropdownMenu && dropdownMenu.classList.contains('show')) {
                const rect = dropdownMenu.getBoundingClientRect();
                const navbarRect = document.querySelector('.navbar').getBoundingClientRect();
                
                if (rect.top > navbarRect.bottom) {
                    resultsDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>قائمة المستخدم تظهر خارج شريط التنقل بنجاح!</div>';
                } else {
                    resultsDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>قائمة المستخدم لا تزال تظهر داخل شريط التنقل.</div>';
                }
            } else {
                resultsDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>لم تظهر قائمة المستخدم.</div>';
            }
        }, 500);
    } else {
        resultsDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>لم يتم العثور على قائمة المستخدم.</div>';
    }
}

function testNotificationsDropdown() {
    const notificationsDropdown = document.getElementById('notificationsDropdown');
    const resultsDiv = document.getElementById('testResults');
    
    if (notificationsDropdown) {
        notificationsDropdown.click();
        
        setTimeout(() => {
            const notificationsContainer = document.getElementById('notificationsContainer');
            if (notificationsContainer && notificationsContainer.classList.contains('show')) {
                const rect = notificationsContainer.getBoundingClientRect();
                const navbarRect = document.querySelector('.navbar').getBoundingClientRect();
                
                if (rect.top > navbarRect.bottom) {
                    resultsDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>قائمة الإشعارات تظهر خارج شريط التنقل بنجاح!</div>';
                } else {
                    resultsDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>قائمة الإشعارات لا تزال تظهر داخل شريط التنقل.</div>';
                }
            } else {
                resultsDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>لم تظهر قائمة الإشعارات.</div>';
            }
        }, 500);
    } else {
        resultsDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>لم يتم العثور على قائمة الإشعارات.</div>';
    }
}

function collectDiagnosticInfo() {
    const diagnosticDiv = document.getElementById('diagnosticInfo');
    
    setTimeout(() => {
        const navbar = document.querySelector('.navbar');
        const dropdowns = document.querySelectorAll('.dropdown-menu');
        
        let info = '<div class="row">';
        info += '<div class="col-md-6">';
        info += '<h6>معلومات شريط التنقل:</h6>';
        info += '<ul class="list-group list-group-flush">';
        info += `<li class="list-group-item">Overflow: ${navbar ? getComputedStyle(navbar).overflow : 'غير موجود'}</li>`;
        info += `<li class="list-group-item">Position: ${navbar ? getComputedStyle(navbar).position : 'غير موجود'}</li>`;
        info += `<li class="list-group-item">Z-index: ${navbar ? getComputedStyle(navbar).zIndex : 'غير موجود'}</li>`;
        info += `<li class="list-group-item">عدد القوائم المنسدلة: ${dropdowns.length}</li>`;
        info += '</ul>';
        info += '</div>';
        
        info += '<div class="col-md-6">';
        info += '<h6>معلومات القوائم المنسدلة:</h6>';
        info += '<ul class="list-group list-group-flush">';
        
        dropdowns.forEach((dropdown, index) => {
            const style = getComputedStyle(dropdown);
            info += `<li class="list-group-item">قائمة ${index + 1}: Position: ${style.position}, Z-index: ${style.zIndex}</li>`;
        });
        
        info += '</ul>';
        info += '</div>';
        info += '</div>';
        
        diagnosticDiv.innerHTML = info;
    }, 1000);
}

function checkDropdownPositions() {
    const summaryDiv = document.getElementById('testResultsSummary');
    const navbar = document.querySelector('.navbar');
    const dropdowns = document.querySelectorAll('.dropdown-menu');
    
    let allGood = true;
    let issues = [];
    
    if (!navbar) {
        issues.push('شريط التنقل غير موجود');
        allGood = false;
    } else {
        const navbarStyle = getComputedStyle(navbar);
        if (navbarStyle.overflow !== 'visible') {
            issues.push('شريط التنقل لا يحتوي على overflow: visible');
            allGood = false;
        }
    }
    
    dropdowns.forEach((dropdown, index) => {
        const style = getComputedStyle(dropdown);
        if (style.position !== 'absolute') {
            issues.push(`القائمة ${index + 1} لا تحتوي على position: absolute`);
            allGood = false;
        }
        if (parseInt(style.zIndex) < 9999) {
            issues.push(`القائمة ${index + 1} لديها z-index منخفض`);
            allGood = false;
        }
    });
    
    if (allGood) {
        summaryDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i><strong>ممتاز!</strong> جميع القوائم المنسدلة مُعدة بشكل صحيح لتظهر خارج شريط التنقل.</div>';
    } else {
        let issuesHtml = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i><strong>تم العثور على مشاكل:</strong><ul class="mb-0 mt-2">';
        issues.forEach(issue => {
            issuesHtml += `<li>${issue}</li>`;
        });
        issuesHtml += '</ul></div>';
        summaryDiv.innerHTML = issuesHtml;
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
