<?php
/**
 * Employee Details Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    flash('error_message', 'معرف الموظف غير صالح', 'alert alert-danger');
    redirect('employees.php');
}

$employeeId = (int)$_GET['id'];

// Get employee details
try {
    $stmt = $pdo->prepare("
        SELECT e.*, d.name as department_name, el.name as education_level_name, el.max_grade
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        JOIN education_levels el ON e.education_level_id = el.id
        WHERE e.id = :id
    ");
    $stmt->execute([':id' => $employeeId]);
    $employee = $stmt->fetch();

    if (!$employee) {
        flash('error_message', 'الموظف غير موجود', 'alert alert-danger');
        redirect('employees.php');
    }

    // Get allowances history
    $allowancesStmt = $pdo->prepare("
        SELECT a.*, u.full_name as created_by_name
        FROM allowances a
        JOIN users u ON a.created_by = u.id
        WHERE a.employee_id = :employee_id
        ORDER BY a.allowance_date DESC
    ");
    $allowancesStmt->execute([':employee_id' => $employeeId]);
    $allowances = $allowancesStmt->fetchAll();

    // Get promotions history
    $promotionsStmt = $pdo->prepare("
        SELECT p.*, u.full_name as created_by_name
        FROM promotions p
        JOIN users u ON p.created_by = u.id
        WHERE p.employee_id = :employee_id
        ORDER BY p.promotion_date DESC
    ");
    $promotionsStmt->execute([':employee_id' => $employeeId]);
    $promotions = $promotionsStmt->fetchAll();

    // Get appreciation letters
    $lettersStmt = $pdo->prepare("
        SELECT a.*, u.full_name as created_by_name
        FROM appreciation_letters a
        JOIN users u ON a.created_by = u.id
        WHERE a.employee_id = :employee_id
        ORDER BY a.letter_date DESC
    ");
    $lettersStmt->execute([':employee_id' => $employeeId]);
    $letters = $lettersStmt->fetchAll();

} catch (PDOException $e) {
    error_log("Get Employee Details Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الموظف', 'alert alert-danger');
    redirect('employees.php');
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-user me-2"></i> بيانات الموظف
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="edit_employee.php?id=<?php echo $employeeId; ?>" class="btn btn-warning">
            <i class="fas fa-edit me-1"></i> تعديل
        </a>
        <a href="employees.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى قائمة الموظفين
        </a>
        <button class="btn btn-outline-secondary btn-print">
            <i class="fas fa-print me-1"></i> طباعة
        </button>
    </div>
</div>

<!-- Employee Information -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-info-circle me-2"></i> المعلومات الأساسية
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الرقم الوظيفي:</div>
                <div><?php echo $employee['employee_number']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الاسم:</div>
                <div><?php echo $employee['full_name']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">العنوان الوظيفي:</div>
                <div><?php echo $employee['job_title']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الدرجة الحالية:</div>
                <div>الدرجة <?php echo $employee['current_grade']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">المرحلة الحالية:</div>
                <div>المرحلة <?php echo isset($employee['current_stage']) ? $employee['current_stage'] : 1; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">القسم:</div>
                <div><?php echo $employee['department_name']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">التحصيل الدراسي:</div>
                <div><?php echo $employee['education_level_name']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">تاريخ التعيين:</div>
                <div><?php echo formatArabicDate($employee['hire_date']); ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">تاريخ الميلاد:</div>
                <div><?php echo isset($employee['birth_date']) ? formatArabicDate($employee['birth_date']) : 'غير محدد'; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">سنوات الخدمة:</div>
                <div><?php echo $employee['years_of_service']; ?> سنة</div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">سنوات الخدمة في الدرجة الحالية:</div>
                <div><?php echo $employee['years_in_current_grade']; ?> سنة</div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">تاريخ التقاعد:</div>
                <div><?php echo isset($employee['retirement_date']) ? formatArabicDate($employee['retirement_date']) : 'غير محدد'; ?></div>
            </div>
        </div>
    </div>
</div>

<!-- Salary Information -->
<div class="card mb-4">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">
            <i class="fas fa-money-bill-wave me-2"></i> معلومات الراتب الاسمي
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الراتب الاسمي الحالي:</div>
                <div><?php echo isset($employee['nominal_salary']) ? number_format($employee['nominal_salary'], 2) . ' دينار' : 'غير محدد'; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">الدرجة الحالية:</div>
                <div>الدرجة <?php echo $employee['current_grade']; ?></div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="fw-bold">المرحلة الحالية:</div>
                <div>المرحلة <?php echo isset($employee['current_stage']) ? $employee['current_stage'] : 1; ?></div>
            </div>
        </div>

        <?php if (hasRole(['admin', 'hr'])): ?>
            <div class="mt-3">
                <a href="salary_history.php?id=<?php echo $employeeId; ?>" class="btn btn-info">
                    <i class="fas fa-history me-1"></i> عرض تاريخ الراتب الاسمي
                </a>
                <?php if (!$employee['law_103_applied'] && $employee['current_grade'] > $employee['max_grade']): ?>
                    <a href="apply_law_103.php?id=<?php echo $employeeId; ?>" class="btn btn-warning">
                        <i class="fas fa-graduation-cap me-1"></i> تطبيق قانون 103
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Allowances and Promotions Information -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i> معلومات العلاوات
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="fw-bold">عدد العلاوات في الدرجة الحالية:</div>
                    <div><?php echo $employee['allowances_in_current_grade']; ?> من <?php echo ALLOWANCES_PER_GRADE; ?></div>
                </div>
                <div class="mb-3">
                    <div class="fw-bold">تاريخ آخر علاوة:</div>
                    <div><?php echo $employee['last_allowance_date'] ? formatArabicDate($employee['last_allowance_date']) : 'لا يوجد'; ?></div>
                </div>
                <div class="mb-3">
                    <div class="fw-bold">تاريخ العلاوة القادمة:</div>
                    <div>
                        <?php if ($employee['allowances_in_current_grade'] >= ALLOWANCES_PER_GRADE): ?>
                            <span class="badge bg-danger">اكتملت العلاوات في الدرجة الحالية</span>
                        <?php else: ?>
                            <?php echo formatArabicDate($employee['next_allowance_date']); ?>
                            <?php if (isEligibleForAllowance($employee)): ?>
                                <span class="badge bg-success">مستحق</span>
                                <?php if (hasRole(['admin', 'hr'])): ?>
                                    <a href="add_allowance.php?employee_id=<?php echo $employeeId; ?>" class="btn btn-sm btn-success ms-2">
                                        <i class="fas fa-plus me-1"></i> إضافة علاوة
                                    </a>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-level-up-alt me-2"></i> معلومات الترفيع
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="fw-bold">تاريخ آخر ترفيع:</div>
                    <div><?php echo $employee['last_promotion_date'] ? formatArabicDate($employee['last_promotion_date']) : 'لا يوجد'; ?></div>
                </div>
                <div class="mb-3">
                    <div class="fw-bold">تاريخ الترفيع القادم:</div>
                    <div>
                        <?php echo formatArabicDate($employee['next_promotion_date']); ?>
                        <?php if (isEligibleForPromotion($employee)): ?>
                            <span class="badge bg-success">مستحق</span>
                            <?php if (hasRole(['admin', 'hr'])): ?>
                                <a href="add_promotion.php?employee_id=<?php echo $employeeId; ?>" class="btn btn-sm btn-primary ms-2">
                                    <i class="fas fa-plus me-1"></i> إضافة ترفيع
                                </a>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="fw-bold">الحد الأقصى للدرجة حسب التحصيل الدراسي:</div>
                    <div>الدرجة <?php echo getEducationMaxGrade($employee['education_level_id']); ?></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tabs for History -->
<div class="card mb-4">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="employeeTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="allowances-tab" data-bs-toggle="tab" data-bs-target="#allowances" type="button" role="tab">
                    <i class="fas fa-money-bill-wave me-1"></i> سجل العلاوات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="promotions-tab" data-bs-toggle="tab" data-bs-target="#promotions" type="button" role="tab">
                    <i class="fas fa-level-up-alt me-1"></i> سجل الترفيعات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="letters-tab" data-bs-toggle="tab" data-bs-target="#letters" type="button" role="tab">
                    <i class="fas fa-certificate me-1"></i> كتب الشكر
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="salary-history-tab" data-bs-toggle="tab" data-bs-target="#salary-history" type="button" role="tab">
                    <i class="fas fa-history me-1"></i> تاريخ الراتب الاسمي
                </button>
                <button class="nav-link" id="notes-tab" data-bs-toggle="tab" data-bs-target="#notes" type="button" role="tab">
                    <i class="fas fa-sticky-note me-1"></i> الملاحظات
                </button>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <div class="tab-content" id="employeeTabsContent">
            <!-- Allowances History -->
            <div class="tab-pane fade show active" id="allowances" role="tabpanel">
                <?php if (hasRole(['admin', 'hr'])): ?>
                    <div class="mb-3 text-end">
                        <a href="add_allowance.php?employee_id=<?php echo $employeeId; ?>" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i> إضافة علاوة
                        </a>
                    </div>
                <?php endif; ?>

                <?php if (count($allowances) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>تاريخ العلاوة</th>
                                    <th>الدرجة</th>
                                    <th>رقم العلاوة</th>
                                    <th>ملاحظات</th>
                                    <th>تمت بواسطة</th>
                                    <th>تاريخ الإضافة</th>
                                    <?php if (hasRole(['admin', 'hr'])): ?>
                                        <th class="no-print">الإجراءات</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($allowances as $allowance): ?>
                                    <tr>
                                        <td><?php echo formatArabicDate($allowance['allowance_date']); ?></td>
                                        <td>الدرجة <?php echo $allowance['grade_at_time']; ?></td>
                                        <td><?php echo $allowance['allowance_number']; ?></td>
                                        <td><?php echo $allowance['notes'] ?: '-'; ?></td>
                                        <td><?php echo $allowance['created_by_name']; ?></td>
                                        <td><?php echo date('d/m/Y', strtotime($allowance['created_at'])); ?></td>
                                        <?php if (hasRole(['admin', 'hr'])): ?>
                                            <td class="no-print">
                                                <a href="edit_allowance.php?id=<?php echo $allowance['id']; ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="delete_allowance.php?id=<?php echo $allowance['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا توجد سجلات للعلاوات
                    </div>
                <?php endif; ?>
            </div>

            <!-- Promotions History -->
            <div class="tab-pane fade" id="promotions" role="tabpanel">
                <?php if (hasRole(['admin', 'hr'])): ?>
                    <div class="mb-3 text-end">
                        <a href="add_promotion.php?employee_id=<?php echo $employeeId; ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> إضافة ترفيع
                        </a>
                    </div>
                <?php endif; ?>

                <?php if (count($promotions) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>تاريخ الترفيع</th>
                                    <th>من الدرجة</th>
                                    <th>إلى الدرجة</th>
                                    <th>سنوات الخدمة في الدرجة السابقة</th>
                                    <th>ملاحظات</th>
                                    <th>تمت بواسطة</th>
                                    <th>تاريخ الإضافة</th>
                                    <?php if (hasRole(['admin', 'hr'])): ?>
                                        <th class="no-print">الإجراءات</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($promotions as $promotion): ?>
                                    <tr>
                                        <td><?php echo formatArabicDate($promotion['promotion_date']); ?></td>
                                        <td>الدرجة <?php echo $promotion['from_grade']; ?></td>
                                        <td>الدرجة <?php echo $promotion['to_grade']; ?></td>
                                        <td><?php echo $promotion['years_in_previous_grade']; ?> سنة</td>
                                        <td><?php echo $promotion['notes'] ?: '-'; ?></td>
                                        <td><?php echo $promotion['created_by_name']; ?></td>
                                        <td><?php echo date('d/m/Y', strtotime($promotion['created_at'])); ?></td>
                                        <?php if (hasRole(['admin', 'hr'])): ?>
                                            <td class="no-print">
                                                <a href="edit_promotion.php?id=<?php echo $promotion['id']; ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="delete_promotion.php?id=<?php echo $promotion['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا توجد سجلات للترفيعات
                    </div>
                <?php endif; ?>
            </div>

            <!-- Appreciation Letters -->
            <div class="tab-pane fade" id="letters" role="tabpanel">
                <?php if (hasRole(['admin', 'hr'])): ?>
                    <div class="mb-3 text-end">
                        <a href="add_appreciation.php?employee_id=<?php echo $employeeId; ?>" class="btn btn-warning">
                            <i class="fas fa-plus me-1"></i> إضافة كتاب شكر
                        </a>
                    </div>
                <?php endif; ?>

                <?php if (count($letters) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>تاريخ الكتاب</th>
                                    <th>رقم الكتاب</th>
                                    <th>الجهة المانحة</th>
                                    <th>تخفيض المدة</th>
                                    <th>ملاحظات</th>
                                    <th>تمت بواسطة</th>
                                    <th>تاريخ الإضافة</th>
                                    <th class="no-print">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($letters as $letter): ?>
                                    <tr>
                                        <td><?php echo formatArabicDate($letter['letter_date']); ?></td>
                                        <td><?php echo $letter['letter_number']; ?></td>
                                        <td>
                                            <?php echo $letter['issuer'] == 'prime_minister' ? 'رئيس الوزراء' : 'عادي'; ?>
                                        </td>
                                        <td><?php echo $letter['months_reduction']; ?> شهر</td>
                                        <td><?php echo $letter['notes'] ?: '-'; ?></td>
                                        <td><?php echo $letter['created_by_name']; ?></td>
                                        <td><?php echo date('d/m/Y', strtotime($letter['created_at'])); ?></td>
                                        <td class="no-print">
                                            <?php if ($letter['file_path']): ?>
                                                <a href="<?php echo $letter['file_path']; ?>" class="btn btn-sm btn-info" target="_blank" data-bs-toggle="tooltip" title="عرض الملف">
                                                    <i class="fas fa-file"></i>
                                                </a>
                                            <?php endif; ?>

                                            <?php if (hasRole(['admin', 'hr'])): ?>
                                                <a href="edit_appreciation.php?id=<?php echo $letter['id']; ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="delete_appreciation.php?id=<?php echo $letter['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا توجد كتب شكر
                    </div>
                <?php endif; ?>
            </div>

            <!-- Salary History -->
            <div class="tab-pane fade" id="salary-history" role="tabpanel">
                <?php
                // Get salary history
                try {
                    $salaryHistoryStmt = $pdo->prepare("
                        SELECT sh.*, u.full_name as created_by_name
                        FROM salary_history sh
                        JOIN users u ON sh.created_by = u.id
                        WHERE sh.employee_id = :employee_id
                        ORDER BY sh.effective_date DESC
                    ");
                    $salaryHistoryStmt->execute([':employee_id' => $employeeId]);
                    $salaryHistory = $salaryHistoryStmt->fetchAll();
                } catch (PDOException $e) {
                    error_log("Get Salary History Error: " . $e->getMessage());
                    $salaryHistory = [];
                }
                ?>

                <?php if (count($salaryHistory) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>تاريخ التغيير</th>
                                    <th>الدرجة</th>
                                    <th>المرحلة</th>
                                    <th>الراتب الاسمي</th>
                                    <th>السبب</th>
                                    <th>ملاحظات</th>
                                    <th>تمت بواسطة</th>
                                    <th>تاريخ الإضافة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($salaryHistory as $record): ?>
                                    <tr>
                                        <td><?php echo formatArabicDate($record['effective_date']); ?></td>
                                        <td>الدرجة <?php echo $record['grade']; ?></td>
                                        <td>المرحلة <?php echo $record['stage']; ?></td>
                                        <td><?php echo number_format($record['nominal_salary'], 2); ?> دينار</td>
                                        <td>
                                            <?php
                                            $reasons = [
                                                'initial' => 'راتب أولي',
                                                'allowance' => 'علاوة سنوية',
                                                'promotion' => 'ترفيع',
                                                'higher_degree' => 'شهادة أعلى'
                                            ];
                                            echo $reasons[$record['reason']] ?? $record['reason'];
                                            ?>
                                        </td>
                                        <td><?php echo $record['notes'] ?: '-'; ?></td>
                                        <td><?php echo $record['created_by_name']; ?></td>
                                        <td><?php echo date('d/m/Y', strtotime($record['created_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا يوجد تاريخ للراتب الاسمي
                    </div>
                <?php endif; ?>
            </div>

            <!-- Notes -->
            <div class="tab-pane fade" id="notes" role="tabpanel">
                <?php if (!empty($employee['notes'])): ?>
                    <div class="card">
                        <div class="card-body">
                            <?php echo nl2br($employee['notes']); ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا توجد ملاحظات
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
