<?php
/**
 * Dashboard Page
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();

// Set page title
$pageTitle = "لوحة المعلومات";

// Get current date
$currentDate = date('Y-m-d');

// Initialize default values for all variables
$totalEmployees = 0;
$employeesByDept = [];
$employeesByGrade = [];
$upcomingPromotions = [];
$upcomingAllowances = [];
$salaryDistribution = [];
$approachingRetirement = 0;
$genderDistribution = [];
$educationDistribution = [];

// Get statistics
try {
    // Total employees count
    $totalEmployeesStmt = $pdo->query("SELECT COUNT(*) as total FROM employees");
    $totalEmployees = $totalEmployeesStmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Employees by department
    $deptStmt = $pdo->query("
        SELECT d.name, COUNT(e.id) as count
        FROM employees e
        JOIN departments d ON e.department_id = d.id
        GROUP BY e.department_id
        ORDER BY count DESC
    ");
    $employeesByDept = $deptStmt->fetchAll(PDO::FETCH_ASSOC);

    // Employees by grade
    $gradeStmt = $pdo->query("
        SELECT e.current_grade, COUNT(e.id) as count
        FROM employees e
        GROUP BY e.current_grade
        ORDER BY e.current_grade ASC
    ");
    $employeesByGrade = $gradeStmt->fetchAll(PDO::FETCH_ASSOC);

    // Upcoming promotions (employees who are due for promotion in the next 3 months)
    $threeMonthsLater = date('Y-m-d', strtotime('+3 months'));
    try {
        // Log the current date and three months later date for debugging
        error_log("Current Date: " . date('Y-m-d') . ", Three Months Later: " . $threeMonthsLater);

        $upcomingPromotionsStmt = $pdo->prepare("
            SELECT e.id, e.full_name, e.current_grade, e.last_promotion_date,
                d.name as department_name,
                DATE_ADD(e.last_promotion_date, INTERVAL
                    CASE
                        WHEN e.current_grade BETWEEN 1 AND 4 THEN 5
                        ELSE 4
                    END YEAR) as next_promotion_date,
                DATEDIFF(DATE_ADD(e.last_promotion_date, INTERVAL
                    CASE
                        WHEN e.current_grade BETWEEN 1 AND 4 THEN 5
                        ELSE 4
                    END YEAR), CURDATE()) as days_until_promotion
            FROM employees e
            JOIN departments d ON e.department_id = d.id
            WHERE e.last_promotion_date IS NOT NULL
            AND DATE_ADD(e.last_promotion_date, INTERVAL
                CASE
                    WHEN e.current_grade BETWEEN 1 AND 4 THEN 5
                    ELSE 4
                END YEAR) BETWEEN CURDATE() AND ?
            ORDER BY days_until_promotion ASC
            LIMIT 10
        ");
        $upcomingPromotionsStmt->execute([$threeMonthsLater]);
        $upcomingPromotions = $upcomingPromotionsStmt->fetchAll(PDO::FETCH_ASSOC);

        // Log the number of upcoming promotions found
        error_log("Upcoming Promotions Found: " . count($upcomingPromotions));

        // If no promotions found, check if there are any employees with last_promotion_date
        if (empty($upcomingPromotions)) {
            $checkStmt = $pdo->query("SELECT COUNT(*) as count FROM employees WHERE last_promotion_date IS NOT NULL");
            $employeesWithPromotionDate = $checkStmt->fetch(PDO::FETCH_ASSOC)['count'];
            error_log("Employees with last_promotion_date: " . $employeesWithPromotionDate);

            // Check for employees with upcoming promotions in any time frame
            $anyPromotionsStmt = $pdo->query("
                SELECT COUNT(*) as count
                FROM employees
                WHERE last_promotion_date IS NOT NULL
                AND DATE_ADD(last_promotion_date, INTERVAL
                    CASE
                        WHEN current_grade BETWEEN 1 AND 4 THEN 5
                        ELSE 4
                    END YEAR) > CURDATE()
            ");
            $anyUpcomingPromotions = $anyPromotionsStmt->fetch(PDO::FETCH_ASSOC)['count'];
            error_log("Employees with any upcoming promotions: " . $anyUpcomingPromotions);
        }
    } catch (PDOException $e) {
        // If columns don't exist, use empty array
        error_log("Upcoming Promotions Error: " . $e->getMessage());
        $upcomingPromotions = [];
    }

    // Upcoming allowances (employees who are due for allowance in the next 3 months)
    try {
        // Log the current date and three months later date for debugging
        error_log("Current Date for Allowances: " . date('Y-m-d') . ", Three Months Later: " . $threeMonthsLater);

        $upcomingAllowancesStmt = $pdo->prepare("
            SELECT e.id, e.full_name, e.current_grade, e.last_allowance_date,
                d.name as department_name,
                DATE_ADD(e.last_allowance_date, INTERVAL 1 YEAR) as next_allowance_date,
                DATEDIFF(DATE_ADD(e.last_allowance_date, INTERVAL 1 YEAR), CURDATE()) as days_until_allowance
            FROM employees e
            JOIN departments d ON e.department_id = d.id
            WHERE e.last_allowance_date IS NOT NULL
            AND DATE_ADD(e.last_allowance_date, INTERVAL 1 YEAR) BETWEEN CURDATE() AND ?
            ORDER BY days_until_allowance ASC
            LIMIT 10
        ");
        $upcomingAllowancesStmt->execute([$threeMonthsLater]);
        $upcomingAllowances = $upcomingAllowancesStmt->fetchAll(PDO::FETCH_ASSOC);

        // Log the number of upcoming allowances found
        error_log("Upcoming Allowances Found: " . count($upcomingAllowances));

        // If no allowances found, check if there are any employees with last_allowance_date
        if (empty($upcomingAllowances)) {
            $checkStmt = $pdo->query("SELECT COUNT(*) as count FROM employees WHERE last_allowance_date IS NOT NULL");
            $employeesWithAllowanceDate = $checkStmt->fetch(PDO::FETCH_ASSOC)['count'];
            error_log("Employees with last_allowance_date: " . $employeesWithAllowanceDate);

            // Check for employees with upcoming allowances in any time frame
            $anyAllowancesStmt = $pdo->query("
                SELECT COUNT(*) as count
                FROM employees
                WHERE last_allowance_date IS NOT NULL
                AND DATE_ADD(last_allowance_date, INTERVAL 1 YEAR) > CURDATE()
            ");
            $anyUpcomingAllowances = $anyAllowancesStmt->fetch(PDO::FETCH_ASSOC)['count'];
            error_log("Employees with any upcoming allowances: " . $anyUpcomingAllowances);
        }
    } catch (PDOException $e) {
        // If columns don't exist, use empty array
        error_log("Upcoming Allowances Error: " . $e->getMessage());
        $upcomingAllowances = [];
    }

    // Salary distribution - Check if nominal_salary column exists
    try {
        $salaryDistStmt = $pdo->query("
            SELECT
                CASE
                    WHEN nominal_salary < 500000 THEN 'أقل من 500,000'
                    WHEN nominal_salary BETWEEN 500000 AND 750000 THEN '500,000 - 750,000'
                    WHEN nominal_salary BETWEEN 750001 AND 1000000 THEN '750,001 - 1,000,000'
                    WHEN nominal_salary BETWEEN 1000001 AND 1500000 THEN '1,000,001 - 1,500,000'
                    ELSE 'أكثر من 1,500,000'
                END as salary_range,
                COUNT(*) as count
            FROM employees
            GROUP BY salary_range
            ORDER BY MIN(nominal_salary)
        ");
        $salaryDistribution = $salaryDistStmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // If column doesn't exist, use default data
        error_log("Salary Distribution Error: " . $e->getMessage());
        $salaryDistribution = [
            ['salary_range' => 'أقل من 500,000', 'count' => 0],
            ['salary_range' => '500,000 - 750,000', 'count' => 0],
            ['salary_range' => '750,001 - 1,000,000', 'count' => 0],
            ['salary_range' => '1,000,001 - 1,500,000', 'count' => 0],
            ['salary_range' => 'أكثر من 1,500,000', 'count' => 0]
        ];
    }

    // Employees approaching retirement (age > 55) - Check if birth_date column exists
    try {
        $retirementStmt = $pdo->query("
            SELECT COUNT(*) as count
            FROM employees
            WHERE TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) >= 55
        ");
        $approachingRetirement = $retirementStmt->fetch(PDO::FETCH_ASSOC)['count'];
    } catch (PDOException $e) {
        // If column doesn't exist, use default value
        error_log("Retirement Count Error: " . $e->getMessage());
        $approachingRetirement = 0;
    }

    // Gender distribution - Commented out because gender column doesn't exist
    // $genderStmt = $pdo->query("
    //     SELECT gender, COUNT(*) as count
    //     FROM employees
    //     GROUP BY gender
    // ");
    // $genderDistribution = $genderStmt->fetchAll(PDO::FETCH_ASSOC);
    $genderDistribution = []; // Empty array as fallback

    // Education level distribution - Check if education_level_id column exists
    try {
        $eduStmt = $pdo->query("
            SELECT el.name, COUNT(e.id) as count
            FROM employees e
            JOIN education_levels el ON e.education_level_id = el.id
            GROUP BY e.education_level_id
            ORDER BY count DESC
        ");
        $educationDistribution = $eduStmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // If column doesn't exist or table doesn't exist, use default data
        error_log("Education Distribution Error: " . $e->getMessage());
        $educationDistribution = [
            ['name' => 'بكالوريوس', 'count' => 0],
            ['name' => 'دبلوم', 'count' => 0],
            ['name' => 'ماجستير', 'count' => 0],
            ['name' => 'دكتوراه', 'count' => 0],
            ['name' => 'إعدادية', 'count' => 0]
        ];
    }

} catch (PDOException $e) {
    error_log("Dashboard Statistics Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء جلب الإحصائيات: ' . $e->getMessage(), 'alert alert-danger');
}
?>

<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="display-5 mb-0">
                <i class="fas fa-tachometer-alt me-2"></i> لوحة المعلومات
            </h1>
        </div>
        <div class="col-md-6 text-md-end">
            <div class="btn-group" role="group">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة إلى الرئيسية
                </a>
                <button id="refreshDashboard" class="btn btn-primary">
                    <i class="fas fa-sync-alt me-1"></i> تحديث البيانات
                </button>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الموظفين</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalEmployees; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                الترقيات المستحقة قريباً</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo count($upcomingPromotions); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                العلاوات المستحقة قريباً</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo count($upcomingAllowances); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                اقتراب سن التقاعد</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $approachingRetirement; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-times fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Employees by Department Chart -->
        <div class="col-xl-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h6 class="m-0 font-weight-bold">توزيع الموظفين حسب القسم</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="deptChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employees by Grade Chart -->
        <div class="col-xl-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h6 class="m-0 font-weight-bold">توزيع الموظفين حسب الدرجة</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="gradeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Charts Row -->
    <div class="row mb-4">
        <!-- Salary Distribution Chart -->
        <div class="col-xl-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h6 class="m-0 font-weight-bold">توزيع الرواتب</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="salaryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Education & Gender Distribution Chart -->
        <div class="col-xl-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h6 class="m-0 font-weight-bold">توزيع المستوى التعليمي</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="educationChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tables Row -->
    <div class="row">
        <!-- Upcoming Promotions Table -->
        <div class="col-xl-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h6 class="m-0 font-weight-bold">الترقيات المستحقة قريباً</h6>
                </div>
                <div class="card-body">
                    <?php if (count($upcomingPromotions) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>القسم</th>
                                        <th>الدرجة</th>
                                        <th>تاريخ آخر ترقية</th>
                                        <th>تاريخ الترقية القادمة</th>
                                        <th>الأيام المتبقية</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($upcomingPromotions as $promotion): ?>
                                        <tr>
                                            <td>
                                                <a href="view_employee.php?id=<?php echo $promotion['id']; ?>">
                                                    <?php echo $promotion['full_name']; ?>
                                                </a>
                                            </td>
                                            <td><?php echo $promotion['department_name']; ?></td>
                                            <td><?php echo $promotion['current_grade']; ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($promotion['last_promotion_date'])); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($promotion['next_promotion_date'])); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo ($promotion['days_until_promotion'] <= 30) ? 'danger' : 'warning'; ?>">
                                                    <?php echo $promotion['days_until_promotion']; ?> يوم
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> لا توجد ترقيات مستحقة في الأشهر الثلاثة القادمة.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Upcoming Allowances Table -->
        <div class="col-xl-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h6 class="m-0 font-weight-bold">العلاوات المستحقة قريباً</h6>
                </div>
                <div class="card-body">
                    <?php if (count($upcomingAllowances) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>القسم</th>
                                        <th>الدرجة</th>
                                        <th>تاريخ آخر علاوة</th>
                                        <th>تاريخ العلاوة القادمة</th>
                                        <th>الأيام المتبقية</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($upcomingAllowances as $allowance): ?>
                                        <tr>
                                            <td>
                                                <a href="view_employee.php?id=<?php echo $allowance['id']; ?>">
                                                    <?php echo $allowance['full_name']; ?>
                                                </a>
                                            </td>
                                            <td><?php echo $allowance['department_name']; ?></td>
                                            <td><?php echo $allowance['current_grade']; ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($allowance['last_allowance_date'])); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($allowance['next_allowance_date'])); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo ($allowance['days_until_allowance'] <= 30) ? 'danger' : 'warning'; ?>">
                                                    <?php echo $allowance['days_until_allowance']; ?> يوم
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> لا توجد علاوات مستحقة في الأشهر الثلاثة القادمة.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Prepare data for charts
document.addEventListener('DOMContentLoaded', function() {
    // Department Chart
    const deptCtx = document.getElementById('deptChart').getContext('2d');
    const deptData = <?php echo json_encode($employeesByDept); ?>;
    const deptLabels = deptData.map(item => item.name);
    const deptCounts = deptData.map(item => item.count);

    new Chart(deptCtx, {
        type: 'bar',
        data: {
            labels: deptLabels,
            datasets: [{
                label: 'عدد الموظفين',
                data: deptCounts,
                backgroundColor: 'rgba(78, 115, 223, 0.8)',
                borderColor: 'rgba(78, 115, 223, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });

    // Grade Chart
    const gradeCtx = document.getElementById('gradeChart').getContext('2d');
    const gradeData = <?php echo json_encode($employeesByGrade); ?>;
    const gradeLabels = gradeData.map(item => 'الدرجة ' + item.current_grade);
    const gradeCounts = gradeData.map(item => item.count);

    new Chart(gradeCtx, {
        type: 'bar',
        data: {
            labels: gradeLabels,
            datasets: [{
                label: 'عدد الموظفين',
                data: gradeCounts,
                backgroundColor: 'rgba(40, 167, 69, 0.8)',
                borderColor: 'rgba(40, 167, 69, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });

    // Salary Distribution Chart
    const salaryCtx = document.getElementById('salaryChart').getContext('2d');
    const salaryData = <?php echo json_encode($salaryDistribution); ?>;
    const salaryLabels = salaryData.map(item => item.salary_range);
    const salaryCounts = salaryData.map(item => item.count);

    new Chart(salaryCtx, {
        type: 'pie',
        data: {
            labels: salaryLabels,
            datasets: [{
                data: salaryCounts,
                backgroundColor: [
                    'rgba(78, 115, 223, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(246, 194, 62, 0.8)',
                    'rgba(231, 74, 59, 0.8)',
                    'rgba(54, 185, 204, 0.8)'
                ],
                borderColor: [
                    'rgba(78, 115, 223, 1)',
                    'rgba(40, 167, 69, 1)',
                    'rgba(246, 194, 62, 1)',
                    'rgba(231, 74, 59, 1)',
                    'rgba(54, 185, 204, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });

    // Education Distribution Chart
    const eduCtx = document.getElementById('educationChart').getContext('2d');
    const eduData = <?php echo json_encode($educationDistribution); ?>;
    const eduLabels = eduData.map(item => item.name);
    const eduCounts = eduData.map(item => item.count);

    new Chart(eduCtx, {
        type: 'doughnut',
        data: {
            labels: eduLabels,
            datasets: [{
                data: eduCounts,
                backgroundColor: [
                    'rgba(246, 194, 62, 0.8)',
                    'rgba(78, 115, 223, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(231, 74, 59, 0.8)',
                    'rgba(54, 185, 204, 0.8)'
                ],
                borderColor: [
                    'rgba(246, 194, 62, 1)',
                    'rgba(78, 115, 223, 1)',
                    'rgba(40, 167, 69, 1)',
                    'rgba(231, 74, 59, 1)',
                    'rgba(54, 185, 204, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });

    // Refresh dashboard button
    document.getElementById('refreshDashboard').addEventListener('click', function() {
        location.reload();
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
