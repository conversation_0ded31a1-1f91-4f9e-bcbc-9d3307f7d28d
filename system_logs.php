<?php
/**
 * System Logs Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require admin role
requireRole(['admin']);

// Get filter parameters
$user = isset($_GET['user']) ? (int)$_GET['user'] : 0;
$action = isset($_GET['action']) ? sanitize($_GET['action']) : '';
$entity = isset($_GET['entity']) ? sanitize($_GET['entity']) : '';
$dateFrom = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : '';
$dateTo = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : '';

// Set default date range if not provided
if (empty($dateFrom)) {
    $dateFrom = date('Y-m-d', strtotime('-30 days'));
}

if (empty($dateTo)) {
    $dateTo = date('Y-m-d');
}

// Build query
$query = "
    SELECT sl.*, u.username, u.full_name
    FROM system_logs sl
    JOIN users u ON sl.user_id = u.id
    WHERE sl.created_at BETWEEN :date_from AND :date_to
";

$params = [
    ':date_from' => $dateFrom . ' 00:00:00',
    ':date_to' => $dateTo . ' 23:59:59'
];

if ($user > 0) {
    $query .= " AND sl.user_id = :user_id";
    $params[':user_id'] = $user;
}

if (!empty($action)) {
    $query .= " AND sl.action = :action";
    $params[':action'] = $action;
}

if (!empty($entity)) {
    $query .= " AND sl.entity_type = :entity_type";
    $params[':entity_type'] = $entity;
}

$query .= " ORDER BY sl.created_at DESC";

// Get users for filter
try {
    $userStmt = $pdo->query("SELECT id, username, full_name FROM users ORDER BY username ASC");
    $users = $userStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Users Error: " . $e->getMessage());
    $users = [];
}

// Get actions for filter
try {
    $actionStmt = $pdo->query("SELECT DISTINCT action FROM system_logs ORDER BY action ASC");
    $actions = $actionStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Actions Error: " . $e->getMessage());
    $actions = [];
}

// Get entity types for filter
try {
    $entityStmt = $pdo->query("SELECT DISTINCT entity_type FROM system_logs ORDER BY entity_type ASC");
    $entities = $entityStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Entity Types Error: " . $e->getMessage());
    $entities = [];
}

// Get logs
try {
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $logs = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Logs Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع سجلات النظام', 'alert alert-danger');
    $logs = [];
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-history me-2"></i> سجلات النظام
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <button class="btn btn-outline-secondary btn-print">
            <i class="fas fa-print me-1"></i> طباعة
        </button>
        <button class="btn btn-outline-success btn-export" data-table-target="#logsTable" data-filename="system_logs">
            <i class="fas fa-file-excel me-1"></i> تصدير
        </button>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="get" class="row g-3">
            <div class="col-md-3">
                <label for="user" class="form-label">المستخدم</label>
                <select class="form-select" id="user" name="user">
                    <option value="0">جميع المستخدمين</option>
                    <?php foreach ($users as $u): ?>
                        <option value="<?php echo $u['id']; ?>" <?php echo ($user == $u['id']) ? 'selected' : ''; ?>>
                            <?php echo $u['username']; ?> (<?php echo $u['full_name']; ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label for="action" class="form-label">النشاط</label>
                <select class="form-select" id="action" name="action">
                    <option value="">جميع الأنشطة</option>
                    <?php foreach ($actions as $a): ?>
                        <option value="<?php echo $a['action']; ?>" <?php echo ($action === $a['action']) ? 'selected' : ''; ?>>
                            <?php echo $a['action']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label for="entity" class="form-label">نوع الكيان</label>
                <select class="form-select" id="entity" name="entity">
                    <option value="">جميع الأنواع</option>
                    <?php foreach ($entities as $e): ?>
                        <option value="<?php echo $e['entity_type']; ?>" <?php echo ($entity === $e['entity_type']) ? 'selected' : ''; ?>>
                            <?php echo $e['entity_type']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $dateFrom; ?>">
            </div>
            <div class="col-md-2">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $dateTo; ?>">
            </div>
            <div class="col-md-1 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">تطبيق</button>
            </div>
        </form>
    </div>
</div>

<!-- Logs Table -->
<div class="card">
    <div class="card-body">
        <?php if (count($logs) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover" id="logsTable">
                    <thead>
                        <tr>
                            <th>التاريخ والوقت</th>
                            <th>المستخدم</th>
                            <th>النشاط</th>
                            <th>نوع الكيان</th>
                            <th>معرف الكيان</th>
                            <th>التفاصيل</th>
                            <th>عنوان IP</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log): ?>
                            <tr>
                                <td><?php echo date('d/m/Y H:i:s', strtotime($log['created_at'])); ?></td>
                                <td><?php echo $log['full_name']; ?> (<?php echo $log['username']; ?>)</td>
                                <td><?php echo $log['action']; ?></td>
                                <td><?php echo $log['entity_type']; ?></td>
                                <td><?php echo $log['entity_id'] ?: '-'; ?></td>
                                <td><?php echo $log['details'] ?: '-'; ?></td>
                                <td><?php echo $log['ip_address'] ?: '-'; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد سجلات للعرض
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
