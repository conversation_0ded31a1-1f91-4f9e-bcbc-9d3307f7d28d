<?php
/**
 * Delete Appreciation Letter Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include required files
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Require login and proper role
requireRole(['admin', 'hr']);

// Check if appreciation letter ID is provided
$letterId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($letterId <= 0) {
    flash('error_message', 'معرف كتاب الشكر غير صحيح', 'alert alert-danger');
    redirect('appreciation.php');
}

// Get appreciation letter details
try {
    $stmt = $pdo->prepare("
        SELECT a.*, e.full_name, e.next_allowance_date, e.next_promotion_date
        FROM appreciation_letters a
        JOIN employees e ON a.employee_id = e.id
        WHERE a.id = :id
    ");
    $stmt->execute([':id' => $letterId]);
    $letter = $stmt->fetch();
    
    if (!$letter) {
        flash('error_message', 'كتاب الشكر غير موجود', 'alert alert-danger');
        redirect('appreciation.php');
    }
} catch (PDOException $e) {
    error_log("Get Appreciation Letter Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات كتاب الشكر', 'alert alert-danger');
    redirect('appreciation.php');
}

// Process deletion
try {
    // Start transaction
    $pdo->beginTransaction();
    
    // Restore employee's next allowance and promotion dates
    // Calculate new dates by adding back the months reduction
    $nextAllowanceDate = new DateTime($letter['next_allowance_date']);
    $nextAllowanceDate->add(new DateInterval('P' . $letter['months_reduction'] . 'M'));
    
    $nextPromotionDate = new DateTime($letter['next_promotion_date']);
    $nextPromotionDate->add(new DateInterval('P' . $letter['months_reduction'] . 'M'));
    
    // Update employee record
    $updateStmt = $pdo->prepare("
        UPDATE employees SET
            next_allowance_date = :next_allowance_date,
            next_promotion_date = :next_promotion_date
        WHERE id = :id
    ");
    
    $updateStmt->execute([
        ':next_allowance_date' => $nextAllowanceDate->format('Y-m-d'),
        ':next_promotion_date' => $nextPromotionDate->format('Y-m-d'),
        ':id' => $letter['employee_id']
    ]);
    
    // Delete file if exists
    if ($letter['file_path'] && file_exists($letter['file_path'])) {
        @unlink($letter['file_path']);
    }
    
    // Delete appreciation letter record
    $deleteStmt = $pdo->prepare("DELETE FROM appreciation_letters WHERE id = :id");
    $deleteStmt->execute([':id' => $letterId]);
    
    // Log activity
    logActivity(
        $_SESSION['user_id'],
        'حذف كتاب شكر',
        'appreciation_letters',
        $letterId,
        'تم حذف كتاب شكر للموظف: ' . $letter['full_name']
    );
    
    // Commit transaction
    $pdo->commit();
    
    // Redirect to appreciation letters page
    flash('success_message', 'تم حذف كتاب الشكر بنجاح', 'alert alert-success');
    redirect('appreciation.php');
    
} catch (PDOException $e) {
    // Rollback transaction on error
    $pdo->rollBack();
    
    error_log("Delete Appreciation Letter Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء حذف كتاب الشكر', 'alert alert-danger');
    redirect('appreciation.php');
}
?>
