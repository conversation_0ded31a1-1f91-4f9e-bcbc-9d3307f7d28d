نظام إدارة العلاوات والترقية وكتب الشكر - نسخة محمولة
=================================================

تعليمات التشغيل:
--------------

1. لتشغيل النظام:
   - في Windows: انقر نقرًا مزدوجًا على ملف start_system.bat
   - في Linux/macOS: افتح Terminal وقم بتشغيل ./start_system.sh

2. سيتم فتح المتصفح تلقائيًا على صفحة النظام
   - إذا لم يفتح المتصفح تلقائيًا، افتح المتصفح يدويًا وانتقل إلى: http://localhost/ترقيات/

3. لإيقاف النظام:
   - في Windows: انقر نقرًا مزدوجًا على ملف stop_system.bat
   - في Linux/macOS: افتح Terminal وقم بتشغيل ./stop_system.sh

معلومات تسجيل الدخول:
-------------------
اسم المستخدم: admin
كلمة المرور: admin123

إذا نسيت كلمة المرور:
-------------------
1. قم بتشغيل النظام
2. انتقل إلى: http://localhost/ترقيات/reset_password.php
3. اتبع التعليمات لإعادة تعيين كلمة المرور

إنشاء قاعدة البيانات (للمرة الأولى فقط):
-------------------------------------
1. قم بتشغيل النظام
2. انتقل إلى: http://localhost/ترقيات/create_database.php
3. اتبع التعليمات لإنشاء قاعدة البيانات
4. انتقل إلى: http://localhost/ترقيات/create_user.php لإنشاء مستخدم جديد

ملاحظات هامة:
-----------
- تأكد من عدم تشغيل أي خدمات Apache أو MySQL أخرى على الجهاز قبل تشغيل النظام
- إذا واجهت مشكلة في الاتصال بقاعدة البيانات، انتقل إلى: http://localhost/ترقيات/test_connection.php
- إذا كنت بحاجة إلى إنشاء مستخدم جديد، انتقل إلى: http://localhost/ترقيات/create_user.php

متطلبات النظام:
------------
- نظام التشغيل: Windows 7/8/10/11, macOS 10.10+, أو أي توزيعة Linux حديثة
- المعالج: 1 GHz أو أعلى
- الذاكرة: 512 MB RAM (يوصى بـ 1 GB أو أكثر)
- مساحة التخزين: 500 MB من المساحة الحرة
- متصفح ويب حديث: Chrome, Firefox, Edge, Safari

حل المشكلات الشائعة:
-----------------

1. المنفذ 80 مشغول:
   - إذا ظهرت رسالة خطأ تفيد بأن المنفذ 80 مشغول، فهذا يعني أن هناك تطبيق آخر يستخدم هذا المنفذ
   - قم بإيقاف أي خدمات Apache أو IIS أخرى تعمل على الجهاز
   - أو قم بتعديل ملف xampp/apache/conf/httpd.conf وتغيير المنفذ من 80 إلى 8080

2. المنفذ 3306 مشغول:
   - إذا ظهرت رسالة خطأ تفيد بأن المنفذ 3306 مشغول، فهذا يعني أن هناك خدمة MySQL أخرى تعمل
   - قم بإيقاف أي خدمات MySQL أخرى تعمل على الجهاز
   - أو قم بتعديل ملف xampp/mysql/bin/my.ini وتغيير المنفذ من 3306 إلى 3307

3. مشكلة في الاتصال بقاعدة البيانات:
   - تأكد من تشغيل خدمة MySQL
   - انتقل إلى: http://localhost/ترقيات/test_connection.php
   - تأكد من وجود قاعدة البيانات employee_promotions_db
   - إذا لم تكن موجودة، انتقل إلى: http://localhost/ترقيات/create_database.php

4. مشكلة في تسجيل الدخول:
   - تأكد من استخدام اسم المستخدم وكلمة المرور الصحيحين
   - إذا نسيت كلمة المرور، انتقل إلى: http://localhost/ترقيات/reset_password.php
   - إذا لم يكن هناك مستخدمين، انتقل إلى: http://localhost/ترقيات/create_user.php

5. مشاكل في الواجهة الرسومية والقوائم:
   - إذا ظهرت مشاكل في الواجهة الرسومية أو القوائم بعد نقل النظام إلى حاسوب آخر
   - انتقل إلى: http://localhost/ترقيات/fix_ui.php
   - اتبع التعليمات لإصلاح مشاكل الواجهة الرسومية

6. مشاكل في المسارات والإعدادات:
   - إذا ظهرت مشاكل في المسارات أو الإعدادات بعد نقل النظام إلى حاسوب آخر
   - انتقل إلى: http://localhost/ترقيات/fix_paths.php
   - اتبع التعليمات لإصلاح المسارات والإعدادات

7. مشاكل في عرض النصوص العربية:
   - تأكد من أن ترميز الصفحة هو UTF-8
   - تأكد من أن اتجاه الصفحة هو RTL (من اليمين إلى اليسار)
   - انتقل إلى: http://localhost/ترقيات/fix_arabic.php لإصلاح مشاكل النصوص العربية

للمزيد من المعلومات والمساعدة، راجع ملف portable_setup.md
