<?php
/**
 * Backup and Restore Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and admin role
requireRole(['admin']);

// Create backup directory if it doesn't exist
$backupDir = 'backups';
if (!file_exists($backupDir)) {
    mkdir($backupDir, 0755, true);
}

// Process backup request
if (isset($_POST['create_backup'])) {
    try {
        // Generate backup filename with timestamp
        $timestamp = date('Y-m-d_H-i-s');
        $backupDir = 'backups';

        // Make sure backup directory exists
        if (!file_exists($backupDir)) {
            mkdir($backupDir, 0755, true);
        }

        // Create a temporary directory for backup files
        $tempBackupDir = $backupDir . '/temp_' . $timestamp;
        if (!file_exists($tempBackupDir)) {
            mkdir($tempBackupDir, 0755, true);
        }

        // Get database connection
        $pdo = $GLOBALS['pdo']; // Use existing PDO connection

        // Get all tables
        $tables = [];
        $stmt = $pdo->query("SHOW TABLES");
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $tables[] = $row[0];
        }

        // Create a simple text file with backup info
        $infoContent = "نظام إدارة العلاوات والترفيع وكتب الشكر - نسخة احتياطية\n";
        $infoContent .= "تاريخ النسخ الاحتياطي: " . date('Y-m-d H:i:s') . "\n";
        $infoContent .= "قاعدة البيانات: " . DB_NAME . "\n";
        $infoContent .= "عدد الجداول: " . count($tables) . "\n";
        $infoContent .= "الجداول: " . implode(", ", $tables) . "\n";

        file_put_contents($tempBackupDir . '/backup_info.txt', $infoContent);

        // Export each table structure as SQL
        $structureSQL = "-- نظام إدارة العلاوات والترفيع وكتب الشكر - هيكل الجداول\n";
        $structureSQL .= "-- تاريخ النسخ الاحتياطي: " . date('Y-m-d H:i:s') . "\n\n";
        $structureSQL .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

        foreach ($tables as $table) {
            // Get table structure
            $stmt = $pdo->query("SHOW CREATE TABLE `$table`");
            $row = $stmt->fetch(PDO::FETCH_NUM);
            $structureSQL .= "DROP TABLE IF EXISTS `$table`;\n";
            $structureSQL .= $row[1] . ";\n\n";
        }

        $structureSQL .= "SET FOREIGN_KEY_CHECKS=1;\n";

        file_put_contents($tempBackupDir . '/table_structure.sql', $structureSQL);

        // Export each table data as CSV
        foreach ($tables as $table) {
            // Get column names
            $stmt = $pdo->query("SHOW COLUMNS FROM `$table`");
            $columns = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $columns[] = $row['Field'];
            }

            if (empty($columns)) {
                continue; // Skip if no columns
            }

            // Create CSV file for this table
            $csvFile = fopen($tempBackupDir . '/' . $table . '.csv', 'w');

            // Write header row with column names
            fputcsv($csvFile, $columns);

            // Get data in batches to avoid memory issues
            $batchSize = 1000;
            $offset = 0;

            do {
                $stmt = $pdo->query("SELECT * FROM `$table` LIMIT $offset, $batchSize");
                $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $count = count($rows);

                // Write data rows
                foreach ($rows as $row) {
                    fputcsv($csvFile, $row);
                }

                $offset += $batchSize;
            } while ($count > 0);

            fclose($csvFile);
        }

        // Create a simple text file with import instructions
        $instructionsContent = "تعليمات استعادة النسخة الاحتياطية:\n\n";
        $instructionsContent .= "1. قم بفك ضغط ملف النسخة الاحتياطية\n";
        $instructionsContent .= "2. قم باستيراد ملف هيكل الجداول (table_structure.sql) إلى قاعدة البيانات\n";
        $instructionsContent .= "3. قم باستيراد ملفات البيانات (*.csv) إلى الجداول المناسبة\n";

        file_put_contents($tempBackupDir . '/instructions.txt', $instructionsContent);

        // Create a zip file of all the backup files
        $zipFile = $backupDir . '/backup_' . $timestamp . '.zip';
        $zip = new ZipArchive();

        if ($zip->open($zipFile, ZipArchive::CREATE) === TRUE) {
            // Add all files from the temp directory
            $files = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($tempBackupDir),
                RecursiveIteratorIterator::LEAVES_ONLY
            );

            foreach ($files as $file) {
                if (!$file->isDir()) {
                    $filePath = $file->getRealPath();
                    $relativePath = substr($filePath, strlen($tempBackupDir) + 1);

                    $zip->addFile($filePath, $relativePath);
                }
            }

            $zip->close();

            // Remove the temporary directory
            deleteDirectory($tempBackupDir);

            // Log activity
            logActivity(
                $_SESSION['user_id'],
                'إنشاء نسخة احتياطية',
                'backup',
                0,
                'تم إنشاء نسخة احتياطية: ' . basename($zipFile)
            );

            flash('success_message', 'تم إنشاء النسخة الاحتياطية بنجاح', 'alert alert-success');
        } else {
            flash('error_message', 'حدث خطأ أثناء إنشاء ملف الضغط', 'alert alert-danger');
        }
    } catch (Exception $e) {
        error_log("Backup Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء إنشاء النسخة الاحتياطية: ' . $e->getMessage(), 'alert alert-danger');

        // Clean up any temporary files
        if (isset($tempBackupDir) && file_exists($tempBackupDir)) {
            deleteDirectory($tempBackupDir);
        }
    }
}

// Process restore request
if (isset($_POST['restore_backup']) && isset($_POST['backup_file'])) {
    try {
        $backupFile = sanitize($_POST['backup_file']);
        $fullPath = $backupDir . '/' . $backupFile;

        // Create a temporary directory for extraction
        $timestamp = date('Y-m-d_H-i-s');
        $extractDir = $backupDir . '/extract_' . $timestamp;
        if (!file_exists($extractDir)) {
            mkdir($extractDir, 0755, true);
        }

        if (file_exists($fullPath) && pathinfo($fullPath, PATHINFO_EXTENSION) === 'zip') {
            // Extract the zip file
            $zip = new ZipArchive();
            if ($zip->open($fullPath) === TRUE) {
                $zip->extractTo($extractDir);
                $zip->close();

                // Check if table structure file exists
                if (file_exists($extractDir . '/table_structure.sql')) {
                    // Get database connection
                    $pdo = $GLOBALS['pdo']; // Use existing PDO connection

                    try {
                        // Begin transaction
                        $pdo->beginTransaction();

                        // Import table structure
                        $structureSQL = file_get_contents($extractDir . '/table_structure.sql');

                        // Split SQL file into individual queries
                        $queries = preg_split('/;\s*$/m', $structureSQL);

                        // Execute each query to create tables
                        foreach ($queries as $query) {
                            $query = trim($query);
                            if (!empty($query)) {
                                $pdo->exec($query);
                            }
                        }

                        // Get all CSV files (table data)
                        $csvFiles = glob($extractDir . '/*.csv');

                        foreach ($csvFiles as $csvFile) {
                            $tableName = pathinfo($csvFile, PATHINFO_FILENAME);

                            // Skip if not a valid table name
                            if (!preg_match('/^[a-zA-Z0-9_]+$/', $tableName)) {
                                continue;
                            }

                            // Open CSV file
                            $file = fopen($csvFile, 'r');
                            if ($file) {
                                // Read header row (column names)
                                $columns = fgetcsv($file);

                                if ($columns && count($columns) > 0) {
                                    // Prepare column list for SQL
                                    $columnList = '`' . implode('`, `', $columns) . '`';

                                    // Prepare placeholders for values
                                    $placeholders = [];
                                    for ($i = 0; $i < count($columns); $i++) {
                                        $placeholders[] = '?';
                                    }
                                    $placeholderStr = '(' . implode(', ', $placeholders) . ')';

                                    // Prepare insert statement
                                    $stmt = $pdo->prepare("INSERT INTO `$tableName` ($columnList) VALUES $placeholderStr");

                                    // Read and insert data rows in batches
                                    $batchSize = 100;
                                    $batch = [];

                                    while (($row = fgetcsv($file)) !== FALSE) {
                                        // Execute insert for this row
                                        $stmt->execute($row);
                                    }
                                }

                                fclose($file);
                            }
                        }

                        // Commit transaction
                        $pdo->commit();

                        // Log activity
                        logActivity(
                            $_SESSION['user_id'],
                            'استعادة نسخة احتياطية',
                            'backup',
                            0,
                            'تم استعادة النسخة الاحتياطية: ' . $backupFile
                        );

                        flash('success_message', 'تم استعادة النسخة الاحتياطية بنجاح', 'alert alert-success');

                    } catch (PDOException $e) {
                        // Rollback transaction on error
                        if ($pdo->inTransaction()) {
                            $pdo->rollBack();
                        }

                        error_log("Restore Error: " . $e->getMessage());
                        flash('error_message', 'حدث خطأ أثناء استعادة النسخة الاحتياطية: ' . $e->getMessage(), 'alert alert-danger');
                    }
                } else {
                    flash('error_message', 'لم يتم العثور على ملف هيكل الجداول في النسخة الاحتياطية', 'alert alert-danger');
                }
            } else {
                flash('error_message', 'حدث خطأ أثناء فك ضغط النسخة الاحتياطية', 'alert alert-danger');
            }
        } else {
            flash('error_message', 'ملف النسخة الاحتياطية غير موجود أو غير صالح', 'alert alert-danger');
        }

        // Clean up extracted files
        if (file_exists($extractDir)) {
            deleteDirectory($extractDir);
        }
    } catch (Exception $e) {
        error_log("Restore Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء استعادة النسخة الاحتياطية: ' . $e->getMessage(), 'alert alert-danger');

        // Clean up extracted files
        if (isset($extractDir) && file_exists($extractDir)) {
            deleteDirectory($extractDir);
        }
    }
}

// Process delete backup request
if (isset($_POST['delete_backup']) && isset($_POST['backup_file'])) {
    try {
        $backupFile = sanitize($_POST['backup_file']);
        $fullPath = $backupDir . '/' . $backupFile;

        if (file_exists($fullPath)) {
            unlink($fullPath);

            // Log activity
            logActivity(
                $_SESSION['user_id'],
                'حذف نسخة احتياطية',
                'backup',
                0,
                'تم حذف النسخة الاحتياطية: ' . $backupFile
            );

            flash('success_message', 'تم حذف النسخة الاحتياطية بنجاح', 'alert alert-success');
        } else {
            flash('error_message', 'ملف النسخة الاحتياطية غير موجود', 'alert alert-danger');
        }
    } catch (Exception $e) {
        error_log("Delete Backup Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء حذف النسخة الاحتياطية: ' . $e->getMessage(), 'alert alert-danger');
    }
}

// Get list of backup files
$backupFiles = [];
if (file_exists($backupDir)) {
    foreach (glob($backupDir . '/*.zip') as $file) {
        $backupFiles[] = [
            'name' => basename($file),
            'size' => filesize($file),
            'date' => filemtime($file)
        ];
    }

    // Sort by date (newest first)
    usort($backupFiles, function($a, $b) {
        return $b['date'] - $a['date'];
    });
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-database me-2"></i> النسخ الاحتياطي واستعادة البيانات
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <button type="button" class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#backupInfoModal">
            <i class="fas fa-info-circle me-1"></i> معلومات حول النسخ الاحتياطي
        </button>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى الرئيسية
        </a>
    </div>
</div>

<!-- Backup Info Modal -->
<div class="modal fade" id="backupInfoModal" tabindex="-1" aria-labelledby="backupInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="backupInfoModalLabel">
                    <i class="fas fa-info-circle me-2"></i> معلومات حول النسخ الاحتياطي
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <h5><i class="fas fa-lightbulb me-2"></i> نصائح هامة للنسخ الاحتياطي</h5>
                    <ul class="mb-0">
                        <li>قم بإنشاء نسخة احتياطية بشكل دوري (يومياً أو أسبوعياً) للحفاظ على بياناتك.</li>
                        <li>احتفظ بنسخ احتياطية متعددة وليس فقط النسخة الأخيرة.</li>
                        <li>قم بتنزيل النسخ الاحتياطية وحفظها في مكان آمن خارج الخادم.</li>
                        <li>تأكد من اختبار استعادة النسخ الاحتياطية بشكل دوري للتأكد من صلاحيتها.</li>
                    </ul>
                </div>

                <h5 class="mt-4"><i class="fas fa-download me-2"></i> إنشاء نسخة احتياطية</h5>
                <p>عند إنشاء نسخة احتياطية، يقوم النظام بما يلي:</p>
                <ol>
                    <li>استخراج هيكل جميع الجداول من قاعدة البيانات وحفظه في ملف SQL.</li>
                    <li>استخراج البيانات من كل جدول وتخزينها في ملفات CSV منفصلة.</li>
                    <li>إنشاء ملف معلومات يحتوي على تفاصيل النسخة الاحتياطية.</li>
                    <li>إنشاء ملف تعليمات لاستعادة النسخة الاحتياطية.</li>
                    <li>ضغط جميع الملفات بتنسيق ZIP لتوفير المساحة.</li>
                    <li>حفظ الملف المضغوط في مجلد النسخ الاحتياطية.</li>
                </ol>

                <h5 class="mt-4"><i class="fas fa-upload me-2"></i> استعادة نسخة احتياطية</h5>
                <p>عند استعادة نسخة احتياطية، يقوم النظام بما يلي:</p>
                <ol>
                    <li>فك ضغط ملف النسخة الاحتياطية.</li>
                    <li>استيراد ملف هيكل الجداول (SQL) لإعادة إنشاء هيكل قاعدة البيانات.</li>
                    <li>استيراد البيانات من ملفات CSV إلى الجداول المناسبة.</li>
                    <li>حذف الملفات المؤقتة بعد الانتهاء.</li>
                </ol>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> استعادة النسخة الاحتياطية ستؤدي إلى حذف جميع البيانات الحالية واستبدالها بالبيانات من النسخة الاحتياطية.
                </div>

                <!-- Progress bar for restore (hidden by default) -->
                <div class="progress mt-3" id="restoreProgress" style="display: none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" role="progressbar" style="width: 0%"></div>
                </div>
                <small class="text-muted mt-2" id="restoreStatus" style="display: none;">جاري استعادة النسخة الاحتياطية...</small>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> تم تحسين نظام النسخ الاحتياطي ليعمل بشكل أفضل على جميع أنواع الخوادم. يتم الآن تخزين هيكل الجداول في ملف SQL والبيانات في ملفات CSV، مما يجعل عملية النسخ الاحتياطي والاستعادة أكثر موثوقية وتوافقاً مع مختلف البيئات.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Create Backup Card -->
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2"></i> إنشاء نسخة احتياطية
                </h5>
            </div>
            <div class="card-body">
                <p>قم بإنشاء نسخة احتياطية من قاعدة البيانات الحالية. يمكنك استخدام هذه النسخة لاستعادة البيانات في حالة حدوث مشكلة.</p>
                <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" id="backupForm">
                    <button type="submit" name="create_backup" class="btn btn-primary w-100" id="createBackupBtn">
                        <i class="fas fa-download me-1"></i> إنشاء نسخة احتياطية
                    </button>
                </form>

                <!-- Progress bar (hidden by default) -->
                <div class="progress mt-3" id="backupProgress" style="display: none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary" role="progressbar" style="width: 0%"></div>
                </div>
                <small class="text-muted mt-2" id="backupStatus" style="display: none;">جاري إنشاء النسخة الاحتياطية...</small>
            </div>
        </div>
    </div>

    <!-- Schedule Backup Card -->
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i> جدولة النسخ الاحتياطي
                </h5>
            </div>
            <div class="card-body">
                <p>قم بإعداد جدولة للنسخ الاحتياطي التلقائي. سيقوم النظام بإنشاء نسخة احتياطية بشكل دوري حسب الجدول المحدد.</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لإعداد النسخ الاحتياطي التلقائي، يرجى الاتصال بمسؤول النظام لإعداد مهمة Cron Job على الخادم.
                </div>
            </div>
        </div>
    </div>

    <!-- Restore Backup Card -->
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-upload me-2"></i> استعادة نسخة احتياطية
                </h5>
            </div>
            <div class="card-body">
                <p>قم باستعادة قاعدة البيانات من نسخة احتياطية سابقة. سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> استعادة النسخة الاحتياطية ستؤدي إلى حذف جميع البيانات الحالية واستبدالها بالبيانات من النسخة الاحتياطية.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Backup Files List -->
<div class="card mb-4">
    <div class="card-header bg-secondary text-white">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i> النسخ الاحتياطية المتوفرة
        </h5>
    </div>
    <div class="card-body">
        <?php if (count($backupFiles) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم الملف</th>
                            <th>تاريخ الإنشاء</th>
                            <th>حجم الملف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($backupFiles as $file): ?>
                            <tr>
                                <td><?php echo $file['name']; ?></td>
                                <td><?php echo date('Y-m-d H:i:s', $file['date']); ?></td>
                                <td><?php echo formatFileSize($file['size']); ?></td>
                                <td>
                                    <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" class="d-inline" name="restore_form">
                                        <input type="hidden" name="backup_file" value="<?php echo $file['name']; ?>">
                                        <button type="submit" name="restore_backup" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="استعادة">
                                            <i class="fas fa-upload"></i>
                                        </button>
                                    </form>
                                    <a href="<?php echo $backupDir . '/' . $file['name']; ?>" class="btn btn-sm btn-info" download data-bs-toggle="tooltip" title="تنزيل">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" class="d-inline" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذه النسخة الاحتياطية؟')">
                                        <input type="hidden" name="backup_file" value="<?php echo $file['name']; ?>">
                                        <button type="submit" name="delete_backup" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد نسخ احتياطية متوفرة.
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
/**
 * Format file size to human-readable format
 *
 * @param int $bytes File size in bytes
 * @return string Formatted file size
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $i = 0;
    while ($bytes >= 1024 && $i < count($units) - 1) {
        $bytes /= 1024;
        $i++;
    }
    return round($bytes, 2) . ' ' . $units[$i];
}

/**
 * Delete a directory and all its contents recursively
 *
 * @param string $dir Directory path
 * @return bool True on success, false on failure
 */
function deleteDirectory($dir) {
    if (!file_exists($dir)) {
        return true;
    }

    if (!is_dir($dir)) {
        return unlink($dir);
    }

    foreach (scandir($dir) as $item) {
        if ($item == '.' || $item == '..') {
            continue;
        }

        if (!deleteDirectory($dir . DIRECTORY_SEPARATOR . $item)) {
            return false;
        }
    }

    return rmdir($dir);
}

// Include footer
require_once 'includes/footer.php';
?>

<script>
// Backup and restore progress simulation
document.addEventListener('DOMContentLoaded', function() {
    // Backup form elements
    const backupForm = document.getElementById('backupForm');
    const createBackupBtn = document.getElementById('createBackupBtn');
    const backupProgress = document.getElementById('backupProgress');
    const backupProgressBar = backupProgress.querySelector('.progress-bar');
    const backupStatus = document.getElementById('backupStatus');

    // Restore progress elements
    const restoreForms = document.querySelectorAll('form[name="restore_form"]');
    const restoreProgress = document.getElementById('restoreProgress');
    const restoreProgressBar = restoreProgress.querySelector('.progress-bar');
    const restoreStatus = document.getElementById('restoreStatus');

    // Backup form submission
    if (backupForm) {
        backupForm.addEventListener('submit', function(e) {
            // Show progress bar and disable button
            backupProgress.style.display = 'flex';
            backupStatus.style.display = 'block';
            createBackupBtn.disabled = true;
            createBackupBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري إنشاء النسخة الاحتياطية...';

            // Simulate progress (this is just visual feedback, the actual backup happens on the server)
            let progress = 0;
            const interval = setInterval(function() {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;

                backupProgressBar.style.width = progress + '%';
                backupProgressBar.setAttribute('aria-valuenow', progress);

                if (progress < 20) {
                    backupStatus.textContent = 'جاري استخراج هيكل الجداول...';
                } else if (progress < 40) {
                    backupStatus.textContent = 'جاري إنشاء ملف SQL لهيكل الجداول...';
                } else if (progress < 60) {
                    backupStatus.textContent = 'جاري استخراج البيانات وتخزينها في ملفات CSV...';
                } else if (progress < 80) {
                    backupStatus.textContent = 'جاري إنشاء ملفات المعلومات والتعليمات...';
                } else if (progress < 90) {
                    backupStatus.textContent = 'جاري ضغط الملفات...';
                } else {
                    backupStatus.textContent = 'جاري حفظ النسخة الاحتياطية...';
                }

                if (progress === 100) {
                    clearInterval(interval);
                    // Form will submit normally and page will refresh
                }
            }, 500);
        });
    }

    // Restore form submission
    restoreForms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            // Show confirmation dialog
            if (!confirm('هل أنت متأكد من رغبتك في استعادة هذه النسخة الاحتياطية؟ سيتم حذف جميع البيانات الحالية.')) {
                e.preventDefault();
                return false;
            }

            // Show progress bar
            restoreProgress.style.display = 'flex';
            restoreStatus.style.display = 'block';

            // Disable all restore buttons
            document.querySelectorAll('button[name="restore_backup"]').forEach(function(btn) {
                btn.disabled = true;
            });

            // Change clicked button to show spinner
            const restoreBtn = form.querySelector('button[name="restore_backup"]');
            restoreBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';

            // Simulate progress
            let progress = 0;
            const interval = setInterval(function() {
                progress += Math.random() * 10;
                if (progress > 100) progress = 100;

                restoreProgressBar.style.width = progress + '%';
                restoreProgressBar.setAttribute('aria-valuenow', progress);

                if (progress < 20) {
                    restoreStatus.textContent = 'جاري فك ضغط ملف النسخة الاحتياطية...';
                } else if (progress < 40) {
                    restoreStatus.textContent = 'جاري قراءة ملفات النسخة الاحتياطية...';
                } else if (progress < 60) {
                    restoreStatus.textContent = 'جاري استيراد هيكل الجداول من ملف SQL...';
                } else if (progress < 90) {
                    restoreStatus.textContent = 'جاري استيراد البيانات من ملفات CSV...';
                } else {
                    restoreStatus.textContent = 'جاري إكمال عملية الاستعادة...';
                }

                if (progress === 100) {
                    clearInterval(interval);
                    // Form will submit normally and page will refresh
                }
            }, 500);
        });
    });

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
