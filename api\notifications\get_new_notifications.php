<?php
/**
 * API: Get New Notifications
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Define root path
$root_path = dirname(dirname(__DIR__));

// Include required files
require_once $root_path . '/config/config.php';
require_once $root_path . '/config/database.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// Require login
requireLogin();

// Set content type to JSON
header('Content-Type: application/json');

// Get last check timestamp from request
$lastCheck = isset($_GET['last_check']) ? sanitize($_GET['last_check']) : date('Y-m-d H:i:s', strtotime('-1 minute'));

try {
    // Check if notifications table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'notifications'");

    if ($tableCheck->rowCount() > 0) {
        // Get new notifications for current user
        $stmt = $pdo->prepare("
            SELECT id, type, title, message, link, read, created_at
            FROM notifications
            WHERE user_id = :user_id
            AND created_at > :last_check
            AND read = 0
            ORDER BY created_at DESC
        ");

        $stmt->execute([
            ':user_id' => $_SESSION['user_id'],
            ':last_check' => $lastCheck
        ]);

        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        // Return empty array if table doesn't exist
        $notifications = [];
    }

    // Return JSON response
    echo json_encode([
        'success' => true,
        'notifications' => $notifications,
        'last_check' => date('Y-m-d H:i:s')
    ]);
} catch (PDOException $e) {
    // Log error
    error_log("API Error (get_new_notifications): " . $e->getMessage());

    // Return empty array instead of error
    echo json_encode([
        'success' => true,
        'notifications' => [],
        'last_check' => date('Y-m-d H:i:s')
    ]);
}
?>
