# دليل إعداد نسخة محمولة من نظام إدارة العلاوات والترقية

هذا الدليل يشرح كيفية إعداد نسخة محمولة من النظام يمكن تشغيلها على أي جهاز كمبيوتر بغض النظر عن المواصفات.

## الخطوة 1: تحميل XAMPP المحمول

1. قم بتحميل [XAMPP Portable](https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/) المناسب لنظام التشغيل لديك:
   - للويندوز: XAMPP Windows
   - للماك: XAMPP macOS
   - للينكس: XAMPP Linux

2. اختر إصدار XAMPP الذي يحتوي على PHP 7.4 أو أحدث (مثل `xampp-portable-windows-x64-7.4.33-0-VC15.zip`)

## الخطوة 2: إعد<PERSON> XAMPP المحمول

1. قم بإنشاء مجلد جديد على وسيط التخزين المحمول (مثل فلاش USB) باسم `portable_system`
2. استخرج محتويات ملف XAMPP الذي قمت بتحميله إلى المجلد `portable_system`
3. انسخ مجلد النظام بالكامل إلى المسار `portable_system\xampp\htdocs\ترقيات`

## الخطوة 3: إنشاء ملفات التشغيل

### لنظام Windows

قم بإنشاء ملف `start_system.bat` في المجلد `portable_system` بالمحتوى التالي:

```batch
@echo off
echo Starting XAMPP services...
cd xampp
start /B xampp-control.exe
echo.
echo Waiting for services to start...
timeout /t 10 /nobreak > nul
echo.
echo Opening system in browser...
start http://localhost/ترقيات/
echo.
echo To stop the system, run stop_system.bat
echo.
```

قم بإنشاء ملف `stop_system.bat` في المجلد `portable_system` بالمحتوى التالي:

```batch
@echo off
echo Stopping XAMPP services...
cd xampp
xampp_stop.exe
echo.
echo XAMPP services stopped.
echo.
pause
```

### لنظام Linux/macOS

قم بإنشاء ملف `start_system.sh` في المجلد `portable_system` بالمحتوى التالي:

```bash
#!/bin/bash
echo "Starting XAMPP services..."
cd xampp
./xampp start
echo ""
echo "Waiting for services to start..."
sleep 10
echo ""
echo "Opening system in browser..."
xdg-open http://localhost/ترقيات/ || open http://localhost/ترقيات/
echo ""
echo "To stop the system, run stop_system.sh"
echo ""
```

قم بإنشاء ملف `stop_system.sh` في المجلد `portable_system` بالمحتوى التالي:

```bash
#!/bin/bash
echo "Stopping XAMPP services..."
cd xampp
./xampp stop
echo ""
echo "XAMPP services stopped."
echo ""
read -p "Press Enter to continue..."
```

قم بتعيين صلاحيات التنفيذ للملفات:

```bash
chmod +x start_system.sh
chmod +x stop_system.sh
```

## الخطوة 4: إعداد قاعدة البيانات

1. قم بتشغيل النظام باستخدام ملف `start_system.bat` (Windows) أو `start_system.sh` (Linux/macOS)
2. افتح المتصفح وانتقل إلى `http://localhost/ترقيات/create_database.php`
3. اتبع التعليمات لإنشاء قاعدة البيانات
4. انتقل إلى `http://localhost/ترقيات/create_user.php` لإنشاء مستخدم جديد

## الخطوة 5: إنشاء ملف تعليمات للمستخدمين

قم بإنشاء ملف `README.txt` في المجلد `portable_system` بالمحتوى التالي:

```
نظام إدارة العلاوات والترقية وكتب الشكر - نسخة محمولة
=================================================

تعليمات التشغيل:
--------------

1. لتشغيل النظام:
   - في Windows: انقر نقرًا مزدوجًا على ملف start_system.bat
   - في Linux/macOS: افتح Terminal وقم بتشغيل ./start_system.sh

2. سيتم فتح المتصفح تلقائيًا على صفحة النظام
   - إذا لم يفتح المتصفح تلقائيًا، افتح المتصفح يدويًا وانتقل إلى: http://localhost/ترقيات/

3. لإيقاف النظام:
   - في Windows: انقر نقرًا مزدوجًا على ملف stop_system.bat
   - في Linux/macOS: افتح Terminal وقم بتشغيل ./stop_system.sh

معلومات تسجيل الدخول:
-------------------
اسم المستخدم: admin
كلمة المرور: admin123

إذا نسيت كلمة المرور:
-------------------
1. قم بتشغيل النظام
2. انتقل إلى: http://localhost/ترقيات/reset_password.php
3. اتبع التعليمات لإعادة تعيين كلمة المرور

ملاحظات هامة:
-----------
- تأكد من عدم تشغيل أي خدمات Apache أو MySQL أخرى على الجهاز قبل تشغيل النظام
- إذا واجهت مشكلة في الاتصال بقاعدة البيانات، انتقل إلى: http://localhost/ترقيات/test_connection.php
- إذا كنت بحاجة إلى إنشاء مستخدم جديد، انتقل إلى: http://localhost/ترقيات/create_user.php
```

## الخطوة 6: إنشاء نسخة احتياطية من قاعدة البيانات

لضمان إمكانية استعادة البيانات في حالة حدوث مشكلة:

1. قم بتشغيل النظام
2. انتقل إلى `http://localhost/phpmyadmin`
3. حدد قاعدة البيانات `employee_promotions_db`
4. انقر على "تصدير"
5. اختر "سريع" واضغط على "تنفيذ"
6. احفظ ملف SQL الناتج في مجلد `portable_system\database_backup`

## الخطوة 7: اختبار النظام المحمول

1. قم بإيقاف تشغيل النظام باستخدام ملف `stop_system.bat` أو `stop_system.sh`
2. افصل وسيط التخزين المحمول
3. قم بتوصيل وسيط التخزين المحمول بجهاز كمبيوتر آخر
4. قم بتشغيل النظام باستخدام ملف `start_system.bat` أو `start_system.sh`
5. تأكد من أن النظام يعمل بشكل صحيح

## متطلبات النظام الدنيا

- نظام التشغيل: Windows 7/8/10/11, macOS 10.10+, أو أي توزيعة Linux حديثة
- المعالج: 1 GHz أو أعلى
- الذاكرة: 512 MB RAM (يوصى بـ 1 GB أو أكثر)
- مساحة التخزين: 500 MB من المساحة الحرة
- متصفح ويب حديث: Chrome, Firefox, Edge, Safari

## حل المشكلات الشائعة

### المنفذ 80 مشغول

إذا كان المنفذ 80 مشغولاً بواسطة تطبيق آخر:

1. افتح ملف `portable_system\xampp\apache\conf\httpd.conf`
2. ابحث عن `Listen 80` وغيرها إلى `Listen 8080`
3. افتح ملف `portable_system\xampp\apache\conf\extra\httpd-ssl.conf`
4. ابحث عن `Listen 443` وغيرها إلى `Listen 8443`
5. عدل ملفات `start_system.bat` و `start_system.sh` لتستخدم المنفذ الجديد في عنوان URL
