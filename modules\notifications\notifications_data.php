<?php
/**
 * Notifications Data Module
 * وحدة بيانات التنبيهات
 */

// Initialize notifications array
$notifications = [];

try {
    // Check if notifications table exists
    $notificationsTableCheck = $pdo->query("SHOW TABLES LIKE 'notifications'");
    
    if ($notificationsTableCheck->rowCount() > 0) {
        // Get notifications for current user
        $stmt = $pdo->prepare("
            SELECT n.*, u.full_name as sender_name
            FROM notifications n
            LEFT JOIN users u ON n.sender_id = u.id
            WHERE n.user_id = :user_id OR n.user_id IS NULL
            ORDER BY n.is_read ASC, n.created_at DESC
            LIMIT 50
        ");
        $stmt->execute([':user_id' => $_SESSION['user_id']]);
        $notifications = $stmt->fetchAll();
    } else {
        // Create dummy notifications for demonstration
        $notifications = [
            [
                'id' => 1,
                'title' => 'موظف جديد مستحق للعلاوة',
                'message' => 'الموظف أحمد محمد أصبح مستحقاً للعلاوة اعتباراً من اليوم',
                'type' => 'allowance',
                'is_read' => 0,
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours')),
                'sender_name' => 'النظام'
            ],
            [
                'id' => 2,
                'title' => 'موظف مستحق للترفيع',
                'message' => 'الموظفة فاطمة علي مستحقة للترفيع من الدرجة 5 إلى الدرجة 6',
                'type' => 'promotion',
                'is_read' => 0,
                'created_at' => date('Y-m-d H:i:s', strtotime('-4 hours')),
                'sender_name' => 'النظام'
            ],
            [
                'id' => 3,
                'title' => 'كتاب شكر جديد',
                'message' => 'تم إصدار كتاب شكر للموظف محمد أحمد من قسم تكنولوجيا المعلومات',
                'type' => 'appreciation',
                'is_read' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'sender_name' => 'إدارة الموارد البشرية'
            ],
            [
                'id' => 4,
                'title' => 'تذكير: موظف مقبل على التقاعد',
                'message' => 'الموظف عبد الله سالم سيصل لسن التقاعد خلال 6 أشهر',
                'type' => 'retirement',
                'is_read' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'sender_name' => 'النظام'
            ],
            [
                'id' => 5,
                'title' => 'تحديث بيانات موظف',
                'message' => 'تم تحديث بيانات الموظف سارة محمد بنجاح',
                'type' => 'update',
                'is_read' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'sender_name' => 'مدير النظام'
            ]
        ];
    }

    // Get notification statistics
    $unreadCount = 0;
    $todayCount = 0;
    $weekCount = 0;
    $typeStats = [
        'allowance' => 0,
        'promotion' => 0,
        'appreciation' => 0,
        'retirement' => 0,
        'update' => 0,
        'system' => 0
    ];

    foreach ($notifications as $notification) {
        // Count unread
        if (!$notification['is_read']) {
            $unreadCount++;
        }

        // Count today's notifications
        if (date('Y-m-d', strtotime($notification['created_at'])) === date('Y-m-d')) {
            $todayCount++;
        }

        // Count this week's notifications
        if (strtotime($notification['created_at']) >= strtotime('-7 days')) {
            $weekCount++;
        }

        // Count by type
        $type = $notification['type'] ?? 'system';
        if (isset($typeStats[$type])) {
            $typeStats[$type]++;
        } else {
            $typeStats['system']++;
        }
    }

} catch (PDOException $e) {
    error_log("Get Notifications Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع التنبيهات', 'alert alert-danger');
    $notifications = [];
    $unreadCount = 0;
    $todayCount = 0;
    $weekCount = 0;
    $typeStats = [];
}

// Handle notification actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = sanitize($_POST['action']);
    
    try {
        switch ($action) {
            case 'mark_read':
                if (isset($_POST['notification_id'])) {
                    $notificationId = (int)$_POST['notification_id'];
                    
                    // Check if notifications table exists
                    $tableCheck = $pdo->query("SHOW TABLES LIKE 'notifications'");
                    if ($tableCheck->rowCount() > 0) {
                        $stmt = $pdo->prepare("
                            UPDATE notifications 
                            SET is_read = 1, read_at = NOW() 
                            WHERE id = :id AND user_id = :user_id
                        ");
                        $stmt->execute([
                            ':id' => $notificationId,
                            ':user_id' => $_SESSION['user_id']
                        ]);
                        
                        flash('success_message', 'تم تحديد التنبيه كمقروء', 'alert alert-success');
                    }
                }
                break;
                
            case 'mark_all_read':
                // Check if notifications table exists
                $tableCheck = $pdo->query("SHOW TABLES LIKE 'notifications'");
                if ($tableCheck->rowCount() > 0) {
                    $stmt = $pdo->prepare("
                        UPDATE notifications 
                        SET is_read = 1, read_at = NOW() 
                        WHERE user_id = :user_id AND is_read = 0
                    ");
                    $stmt->execute([':user_id' => $_SESSION['user_id']]);
                    
                    flash('success_message', 'تم تحديد جميع التنبيهات كمقروءة', 'alert alert-success');
                }
                break;
                
            case 'delete':
                if (isset($_POST['notification_id'])) {
                    $notificationId = (int)$_POST['notification_id'];
                    
                    // Check if notifications table exists
                    $tableCheck = $pdo->query("SHOW TABLES LIKE 'notifications'");
                    if ($tableCheck->rowCount() > 0) {
                        $stmt = $pdo->prepare("
                            DELETE FROM notifications 
                            WHERE id = :id AND user_id = :user_id
                        ");
                        $stmt->execute([
                            ':id' => $notificationId,
                            ':user_id' => $_SESSION['user_id']
                        ]);
                        
                        flash('success_message', 'تم حذف التنبيه بنجاح', 'alert alert-success');
                    }
                }
                break;
                
            case 'create':
                if (hasRole(['admin', 'hr_manager'])) {
                    $title = sanitize($_POST['title'] ?? '');
                    $message = sanitize($_POST['message'] ?? '');
                    $type = sanitize($_POST['type'] ?? 'system');
                    $targetUser = isset($_POST['target_user']) ? (int)$_POST['target_user'] : null;
                    
                    if (!empty($title) && !empty($message)) {
                        // Check if notifications table exists, create if not
                        $tableCheck = $pdo->query("SHOW TABLES LIKE 'notifications'");
                        if ($tableCheck->rowCount() == 0) {
                            // Create notifications table
                            $createTable = "
                                CREATE TABLE notifications (
                                    id INT AUTO_INCREMENT PRIMARY KEY,
                                    user_id INT NULL,
                                    sender_id INT NULL,
                                    title VARCHAR(255) NOT NULL,
                                    message TEXT NOT NULL,
                                    type VARCHAR(50) DEFAULT 'system',
                                    is_read BOOLEAN DEFAULT FALSE,
                                    read_at TIMESTAMP NULL,
                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                    INDEX idx_user_id (user_id),
                                    INDEX idx_is_read (is_read),
                                    INDEX idx_created_at (created_at)
                                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                            ";
                            $pdo->exec($createTable);
                        }
                        
                        $stmt = $pdo->prepare("
                            INSERT INTO notifications (user_id, sender_id, title, message, type, created_at)
                            VALUES (:user_id, :sender_id, :title, :message, :type, NOW())
                        ");
                        $stmt->execute([
                            ':user_id' => $targetUser,
                            ':sender_id' => $_SESSION['user_id'],
                            ':title' => $title,
                            ':message' => $message,
                            ':type' => $type
                        ]);
                        
                        flash('success_message', 'تم إنشاء التنبيه بنجاح', 'alert alert-success');
                    } else {
                        flash('error_message', 'يرجى ملء جميع الحقول المطلوبة', 'alert alert-danger');
                    }
                }
                break;
        }
        
        // Redirect to avoid form resubmission
        header('Location: ' . $_SERVER['PHP_SELF'] . '?tab=notifications');
        exit();
        
    } catch (PDOException $e) {
        error_log("Notification Action Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء تنفيذ العملية', 'alert alert-danger');
    }
}
?>
