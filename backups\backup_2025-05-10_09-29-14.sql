-- نظام إدارة العلاوات والترفيع وكتب الشكر - نسخة احتياطية
-- تاريخ النسخ الاحتياطي: 2025-05-10 09:29:14

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

DROP TABLE IF EXISTS `activity_log`;
CREATE TABLE `activity_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `action` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `entity_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `entity_id` int(11) NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `activity_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `alerts`;
CREATE TABLE `alerts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('allowance','promotion','retirement','appreciation_limit') COLLATE utf8mb4_unicode_ci NOT NULL,
  `employee_id` int(11) NOT NULL,
  `alert_date` date NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  CONSTRAINT `alerts_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `alerts` (`id`, `type`, `employee_id`, `alert_date`, `message`, `is_read`, `created_at`) VALUES
('7', 'retirement', '8', '2084-05-02', 'الموظف سيصل إلى سن التقاعد في 02/05/2085', '0', '2025-05-10 09:28:27');

DROP TABLE IF EXISTS `allowances`;
CREATE TABLE `allowances` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `allowance_date` date NOT NULL,
  `grade_at_time` int(11) NOT NULL COMMENT 'Employee grade at the time of allowance',
  `allowance_number` int(11) NOT NULL COMMENT 'Which allowance number in the grade (1-11)',
  `notes` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `allowances_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `allowances_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `appreciation_letters`;
CREATE TABLE `appreciation_letters` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `letter_date` date NOT NULL,
  `year` int(11) NOT NULL,
  `letter_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `issuer` enum('regular','prime_minister') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'regular',
  `file_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `months_reduction` int(11) NOT NULL COMMENT 'Number of months to reduce from next allowance/promotion date',
  `notes` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `appreciation_letters_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `appreciation_letters_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `departments` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES
('1', 'الإدارة', 'الإدارة العليا للمؤسسة', '2025-05-07 13:37:08', '2025-05-07 13:37:08'),
('2', 'الموارد البشرية', 'قسم إدارة الموارد البشرية', '2025-05-07 13:37:08', '2025-05-07 13:37:08'),
('3', 'المالية', 'قسم الشؤون المالية', '2025-05-07 13:37:08', '2025-05-07 13:37:08'),
('4', 'تكنولوجيا المعلومات', 'قسم تكنولوجيا المعلومات', '2025-05-07 13:37:08', '2025-05-07 13:37:08'),
('5', 'الخدمات الإدارية', 'قسم الخدمات الإدارية', '2025-05-07 13:37:08', '2025-05-07 13:37:08'),
('10', 'قسم الموارد البشرية', 'إدارة شؤون الموظفين والتوظيف والتدريب', '2025-05-08 09:16:54', '2025-05-08 09:16:54'),
('11', 'قسم تكنولوجيا المعلومات', 'إدارة أنظمة المعلومات والبنية التحتية التقنية', '2025-05-08 09:16:54', '2025-05-08 09:16:54'),
('13', 'قسم الإدارة', 'الإدارة العامة والشؤون الإدارية', '2025-05-08 09:16:54', '2025-05-08 09:16:54'),
('24', 'قسم المالية', 'إدارة الشؤون المالية والمحاسبية', '2025-05-08 10:53:22', '2025-05-08 10:53:22');

DROP TABLE IF EXISTS `education_levels`;
CREATE TABLE `education_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `max_grade` int(11) NOT NULL COMMENT 'Maximum grade an employee with this education can reach',
  `min_grade` int(11) NOT NULL DEFAULT 10,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `education_levels` (`id`, `name`, `max_grade`, `min_grade`, `created_at`, `updated_at`) VALUES
('1', 'بدون شهادة', '4', '10', '2025-05-07 13:37:08', '2025-05-07 13:37:08'),
('2', 'ابتدائية', '4', '10', '2025-05-07 13:37:08', '2025-05-07 13:37:08'),
('3', 'متوسطة', '4', '10', '2025-05-07 13:37:08', '2025-05-07 13:37:08'),
('4', 'إعدادية', '3', '10', '2025-05-07 13:37:08', '2025-05-07 13:37:08'),
('5', 'دبلوم', '1', '5', '2025-05-07 13:37:08', '2025-05-08 10:29:56'),
('6', 'بكالوريوس', '1', '7', '2025-05-07 13:37:08', '2025-05-08 10:29:56'),
('8', 'دكتوراه', '1', '1', '2025-05-07 13:37:08', '2025-05-08 10:29:56'),
('10', 'ماجستير', '1', '3', '2025-05-08 09:20:02', '2025-05-08 11:42:57');

DROP TABLE IF EXISTS `employees`;
CREATE TABLE `employees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_number` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `full_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `current_grade` int(11) NOT NULL COMMENT 'Current grade (1-10)',
  `current_stage` int(11) NOT NULL DEFAULT 1,
  `nominal_salary` decimal(10,2) DEFAULT NULL,
  `job_title` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `department_id` int(11) NOT NULL,
  `education_level_id` int(11) NOT NULL,
  `higher_degree_date` date DEFAULT NULL,
  `previous_grade` int(11) DEFAULT NULL,
  `law_103_applied` tinyint(1) NOT NULL DEFAULT 0,
  `max_grade_by_education` int(11) DEFAULT NULL,
  `hire_date` date NOT NULL,
  `birth_date` date DEFAULT NULL,
  `retirement_date` date DEFAULT NULL,
  `early_retirement_eligible` tinyint(1) NOT NULL DEFAULT 0,
  `years_of_service` int(11) NOT NULL COMMENT 'Total years of service',
  `years_in_current_grade` int(11) NOT NULL COMMENT 'Years in current grade',
  `allowances_in_current_grade` int(11) NOT NULL DEFAULT 0 COMMENT 'Number of allowances received in current grade',
  `appreciation_letters_count` int(11) NOT NULL DEFAULT 0,
  `last_allowance_date` date DEFAULT NULL COMMENT 'Date of last allowance',
  `last_promotion_date` date DEFAULT NULL COMMENT 'Date of last promotion',
  `next_allowance_date` date DEFAULT NULL COMMENT 'Expected date of next allowance',
  `next_promotion_date` date DEFAULT NULL COMMENT 'Expected date of next promotion',
  `notes` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `employee_number` (`employee_number`),
  KEY `department_id` (`department_id`),
  KEY `education_level_id` (`education_level_id`),
  CONSTRAINT `employees_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`),
  CONSTRAINT `employees_ibfk_2` FOREIGN KEY (`education_level_id`) REFERENCES `education_levels` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `employees` (`id`, `employee_number`, `full_name`, `current_grade`, `current_stage`, `nominal_salary`, `job_title`, `department_id`, `education_level_id`, `higher_degree_date`, `previous_grade`, `law_103_applied`, `max_grade_by_education`, `hire_date`, `birth_date`, `retirement_date`, `early_retirement_eligible`, `years_of_service`, `years_in_current_grade`, `allowances_in_current_grade`, `appreciation_letters_count`, `last_allowance_date`, `last_promotion_date`, `next_allowance_date`, `next_promotion_date`, `notes`, `created_at`, `updated_at`) VALUES
('8', '123456', 'علي حسين تسيار', '1', '1', '910.00', 'خبير', '1', '2', NULL, NULL, '0', '4', '2025-05-09', '2025-05-02', '2085-05-02', '0', '10', '10', '5', '0', '2025-05-01', '2023-01-10', '2026-05-01', '2028-01-10', '', '2025-05-10 09:28:27', '2025-05-10 09:28:27');

DROP TABLE IF EXISTS `grade_service_years`;
CREATE TABLE `grade_service_years` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `grade_id` int(11) NOT NULL,
  `service_years` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `grade_id` (`grade_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `grade_service_years` (`id`, `grade_id`, `service_years`, `created_at`) VALUES
('1', '1', '5', '2025-05-08 12:03:41'),
('2', '2', '5', '2025-05-08 12:03:41'),
('3', '3', '5', '2025-05-08 12:03:41'),
('4', '4', '5', '2025-05-08 12:03:41'),
('5', '5', '5', '2025-05-08 12:03:41'),
('6', '6', '4', '2025-05-08 12:03:41'),
('7', '7', '4', '2025-05-08 12:03:41'),
('8', '8', '4', '2025-05-08 12:03:41'),
('9', '9', '4', '2025-05-08 12:03:41'),
('10', '10', '4', '2025-05-08 12:03:41');

DROP TABLE IF EXISTS `grades`;
CREATE TABLE `grades` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `grades` (`id`, `name`, `created_at`) VALUES
('1', 'Ø§Ù„Ø¯Ø±Ø¬Ø© Ø§Ù„Ø£ÙˆÙ„Ù‰', '2025-05-08 12:03:41'),
('2', 'Ø§Ù„Ø¯Ø±Ø¬Ø© Ø§Ù„Ø«Ø§Ù†ÙŠØ©', '2025-05-08 12:03:41'),
('3', 'Ø§Ù„Ø¯Ø±Ø¬Ø© Ø§Ù„Ø«Ø§Ù„Ø«Ø©', '2025-05-08 12:03:41'),
('4', 'Ø§Ù„Ø¯Ø±Ø¬Ø© Ø§Ù„Ø±Ø§Ø¨Ø¹Ø©', '2025-05-08 12:03:41'),
('5', 'Ø§Ù„Ø¯Ø±Ø¬Ø© Ø§Ù„Ø®Ø§Ù…Ø³Ø©', '2025-05-08 12:03:41'),
('6', 'Ø§Ù„Ø¯Ø±Ø¬Ø© Ø§Ù„Ø³Ø§Ø¯Ø³Ø©', '2025-05-08 12:03:41'),
('7', 'Ø§Ù„Ø¯Ø±Ø¬Ø© Ø§Ù„Ø³Ø§Ø¨Ø¹Ø©', '2025-05-08 12:03:41'),
('8', 'Ø§Ù„Ø¯Ø±Ø¬Ø© Ø§Ù„Ø«Ø§Ù…Ù†Ø©', '2025-05-08 12:03:41'),
('9', 'Ø§Ù„Ø¯Ø±Ø¬Ø© Ø§Ù„ØªØ§Ø³Ø¹Ø©', '2025-05-08 12:03:41'),
('10', 'Ø§Ù„Ø¯Ø±Ø¬Ø© Ø§Ù„Ø¹Ø§Ø´Ø±Ø©', '2025-05-08 12:03:41');

DROP TABLE IF EXISTS `higher_degree_history`;
CREATE TABLE `higher_degree_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `education_level_id` int(11) NOT NULL,
  `previous_grade` int(11) NOT NULL,
  `new_grade` int(11) NOT NULL,
  `degree_date` date NOT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `education_level_id` (`education_level_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `higher_degree_history_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `higher_degree_history_ibfk_2` FOREIGN KEY (`education_level_id`) REFERENCES `education_levels` (`id`),
  CONSTRAINT `higher_degree_history_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `job_titles`;
CREATE TABLE `job_titles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `grade_id` int(11) NOT NULL,
  `title` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `grade_id` (`grade_id`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `job_titles` (`id`, `grade_id`, `title`, `description`, `created_at`) VALUES
('1', '1', 'مدير عام', 'المدير العام للمؤسسة أو الدائرة', '2025-05-08 14:02:15'),
('2', '1', 'خبير', 'خبير متخصص في مجال معين', '2025-05-08 14:02:15'),
('3', '1', 'مستشار', 'مستشار في مجال التخصص', '2025-05-08 14:02:15'),
('4', '2', 'مدير', 'مدير قسم أو إدارة', '2025-05-08 14:02:15'),
('5', '2', 'خبير أول', 'خبير أول في مجال التخصص', '2025-05-08 14:02:15'),
('6', '2', 'مستشار مساعد', 'مستشار مساعد في مجال التخصص', '2025-05-08 14:02:15'),
('7', '3', 'رئيس قسم', 'رئيس قسم في الإدارة', '2025-05-08 14:02:15'),
('8', '3', 'مهندس أقدم', 'مهندس بخبرة عالية', '2025-05-08 14:02:15'),
('9', '3', 'محاسب أقدم', 'محاسب بخبرة عالية', '2025-05-08 14:02:15'),
('10', '3', 'مبرمج أقدم', 'مبرمج بخبرة عالية', '2025-05-08 14:02:15'),
('11', '4', 'رئيس شعبة', 'رئيس شعبة في القسم', '2025-05-08 14:02:15'),
('12', '4', 'مهندس أول', 'مهندس بخبرة متوسطة', '2025-05-08 14:02:15'),
('13', '4', 'محاسب أول', 'محاسب بخبرة متوسطة', '2025-05-08 14:02:15'),
('14', '4', 'مبرمج أول', 'مبرمج بخبرة متوسطة', '2025-05-08 14:02:15'),
('15', '5', 'مسؤول وحدة', 'مسؤول وحدة في الشعبة', '2025-05-08 14:02:15'),
('16', '5', 'مهندس', 'مهندس', '2025-05-08 14:02:15'),
('17', '5', 'محاسب', 'محاسب', '2025-05-08 14:02:15'),
('18', '5', 'مبرمج', 'مبرمج', '2025-05-08 14:02:15'),
('19', '5', 'إداري أول', 'إداري بخبرة متوسطة', '2025-05-08 14:02:15'),
('20', '6', 'مهندس مساعد', 'مهندس حديث التخرج', '2025-05-08 14:02:15'),
('21', '6', 'محاسب مساعد', 'محاسب حديث التخرج', '2025-05-08 14:02:15'),
('22', '6', 'مبرمج مساعد', 'مبرمج حديث التخرج', '2025-05-08 14:02:15'),
('23', '6', 'إداري', 'موظف إداري', '2025-05-08 14:02:15'),
('24', '7', 'فني أول', 'فني بخبرة متوسطة', '2025-05-08 14:02:15'),
('25', '7', 'كاتب أول', 'كاتب بخبرة متوسطة', '2025-05-08 14:02:15'),
('26', '7', 'سكرتير أول', 'سكرتير بخبرة متوسطة', '2025-05-08 14:02:15'),
('27', '8', 'فني', 'فني', '2025-05-08 14:02:15'),
('28', '8', 'كاتب', 'كاتب', '2025-05-08 14:02:15'),
('29', '8', 'سكرتير', 'سكرتير', '2025-05-08 14:02:15'),
('30', '9', 'فني مساعد', 'فني مساعد', '2025-05-08 14:02:15'),
('31', '9', 'كاتب مساعد', 'كاتب مساعد', '2025-05-08 14:02:15'),
('32', '9', 'مساعد إداري', 'مساعد إداري', '2025-05-08 14:02:15'),
('33', '10', 'موظف خدمة', 'موظف خدمة', '2025-05-08 14:02:15'),
('34', '10', 'حارس', 'حارس', '2025-05-08 14:02:15'),
('35', '10', 'سائق', 'سائق', '2025-05-08 14:02:15'),
('36', '10', 'مراسل', 'مراسل', '2025-05-08 14:02:15'),
('37', '1', 'وزير', '', '2025-05-08 14:18:53');

DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` enum('allowance','promotion','appreciation','employee','system') COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `link` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `promotions`;
CREATE TABLE `promotions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `promotion_date` date NOT NULL,
  `from_grade` int(11) NOT NULL,
  `to_grade` int(11) NOT NULL,
  `years_in_previous_grade` int(11) NOT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `promotions_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `promotions_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `salary_history`;
CREATE TABLE `salary_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `grade` int(11) NOT NULL,
  `stage` int(11) NOT NULL,
  `nominal_salary` decimal(10,2) NOT NULL,
  `effective_date` date NOT NULL,
  `reason` enum('initial','allowance','promotion','higher_degree') COLLATE utf8mb4_unicode_ci NOT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `salary_history_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `salary_history_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `salary_history` (`id`, `employee_id`, `grade`, `stage`, `nominal_salary`, `effective_date`, `reason`, `notes`, `created_by`, `created_at`) VALUES
('8', '8', '1', '1', '910.00', '2025-05-09', 'initial', 'الراتب الاسمي الأولي', '2', '2025-05-10 09:28:27');

DROP TABLE IF EXISTS `stages`;
CREATE TABLE `stages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `grade_id` int(11) NOT NULL,
  `stage_number` int(11) NOT NULL,
  `nominal_salary` decimal(10,2) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `grade_stage` (`grade_id`,`stage_number`)
) ENGINE=InnoDB AUTO_INCREMENT=252 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `stages` (`id`, `grade_id`, `stage_number`, `nominal_salary`, `created_at`) VALUES
('142', '1', '1', '910.00', '2025-05-08 12:03:41'),
('143', '1', '2', '930.00', '2025-05-08 12:03:41'),
('144', '1', '3', '950.00', '2025-05-08 12:03:41'),
('145', '1', '4', '970.00', '2025-05-08 12:03:41'),
('146', '1', '5', '990.00', '2025-05-08 12:03:41'),
('147', '1', '6', '1010.00', '2025-05-08 12:03:41'),
('148', '1', '7', '1030.00', '2025-05-08 12:03:41'),
('149', '1', '8', '1050.00', '2025-05-08 12:03:41'),
('150', '1', '9', '1070.00', '2025-05-08 12:03:41'),
('151', '1', '10', '1090.00', '2025-05-08 12:03:41'),
('152', '1', '11', '1110.00', '2025-05-08 12:03:41'),
('153', '2', '1', '723.00', '2025-05-08 12:03:41'),
('154', '2', '2', '740.00', '2025-05-08 12:03:41'),
('155', '2', '3', '757.00', '2025-05-08 12:03:41'),
('156', '2', '4', '774.00', '2025-05-08 12:03:41'),
('157', '2', '5', '791.00', '2025-05-08 12:03:41'),
('158', '2', '6', '808.00', '2025-05-08 12:03:41'),
('159', '2', '7', '825.00', '2025-05-08 12:03:41'),
('160', '2', '8', '842.00', '2025-05-08 12:03:41'),
('161', '2', '9', '859.00', '2025-05-08 12:03:41'),
('162', '2', '10', '876.00', '2025-05-08 12:03:41'),
('163', '2', '11', '893.00', '2025-05-08 12:03:41'),
('164', '3', '1', '600.00', '2025-05-08 12:03:41'),
('165', '3', '2', '610.00', '2025-05-08 12:03:41'),
('166', '3', '3', '620.00', '2025-05-08 12:03:41'),
('167', '3', '4', '630.00', '2025-05-08 12:03:41'),
('168', '3', '5', '640.00', '2025-05-08 12:03:41'),
('169', '3', '6', '650.00', '2025-05-08 12:03:41'),
('170', '3', '7', '660.00', '2025-05-08 12:03:41'),
('171', '3', '8', '670.00', '2025-05-08 12:03:41'),
('172', '3', '9', '680.00', '2025-05-08 12:03:41'),
('173', '3', '10', '690.00', '2025-05-08 12:03:41'),
('174', '3', '11', '700.00', '2025-05-08 12:03:41'),
('175', '4', '1', '509.00', '2025-05-08 12:03:41'),
('176', '4', '2', '517.00', '2025-05-08 12:03:41'),
('177', '4', '3', '525.00', '2025-05-08 12:03:41'),
('178', '4', '4', '533.00', '2025-05-08 12:03:41'),
('179', '4', '5', '541.00', '2025-05-08 12:03:41'),
('180', '4', '6', '549.00', '2025-05-08 12:03:41'),
('181', '4', '7', '557.00', '2025-05-08 12:03:41'),
('182', '4', '8', '565.00', '2025-05-08 12:03:41'),
('183', '4', '9', '573.00', '2025-05-08 12:03:41'),
('184', '4', '10', '581.00', '2025-05-08 12:03:41'),
('185', '4', '11', '589.00', '2025-05-08 12:03:41'),
('186', '5', '1', '429.00', '2025-05-08 12:03:41'),
('187', '5', '2', '435.00', '2025-05-08 12:03:41'),
('188', '5', '3', '441.00', '2025-05-08 12:03:41'),
('189', '5', '4', '447.00', '2025-05-08 12:03:41'),
('190', '5', '5', '453.00', '2025-05-08 12:03:41'),
('191', '5', '6', '459.00', '2025-05-08 12:03:41'),
('192', '5', '7', '465.00', '2025-05-08 12:03:41'),
('193', '5', '8', '471.00', '2025-05-08 12:03:41'),
('194', '5', '9', '477.00', '2025-05-08 12:03:41'),
('195', '5', '10', '483.00', '2025-05-08 12:03:41'),
('196', '5', '11', '489.00', '2025-05-08 12:03:41'),
('197', '6', '1', '362.00', '2025-05-08 12:03:41'),
('198', '6', '2', '368.00', '2025-05-08 12:03:41'),
('199', '6', '3', '374.00', '2025-05-08 12:03:41'),
('200', '6', '4', '380.00', '2025-05-08 12:03:41'),
('201', '6', '5', '386.00', '2025-05-08 12:03:41'),
('202', '6', '6', '392.00', '2025-05-08 12:03:41'),
('203', '6', '7', '398.00', '2025-05-08 12:03:41'),
('204', '6', '8', '404.00', '2025-05-08 12:03:41'),
('205', '6', '9', '410.00', '2025-05-08 12:03:41'),
('206', '6', '10', '416.00', '2025-05-08 12:03:41'),
('207', '6', '11', '422.00', '2025-05-08 12:03:41'),
('208', '7', '1', '296.00', '2025-05-08 12:03:41'),
('209', '7', '2', '302.00', '2025-05-08 12:03:41'),
('210', '7', '3', '308.00', '2025-05-08 12:03:41'),
('211', '7', '4', '314.00', '2025-05-08 12:03:41'),
('212', '7', '5', '320.00', '2025-05-08 12:03:41'),
('213', '7', '6', '326.00', '2025-05-08 12:03:41'),
('214', '7', '7', '332.00', '2025-05-08 12:03:41'),
('215', '7', '8', '338.00', '2025-05-08 12:03:41'),
('216', '7', '9', '344.00', '2025-05-08 12:03:41'),
('217', '7', '10', '350.00', '2025-05-08 12:03:41'),
('218', '7', '11', '356.00', '2025-05-08 12:03:41'),
('219', '8', '1', '260.00', '2025-05-08 12:03:41'),
('220', '8', '2', '263.00', '2025-05-08 12:03:41'),
('221', '8', '3', '266.00', '2025-05-08 12:03:41'),
('222', '8', '4', '269.00', '2025-05-08 12:03:41'),
('223', '8', '5', '272.00', '2025-05-08 12:03:41'),
('224', '8', '6', '275.00', '2025-05-08 12:03:41'),
('225', '8', '7', '278.00', '2025-05-08 12:03:41'),
('226', '8', '8', '281.00', '2025-05-08 12:03:41'),
('227', '8', '9', '284.00', '2025-05-08 12:03:41'),
('228', '8', '10', '287.00', '2025-05-08 12:03:41'),
('229', '8', '11', '290.00', '2025-05-08 12:03:41'),
('230', '9', '1', '210.00', '2025-05-08 12:03:41'),
('231', '9', '2', '213.00', '2025-05-08 12:03:41'),
('232', '9', '3', '216.00', '2025-05-08 12:03:41'),
('233', '9', '4', '219.00', '2025-05-08 12:03:41'),
('234', '9', '5', '222.00', '2025-05-08 12:03:41'),
('235', '9', '6', '225.00', '2025-05-08 12:03:41'),
('236', '9', '7', '228.00', '2025-05-08 12:03:41'),
('237', '9', '8', '231.00', '2025-05-08 12:03:41'),
('238', '9', '9', '234.00', '2025-05-08 12:03:41'),
('239', '9', '10', '237.00', '2025-05-08 12:03:41'),
('240', '9', '11', '240.00', '2025-05-08 12:03:41'),
('241', '10', '1', '170.00', '2025-05-08 12:03:41');

INSERT INTO `stages` (`id`, `grade_id`, `stage_number`, `nominal_salary`, `created_at`) VALUES
('242', '10', '2', '173.00', '2025-05-08 12:03:41'),
('243', '10', '3', '176.00', '2025-05-08 12:03:41'),
('244', '10', '4', '179.00', '2025-05-08 12:03:41'),
('245', '10', '5', '182.00', '2025-05-08 12:03:41'),
('246', '10', '6', '185.00', '2025-05-08 12:03:41'),
('247', '10', '7', '188.00', '2025-05-08 12:03:41'),
('248', '10', '8', '191.00', '2025-05-08 12:03:41'),
('249', '10', '9', '194.00', '2025-05-08 12:03:41'),
('250', '10', '10', '197.00', '2025-05-08 12:03:41'),
('251', '10', '11', '200.00', '2025-05-08 12:03:41');

DROP TABLE IF EXISTS `system_logs`;
CREATE TABLE `system_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `action` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `entity_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `entity_id` int(11) DEFAULT NULL,
  `details` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `system_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=129 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `system_logs` (`id`, `user_id`, `action`, `entity_type`, `entity_id`, `details`, `ip_address`, `created_at`) VALUES
('1', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-07 13:53:33'),
('2', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-07 14:03:54'),
('3', '2', 'إضافة موظف', 'employees', '1', 'تمت إضافة موظف جديد: علي حسين تسيار', '::1', '2025-05-07 14:09:10'),
('4', '2', 'إضافة كتاب شكر', 'appreciation_letters', '1', 'تمت إضافة كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-07 14:11:56'),
('5', '2', 'إضافة كتاب شكر', 'appreciation_letters', '2', 'تمت إضافة كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-07 14:12:42'),
('6', '2', 'إضافة كتاب شكر', 'appreciation_letters', '3', 'تمت إضافة كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-07 14:13:51'),
('7', '2', 'إضافة كتاب شكر', 'appreciation_letters', '4', 'تمت إضافة كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-07 14:14:26'),
('8', '2', 'إضافة كتاب شكر', 'appreciation_letters', '5', 'تمت إضافة كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-07 14:14:41'),
('9', '2', 'تسجيل خروج', 'users', '2', 'تم تسجيل الخروج بنجاح', '::1', '2025-05-07 14:19:07'),
('10', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-07 14:19:20'),
('11', '2', 'تسجيل خروج', 'users', '2', 'تم تسجيل الخروج بنجاح', '::1', '2025-05-07 14:21:42'),
('12', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-07 14:22:10'),
('13', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-07 14:26:21'),
('14', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-07 14:29:01'),
('15', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 08:23:19'),
('16', '2', 'تسجيل خروج', 'users', '2', 'تم تسجيل الخروج بنجاح', '::1', '2025-05-08 08:23:50'),
('17', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 08:23:54'),
('18', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 08:33:29'),
('19', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 08:47:57'),
('20', '2', 'تحديث إعدادات النظام', 'system_settings', '0', 'تم تحديث إعدادات النظام', '::1', '2025-05-08 08:48:38'),
('21', '2', 'تحديث إعدادات النظام', 'system_settings', '0', 'تم تحديث إعدادات النظام', '::1', '2025-05-08 08:48:41'),
('22', '2', 'تسجيل خروج', 'users', '2', 'تم تسجيل الخروج بنجاح', '::1', '2025-05-08 08:53:49'),
('23', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 08:53:52'),
('24', '2', 'إرسال تذكيرات', 'reminders', '0', 'تم إرسال تذكيرات للمديرين حول الاستحقاقات القادمة', '::1', '2025-05-08 09:01:13'),
('25', '2', 'تسجيل خروج', 'users', '2', 'تم تسجيل الخروج بنجاح', '::1', '2025-05-08 09:04:04'),
('26', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 09:04:06'),
('27', '2', 'تسجيل خروج', 'users', '2', 'تم تسجيل الخروج بنجاح', '::1', '2025-05-08 09:24:26'),
('28', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 09:24:28'),
('29', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 09:32:54'),
('30', '2', 'حذف كتاب شكر', 'appreciation_letters', '3', 'تم حذف كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 09:40:33'),
('31', '2', 'حذف قسم', 'departments', '9', 'تم حذف القسم: قسم الإدارة', '::1', '2025-05-08 09:40:54'),
('32', '2', 'تعديل كتاب شكر', 'appreciation_letters', '1', 'تم تعديل كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 10:03:53'),
('33', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 10:09:51'),
('34', '2', 'حذف كتاب شكر', 'appreciation_letters', '4', 'تم حذف كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 10:11:39'),
('35', '2', 'حذف كتاب شكر', 'appreciation_letters', '2', 'تم حذف كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 10:11:41'),
('36', '2', 'حذف كتاب شكر', 'appreciation_letters', '5', 'تم حذف كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 10:11:43'),
('37', '2', 'حذف كتاب شكر', 'appreciation_letters', '1', 'تم حذف كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 10:11:59'),
('38', '2', 'حذف موظف', 'employees', '1', 'تم حذف الموظف: علي حسين تسيار', '::1', '2025-05-08 10:12:03'),
('39', '2', 'تسجيل خروج', 'users', '2', 'تم تسجيل الخروج بنجاح', '::1', '2025-05-08 10:30:43'),
('40', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 10:30:45'),
('41', '2', 'إضافة موظف', 'employees', '2', 'تمت إضافة موظف جديد: علي حسين تسيار', '::1', '2025-05-08 10:38:20'),
('42', '2', 'إضافة كتاب شكر', 'appreciation_letters', '6', 'تمت إضافة كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 10:39:58'),
('43', '2', 'إضافة كتاب شكر', 'appreciation_letters', '7', 'تمت إضافة كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 10:40:25'),
('44', '2', 'إضافة كتاب شكر', 'appreciation_letters', '8', 'تمت إضافة كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 10:40:52'),
('45', '2', 'إضافة كتاب شكر', 'appreciation_letters', '9', 'تمت إضافة كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 10:42:24'),
('46', '2', 'إضافة كتاب شكر', 'appreciation_letters', '10', 'تمت إضافة كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 10:43:06'),
('47', '2', 'تعديل موظف', 'employees', '2', 'تم تعديل بيانات الموظف: علي حسين تسيار', '::1', '2025-05-08 10:45:14'),
('48', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 10:54:42'),
('49', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 11:10:00'),
('50', '2', 'تعديل موظف', 'employees', '2', 'تم تعديل بيانات الموظف: علي حسين تسيار', '::1', '2025-05-08 11:11:50'),
('51', '2', 'إضافة موظف', 'employees', '3', 'تمت إضافة موظف جديد: محمد حميد', '::1', '2025-05-08 11:18:12'),
('52', '2', 'تطبيق قانون 103', 'employees', '3', 'تم تطبيق قانون 103 للموظف بسبب الحصول على شهادة أعلى', '::1', '2025-05-08 11:18:53'),
('53', '2', 'تسجيل خروج', 'users', '2', 'تم تسجيل الخروج بنجاح', '::1', '2025-05-08 11:37:31'),
('54', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 11:37:35'),
('55', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 12:07:01'),
('56', '2', 'حذف قسم', 'departments', '34', 'تم حذف القسم: Ù‚Ø³Ù… Ø§Ù„Ø®Ø¯Ù…Ø§Øª', '::1', '2025-05-08 12:07:19'),
('57', '2', 'حذف قسم', 'departments', '39', 'تم حذف القسم: Ù‚Ø³Ù… Ø§Ù„Ø®Ø¯Ù…Ø§Øª', '::1', '2025-05-08 12:07:21'),
('58', '2', 'حذف قسم', 'departments', '33', 'تم حذف القسم: Ù‚Ø³Ù… Ø§Ù„Ø¥Ø¯Ø§Ø±Ø©', '::1', '2025-05-08 12:07:23'),
('59', '2', 'حذف قسم', 'departments', '38', 'تم حذف القسم: Ù‚Ø³Ù… Ø§Ù„Ø¥Ø¯Ø§Ø±Ø©', '::1', '2025-05-08 12:07:25'),
('60', '2', 'حذف قسم', 'departments', '32', 'تم حذف القسم: Ù‚Ø³Ù… Ø§Ù„Ù…Ø§Ù„ÙŠØ©', '::1', '2025-05-08 12:07:27'),
('61', '2', 'حذف قسم', 'departments', '37', 'تم حذف القسم: Ù‚Ø³Ù… Ø§Ù„Ù…Ø§Ù„ÙŠØ©', '::1', '2025-05-08 12:07:28'),
('62', '2', 'حذف قسم', 'departments', '30', 'تم حذف القسم: Ù‚Ø³Ù… Ø§Ù„Ù…ÙˆØ§Ø±Ø¯ Ø§Ù„Ø¨Ø´Ø±ÙŠØ©', '::1', '2025-05-08 12:07:30'),
('63', '2', 'حذف قسم', 'departments', '35', 'تم حذف القسم: Ù‚Ø³Ù… Ø§Ù„Ù…ÙˆØ§Ø±Ø¯ Ø§Ù„Ø¨Ø´Ø±ÙŠØ©', '::1', '2025-05-08 12:07:32'),
('64', '2', 'حذف قسم', 'departments', '31', 'تم حذف القسم: Ù‚Ø³Ù… ØªÙƒÙ†ÙˆÙ„ÙˆØ¬ÙŠØ§ Ø§Ù„Ù…Ø¹Ù„ÙˆÙ…Ø§Øª', '::1', '2025-05-08 12:07:34'),
('65', '2', 'حذف قسم', 'departments', '36', 'تم حذف القسم: Ù‚Ø³Ù… ØªÙƒÙ†ÙˆÙ„ÙˆØ¬ÙŠØ§ Ø§Ù„Ù…Ø¹Ù„ÙˆÙ…Ø§Øª', '::1', '2025-05-08 12:07:37'),
('66', '2', 'حذف قسم', 'departments', '17', 'تم حذف القسم: قسم الإدارة', '::1', '2025-05-08 12:07:41'),
('67', '2', 'حذف قسم', 'departments', '21', 'تم حذف القسم: قسم الإدارة', '::1', '2025-05-08 12:07:44'),
('68', '2', 'حذف قسم', 'departments', '29', 'تم حذف القسم: قسم الإدارة', '::1', '2025-05-08 12:07:47'),
('69', '2', 'حذف قسم', 'departments', '27', 'تم حذف القسم: قسم تكنولوجيا المعلومات', '::1', '2025-05-08 12:07:51'),
('70', '2', 'حذف قسم', 'departments', '23', 'تم حذف القسم: قسم تكنولوجيا المعلومات', '::1', '2025-05-08 12:07:53'),
('71', '2', 'حذف قسم', 'departments', '19', 'تم حذف القسم: قسم تكنولوجيا المعلومات', '::1', '2025-05-08 12:07:56'),
('72', '2', 'حذف قسم', 'departments', '15', 'تم حذف القسم: قسم تكنولوجيا المعلومات', '::1', '2025-05-08 12:07:58'),
('73', '2', 'حذف قسم', 'departments', '16', 'تم حذف القسم: قسم المالية', '::1', '2025-05-08 12:08:08'),
('74', '2', 'حذف قسم', 'departments', '20', 'تم حذف القسم: قسم المالية', '::1', '2025-05-08 12:08:14'),
('75', '2', 'حذف قسم', 'departments', '28', 'تم حذف القسم: قسم المالية', '::1', '2025-05-08 12:08:18'),
('76', '2', 'حذف قسم', 'departments', '14', 'تم حذف القسم: قسم الموارد البشرية', '::1', '2025-05-08 12:08:23'),
('77', '2', 'حذف قسم', 'departments', '22', 'تم حذف القسم: قسم الموارد البشرية', '::1', '2025-05-08 12:08:27'),
('78', '2', 'حذف قسم', 'departments', '18', 'تم حذف القسم: قسم الموارد البشرية', '::1', '2025-05-08 12:08:30'),
('79', '2', 'حذف قسم', 'departments', '26', 'تم حذف القسم: قسم الموارد البشرية', '::1', '2025-05-08 12:08:33'),
('80', '2', 'حذف قسم', 'departments', '8', 'تم حذف القسم: قسم المالية', '::1', '2025-05-08 12:08:37'),
('81', '2', 'حذف قسم', 'departments', '12', 'تم حذف القسم: قسم المالية', '::1', '2025-05-08 12:08:40'),
('82', '2', 'حذف قسم', 'departments', '6', 'تم حذف القسم: قسم الموارد البشرية', '::1', '2025-05-08 12:08:43'),
('83', '2', 'حذف قسم', 'departments', '7', 'تم حذف القسم: قسم تكنولوجيا المعلومات', '::1', '2025-05-08 12:08:46'),
('84', '2', 'حذف قسم', 'departments', '25', 'تم حذف القسم: قسم الإدارة', '::1', '2025-05-08 12:08:50'),
('85', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 12:10:36'),
('86', '2', 'حذف مستخدم', 'users', '8', 'تم حذف المستخدم: hr', '::1', '2025-05-08 12:13:19'),
('87', '2', 'حذف مستخدم', 'users', '9', 'تم حذف المستخدم: viewer', '::1', '2025-05-08 12:13:22'),
('88', '2', 'حذف كتاب شكر', 'appreciation_letters', '6', 'تم حذف كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 12:16:03'),
('89', '2', 'حذف كتاب شكر', 'appreciation_letters', '7', 'تم حذف كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 12:16:05'),
('90', '2', 'حذف كتاب شكر', 'appreciation_letters', '9', 'تم حذف كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 12:16:06'),
('91', '2', 'حذف كتاب شكر', 'appreciation_letters', '10', 'تم حذف كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 12:16:08'),
('92', '2', 'حذف كتاب شكر', 'appreciation_letters', '8', 'تم حذف كتاب شكر للموظف: علي حسين تسيار', '::1', '2025-05-08 12:16:12'),
('93', '2', 'تعديل موظف', 'employees', '2', 'تم تعديل بيانات الموظف: علي حسين تسيار', '::1', '2025-05-08 12:17:28'),
('94', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 12:21:55'),
('95', '2', 'حذف موظف', 'employees', '2', 'تم حذف الموظف: علي حسين تسيار', '::1', '2025-05-08 12:22:14'),
('96', '2', 'حذف موظف', 'employees', '3', 'تم حذف الموظف: محمد حميد', '::1', '2025-05-08 12:22:20'),
('97', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 12:44:09'),
('98', '2', 'إضافة موظف', 'employees', '4', 'تمت إضافة موظف جديد: علي حسين تسيار', '::1', '2025-05-08 13:02:08'),
('99', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 13:06:52'),
('100', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 13:23:28');

INSERT INTO `system_logs` (`id`, `user_id`, `action`, `entity_type`, `entity_id`, `details`, `ip_address`, `created_at`) VALUES
('101', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 13:27:32'),
('102', '2', 'حذف موظف', 'employees', '4', 'تم حذف الموظف: علي حسين تسيار', '::1', '2025-05-08 13:28:25'),
('103', '2', 'إضافة موظف', 'employees', '5', 'تمت إضافة موظف جديد: علي حسين تسيار', '::1', '2025-05-08 13:39:33'),
('104', '2', 'حذف موظف', 'employees', '5', 'تم حذف الموظف: علي حسين تسيار', '::1', '2025-05-08 13:40:42'),
('105', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 13:53:07'),
('106', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 14:02:35'),
('107', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 14:08:47'),
('108', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-08 14:18:36'),
('109', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-10 08:58:16'),
('110', '2', 'إضافة موظف', 'employees', '6', 'تمت إضافة موظف جديد: علي حسين تسيار', '::1', '2025-05-10 09:16:49'),
('111', '2', 'حذف موظف', 'employees', '6', 'تم حذف الموظف: علي حسين تسيار', '::1', '2025-05-10 09:17:48'),
('112', '2', 'إضافة موظف', 'employees', '7', 'تمت إضافة موظف جديد: علي حسين تسيار', '::1', '2025-05-10 09:23:43'),
('113', '2', 'حذف موظف', 'employees', '7', 'تم حذف الموظف: علي حسين تسيار', '::1', '2025-05-10 09:24:22'),
('114', '2', 'حذف نسخة احتياطية', 'backup', '0', 'تم حذف النسخة الاحتياطية: backup_2025-05-08_12-44-57.sql', '::1', '2025-05-10 09:25:24'),
('115', '2', 'تسجيل دخول', 'users', '2', 'تم تسجيل الدخول بنجاح', '::1', '2025-05-10 09:26:58'),
('116', '2', 'إنشاء نسخة احتياطية', 'backup', '0', 'تم إنشاء نسخة احتياطية: backup_2025-05-10_09-27-04.sql', '::1', '2025-05-10 09:27:04'),
('117', '2', 'إنشاء نسخة احتياطية', 'backup', '0', 'تم إنشاء نسخة احتياطية: backup_2025-05-10_09-27-07.sql', '::1', '2025-05-10 09:27:07'),
('118', '2', 'إضافة موظف', 'employees', '8', 'تمت إضافة موظف جديد: علي حسين تسيار', '::1', '2025-05-10 09:28:27'),
('119', '2', 'حذف نسخة احتياطية', 'backup', '0', 'تم حذف النسخة الاحتياطية: backup_2025-05-08_12-44-59.sql', '::1', '2025-05-10 09:28:41'),
('120', '2', 'حذف نسخة احتياطية', 'backup', '0', 'تم حذف النسخة الاحتياطية: backup_2025-05-08_12-45-22.sql', '::1', '2025-05-10 09:28:43'),
('121', '2', 'حذف نسخة احتياطية', 'backup', '0', 'تم حذف النسخة الاحتياطية: backup_2025-05-08_12-49-10.sql', '::1', '2025-05-10 09:28:46'),
('122', '2', 'حذف نسخة احتياطية', 'backup', '0', 'تم حذف النسخة الاحتياطية: backup_2025-05-10_09-22-43.sql', '::1', '2025-05-10 09:28:55'),
('123', '2', 'حذف نسخة احتياطية', 'backup', '0', 'تم حذف النسخة الاحتياطية: backup_2025-05-10_09-22-47.sql', '::1', '2025-05-10 09:28:58'),
('124', '2', 'حذف نسخة احتياطية', 'backup', '0', 'تم حذف النسخة الاحتياطية: backup_2025-05-10_09-22-52.sql', '::1', '2025-05-10 09:29:01'),
('125', '2', 'حذف نسخة احتياطية', 'backup', '0', 'تم حذف النسخة الاحتياطية: backup_2025-05-10_09-23-50.sql', '::1', '2025-05-10 09:29:03'),
('126', '2', 'حذف نسخة احتياطية', 'backup', '0', 'تم حذف النسخة الاحتياطية: temp_1746858269.sql', '::1', '2025-05-10 09:29:06'),
('127', '2', 'حذف نسخة احتياطية', 'backup', '0', 'تم حذف النسخة الاحتياطية: backup_2025-05-10_09-27-04.sql', '::1', '2025-05-10 09:29:08'),
('128', '2', 'حذف نسخة احتياطية', 'backup', '0', 'تم حذف النسخة الاحتياطية: backup_2025-05-10_09-27-07.sql', '::1', '2025-05-10 09:29:10');

DROP TABLE IF EXISTS `system_settings`;
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_value` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `system_settings` (`id`, `setting_key`, `setting_value`, `description`) VALUES
('1', 'allowances_per_grade', '11', 'عدد العلاوات في كل درجة'),
('2', 'promotion_years_lower_grades', '4', 'عدد سنوات الترفيع للدرجات الدنيا (10-5)'),
('3', 'promotion_years_upper_grades', '5', 'عدد سنوات الترفيع للدرجات العليا (5-1)'),
('4', 'regular_letter_months', '1', 'عدد أشهر تخفيض كتاب الشكر العادي'),
('5', 'pm_letter_months', '6', 'عدد أشهر تخفيض كتاب شكر رئيس الوزراء'),
('6', 'enable_notifications', '1', 'تفعيل نظام الإشعارات'),
('7', 'enable_reminders', '1', 'تفعيل نظام التذكيرات'),
('8', 'reminder_days', '30', 'عدد أيام التذكير المسبق'),
('19', 'retirement_age', '60', 'العمر القانوني للتقاعد'),
('20', 'early_retirement_years', '25', 'عدد سنوات الخدمة المطلوبة للتقاعد المبكر'),
('21', 'max_appreciation_letters', '3', 'الحد الأقصى لكتب الشكر سنوياً'),
('22', 'law_103_promotion_years', '2', 'عدد سنوات الترفيع وفق قانون 103');

DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `full_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `role` enum('admin','hr','viewer') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'viewer',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `users` (`id`, `username`, `password`, `full_name`, `email`, `role`, `created_at`, `updated_at`) VALUES
('1', 'admin', '$2y$10$8zUkFX1tXYdJMFLwkVl.5OQCJpQ0ZZ6Zr1gZIiALHu7S8UlOyLjbm', 'مدير النظام', '<EMAIL>', 'admin', '2025-05-07 13:37:08', '2025-05-07 13:37:08'),
('2', 'admin2', '$2y$10$1BW0Y44Nyx43pHYT9cpdTuzepG/1t9pWAH7eA.9jGVg8IzoLKIMGe', 'مدير النظام الجديد', '<EMAIL>', 'admin', '2025-05-07 13:52:04', '2025-05-07 13:52:04');

SET FOREIGN_KEY_CHECKS=1;
COMMIT;
