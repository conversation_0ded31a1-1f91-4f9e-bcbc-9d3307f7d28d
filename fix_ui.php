<?php
/**
 * Fix UI Issues
 * إصلاح مشاكل الواجهة الرسومية
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// CSS styles
$styles = '
<style>
    body {
        font-family: Arial, sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 20px;
        direction: rtl;
    }
    .container {
        max-width: 800px;
        margin: 0 auto;
        background-color: #fff;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    h1, h2 {
        color: #007bff;
    }
    .alert-success {
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .alert-warning {
        color: #856404;
        background-color: #fff3cd;
        border-color: #ffeeba;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .btn {
        display: inline-block;
        padding: 8px 16px;
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.5;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        cursor: pointer;
        border: 1px solid transparent;
        border-radius: 4px;
        text-decoration: none;
    }
    .btn-primary {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }
    .btn-success {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    pre {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        overflow: auto;
    }
    .step {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border-left: 4px solid #007bff;
    }
    .step h3 {
        margin-top: 0;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
    table, th, td {
        border: 1px solid #ddd;
    }
    th, td {
        padding: 10px;
        text-align: right;
    }
    th {
        background-color: #f2f2f2;
    }
</style>
';

// Start HTML output
echo '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل الواجهة الرسومية - نظام إدارة العلاوات والترقية</title>
    ' . $styles . '
</head>
<body>
    <div class="container">
        <h1>إصلاح مشاكل الواجهة الرسومية</h1>
        <p>هذه الأداة تساعد في إصلاح مشاكل الواجهة الرسومية والقوائم بعد نقل النظام إلى حاسوب آخر.</p>';

// Get current directory
$currentDir = dirname(__FILE__);
$rootDir = realpath($currentDir);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['fix_ui'])) {
        echo '<h2>جاري إصلاح مشاكل الواجهة الرسومية...</h2>';
        
        $success = true;
        $messages = [];
        
        // Step 1: Fix CSS files
        echo '<div class="step">
            <h3>الخطوة 1: إصلاح ملفات CSS</h3>';
        
        // Check if CSS directory exists
        $cssDir = $rootDir . '/assets/css';
        if (file_exists($cssDir)) {
            echo '<p>مجلد CSS موجود: ' . $cssDir . '</p>';
            
            // Check main CSS file
            $mainCssFile = $cssDir . '/style.css';
            if (file_exists($mainCssFile)) {
                echo '<p>ملف CSS الرئيسي موجود: ' . $mainCssFile . '</p>';
                
                // Make sure it's readable
                if (is_readable($mainCssFile)) {
                    echo '<p>ملف CSS الرئيسي قابل للقراءة.</p>';
                    
                    // Add RTL fixes if needed
                    $cssContent = file_get_contents($mainCssFile);
                    
                    // Check if RTL styles are already included
                    if (strpos($cssContent, 'direction: rtl') === false) {
                        $rtlFixes = "\n\n/* RTL Fixes */\n";
                        $rtlFixes .= "body, .container, .row, .col, .form-group, .table {\n";
                        $rtlFixes .= "    direction: rtl;\n";
                        $rtlFixes .= "    text-align: right;\n";
                        $rtlFixes .= "}\n\n";
                        $rtlFixes .= ".dropdown-menu {\n";
                        $rtlFixes .= "    right: 0;\n";
                        $rtlFixes .= "    left: auto;\n";
                        $rtlFixes .= "    text-align: right;\n";
                        $rtlFixes .= "}\n\n";
                        $rtlFixes .= ".input-group-text {\n";
                        $rtlFixes .= "    border-radius: 0 0.25rem 0.25rem 0 !important;\n";
                        $rtlFixes .= "}\n\n";
                        $rtlFixes .= ".form-control {\n";
                        $rtlFixes .= "    border-radius: 0.25rem 0 0 0.25rem !important;\n";
                        $rtlFixes .= "}\n";
                        
                        // Append RTL fixes to CSS file
                        if (file_put_contents($mainCssFile, $cssContent . $rtlFixes)) {
                            $messages[] = "تم إضافة إصلاحات RTL إلى ملف CSS الرئيسي.";
                        } else {
                            $success = false;
                            $messages[] = "فشل في إضافة إصلاحات RTL إلى ملف CSS الرئيسي.";
                        }
                    } else {
                        $messages[] = "إصلاحات RTL موجودة بالفعل في ملف CSS الرئيسي.";
                    }
                } else {
                    $success = false;
                    $messages[] = "ملف CSS الرئيسي غير قابل للقراءة!";
                }
            } else {
                $success = false;
                $messages[] = "ملف CSS الرئيسي غير موجود!";
            }
        } else {
            $success = false;
            $messages[] = "مجلد CSS غير موجود!";
        }
        
        // Display messages
        foreach ($messages as $message) {
            echo '<p>' . $message . '</p>';
        }
        
        echo '</div>';
        
        // Step 2: Fix JavaScript files
        echo '<div class="step">
            <h3>الخطوة 2: إصلاح ملفات JavaScript</h3>';
        
        $messages = [];
        
        // Check if JS directory exists
        $jsDir = $rootDir . '/assets/js';
        if (file_exists($jsDir)) {
            echo '<p>مجلد JavaScript موجود: ' . $jsDir . '</p>';
            
            // Check main JS file
            $mainJsFile = $jsDir . '/main.js';
            if (file_exists($mainJsFile)) {
                echo '<p>ملف JavaScript الرئيسي موجود: ' . $mainJsFile . '</p>';
                
                // Make sure it's readable
                if (is_readable($mainJsFile)) {
                    echo '<p>ملف JavaScript الرئيسي قابل للقراءة.</p>';
                } else {
                    $success = false;
                    $messages[] = "ملف JavaScript الرئيسي غير قابل للقراءة!";
                }
            } else {
                $success = false;
                $messages[] = "ملف JavaScript الرئيسي غير موجود!";
            }
        } else {
            $success = false;
            $messages[] = "مجلد JavaScript غير موجود!";
        }
        
        // Display messages
        foreach ($messages as $message) {
            echo '<p>' . $message . '</p>';
        }
        
        echo '</div>';
        
        // Step 3: Fix header and footer files
        echo '<div class="step">
            <h3>الخطوة 3: إصلاح ملفات الهيدر والفوتر</h3>';
        
        $messages = [];
        
        // Check header file
        $headerFile = $rootDir . '/includes/header.php';
        if (file_exists($headerFile)) {
            echo '<p>ملف الهيدر موجود: ' . $headerFile . '</p>';
            
            // Make sure it's readable
            if (is_readable($headerFile)) {
                echo '<p>ملف الهيدر قابل للقراءة.</p>';
                
                // Fix header file
                $headerContent = file_get_contents($headerFile);
                
                // Make sure charset is UTF-8
                if (strpos($headerContent, 'charset=utf-8') === false) {
                    $headerContent = preg_replace('/<meta charset="[^"]*"/', '<meta charset="utf-8"', $headerContent);
                    
                    if (file_put_contents($headerFile, $headerContent)) {
                        $messages[] = "تم إصلاح ترميز UTF-8 في ملف الهيدر.";
                    } else {
                        $success = false;
                        $messages[] = "فشل في إصلاح ترميز UTF-8 في ملف الهيدر.";
                    }
                } else {
                    $messages[] = "ترميز UTF-8 موجود بالفعل في ملف الهيدر.";
                }
                
                // Make sure RTL is set
                if (strpos($headerContent, 'dir="rtl"') === false) {
                    $headerContent = preg_replace('/<html[^>]*>/', '<html lang="ar" dir="rtl">', $headerContent);
                    
                    if (file_put_contents($headerFile, $headerContent)) {
                        $messages[] = "تم إضافة اتجاه RTL إلى ملف الهيدر.";
                    } else {
                        $success = false;
                        $messages[] = "فشل في إضافة اتجاه RTL إلى ملف الهيدر.";
                    }
                } else {
                    $messages[] = "اتجاه RTL موجود بالفعل في ملف الهيدر.";
                }
            } else {
                $success = false;
                $messages[] = "ملف الهيدر غير قابل للقراءة!";
            }
        } else {
            $success = false;
            $messages[] = "ملف الهيدر غير موجود!";
        }
        
        // Display messages
        foreach ($messages as $message) {
            echo '<p>' . $message . '</p>';
        }
        
        echo '</div>';
        
        // Final result
        if ($success) {
            echo '<div class="alert-success">
                <h2>تم إصلاح مشاكل الواجهة الرسومية بنجاح!</h2>
                <p>يمكنك الآن استخدام النظام بشكل طبيعي.</p>
                <p><a href="index.php" class="btn btn-primary">الذهاب إلى الصفحة الرئيسية</a></p>
            </div>';
        } else {
            echo '<div class="alert-warning">
                <h2>تم إصلاح بعض مشاكل الواجهة الرسومية، لكن قد تكون هناك مشاكل أخرى!</h2>
                <p>يرجى مراجعة الرسائل أعلاه ومحاولة إصلاح المشاكل المتبقية يدويًا.</p>
                <p><a href="index.php" class="btn btn-primary">الذهاب إلى الصفحة الرئيسية</a></p>
            </div>';
        }
    }
}

// Display fix form
echo '<h2>إصلاح مشاكل الواجهة الرسومية</h2>
<p>انقر على الزر أدناه لإصلاح مشاكل الواجهة الرسومية والقوائم:</p>
<form method="post">
    <button type="submit" name="fix_ui" class="btn btn-primary">إصلاح مشاكل الواجهة الرسومية</button>
</form>

<h2>المشاكل الشائعة وحلولها</h2>
<table>
    <tr>
        <th>المشكلة</th>
        <th>الحل</th>
    </tr>
    <tr>
        <td>النصوص العربية تظهر بشكل غير صحيح</td>
        <td>تأكد من أن ترميز الصفحة هو UTF-8 وأن اتجاه الصفحة هو RTL</td>
    </tr>
    <tr>
        <td>القوائم تظهر بشكل غير صحيح</td>
        <td>تأكد من تحميل ملفات CSS و JavaScript بشكل صحيح</td>
    </tr>
    <tr>
        <td>الأزرار والحقول غير متناسقة</td>
        <td>تأكد من تحميل Bootstrap بشكل صحيح وإضافة إصلاحات RTL</td>
    </tr>
    <tr>
        <td>الصور لا تظهر</td>
        <td>تأكد من أن مسارات الصور صحيحة ومن وجود الصور في المجلدات الصحيحة</td>
    </tr>
    <tr>
        <td>الروابط لا تعمل</td>
        <td>تأكد من أن الروابط تستخدم مسارات نسبية وليست مطلقة</td>
    </tr>
</table>

<h2>روابط مفيدة</h2>
<ul>
    <li><a href="fix_paths.php">إصلاح المسارات والإعدادات</a></li>
    <li><a href="test_connection.php">اختبار الاتصال بقاعدة البيانات</a></li>
    <li><a href="index.php">الصفحة الرئيسية</a></li>
</ul>

</div>
</body>
</html>';
?>
