<?php
/**
 * PHP Backup and Restore Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include header
require_once 'includes/header.php';

// Require login and admin role
requireRole(['admin']);

// Set maximum execution time to 5 minutes
ini_set('max_execution_time', 300);
// Set memory limit to 512MB
ini_set('memory_limit', '512M');

// Create backup directory if it doesn't exist
$backupDir = 'backups';
if (!file_exists($backupDir)) {
    mkdir($backupDir, 0755, true);
}

// Process backup request
if (isset($_POST['create_backup'])) {
    try {
        // Generate backup filename with timestamp
        $timestamp = date('Y-m-d_H-i-s');
        $backupFilename = 'backup_' . $timestamp . '.sql';
        $backupPath = $backupDir . '/' . $backupFilename;
        
        // Create backup file
        $handle = fopen($backupPath, 'w');
        if (!$handle) {
            throw new Exception('فشل في إنشاء ملف النسخة الاحتياطية');
        }
        
        // Write header
        fwrite($handle, "-- نظام إدارة العلاوات والترفيع وكتب الشكر - نسخة احتياطية\n");
        fwrite($handle, "-- تاريخ النسخ الاحتياطي: " . date('Y-m-d H:i:s') . "\n\n");
        fwrite($handle, "SET FOREIGN_KEY_CHECKS=0;\n");
        fwrite($handle, "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n");
        fwrite($handle, "SET AUTOCOMMIT = 0;\n");
        fwrite($handle, "START TRANSACTION;\n");
        fwrite($handle, "SET time_zone = \"+00:00\";\n\n");
        
        // Get all tables
        $tables = [];
        $result = $pdo->query("SHOW TABLES");
        while ($row = $result->fetch(PDO::FETCH_NUM)) {
            $tables[] = $row[0];
        }
        
        // Export each table structure and data
        foreach ($tables as $table) {
            // Get table structure
            $result = $pdo->query("SHOW CREATE TABLE `$table`");
            $row = $result->fetch(PDO::FETCH_NUM);
            fwrite($handle, "DROP TABLE IF EXISTS `$table`;\n");
            fwrite($handle, $row[1] . ";\n\n");
            
            // Get table data
            $result = $pdo->query("SELECT * FROM `$table`");
            $rows = $result->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($rows) > 0) {
                // Get column names
                $columns = array_keys($rows[0]);
                $columnList = '`' . implode('`, `', $columns) . '`';
                
                // Insert data in batches
                $batchSize = 100;
                $batchCount = ceil(count($rows) / $batchSize);
                
                for ($i = 0; $i < $batchCount; $i++) {
                    $batchRows = array_slice($rows, $i * $batchSize, $batchSize);
                    
                    if (!empty($batchRows)) {
                        fwrite($handle, "INSERT INTO `$table` ($columnList) VALUES\n");
                        
                        $rowCount = count($batchRows);
                        foreach ($batchRows as $j => $row) {
                            $values = [];
                            foreach ($row as $value) {
                                if ($value === null) {
                                    $values[] = 'NULL';
                                } else {
                                    $values[] = $pdo->quote($value);
                                }
                            }
                            
                            fwrite($handle, "(" . implode(', ', $values) . ")");
                            if ($j < $rowCount - 1) {
                                fwrite($handle, ",\n");
                            } else {
                                fwrite($handle, ";\n\n");
                            }
                        }
                    }
                }
            }
        }
        
        // Write footer
        fwrite($handle, "SET FOREIGN_KEY_CHECKS=1;\n");
        fwrite($handle, "COMMIT;\n");
        
        // Close file
        fclose($handle);
        
        // Log activity
        logActivity(
            $_SESSION['user_id'],
            'إنشاء نسخة احتياطية',
            'backup',
            0,
            'تم إنشاء نسخة احتياطية: ' . $backupFilename
        );
        
        flash('success_message', 'تم إنشاء النسخة الاحتياطية بنجاح. <a href="' . $backupPath . '" class="alert-link" download>انقر هنا لتنزيل النسخة الاحتياطية</a>', 'alert alert-success');
    } catch (Exception $e) {
        error_log("Backup Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء إنشاء النسخة الاحتياطية: ' . $e->getMessage(), 'alert alert-danger');
    }
}

// Process delete backup request
if (isset($_POST['delete_backup']) && isset($_POST['backup_file'])) {
    try {
        $backupFile = $backupDir . '/' . basename($_POST['backup_file']);
        
        // Check if file exists and is within the backup directory
        if (file_exists($backupFile) && strpos($backupFile, $backupDir) === 0) {
            // Delete the file
            if (unlink($backupFile)) {
                // Log activity
                logActivity(
                    $_SESSION['user_id'],
                    'حذف نسخة احتياطية',
                    'backup',
                    0,
                    'تم حذف النسخة الاحتياطية: ' . basename($backupFile)
                );
                
                flash('success_message', 'تم حذف النسخة الاحتياطية بنجاح', 'alert alert-success');
            } else {
                throw new Exception('فشل في حذف ملف النسخة الاحتياطية');
            }
        } else {
            throw new Exception('ملف النسخة الاحتياطية غير موجود أو غير صالح');
        }
    } catch (Exception $e) {
        error_log("Delete Backup Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء حذف النسخة الاحتياطية: ' . $e->getMessage(), 'alert alert-danger');
    }
}

// Process restore request
if (isset($_POST['restore_backup']) && isset($_FILES['backup_file'])) {
    try {
        $uploadedFile = $_FILES['backup_file'];
        
        // Check for upload errors
        if ($uploadedFile['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('حدث خطأ أثناء رفع الملف. رمز الخطأ: ' . $uploadedFile['error']);
        }
        
        // Check file type
        $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));
        if ($fileExtension !== 'sql') {
            throw new Exception('نوع الملف غير صالح. يجب أن يكون الملف بتنسيق SQL.');
        }
        
        // Move uploaded file to backup directory
        $tempFile = $backupDir . '/temp_' . time() . '.sql';
        if (move_uploaded_file($uploadedFile['tmp_name'], $tempFile)) {
            // Read SQL file
            $sql = file_get_contents($tempFile);
            
            // Make sure we're using UTF-8
            $pdo->exec("SET NAMES utf8mb4");
            $pdo->exec("SET CHARACTER SET utf8mb4");
            $pdo->exec("SET COLLATION_CONNECTION = utf8mb4_unicode_ci");
            
            // Disable foreign key checks
            $pdo->exec("SET FOREIGN_KEY_CHECKS=0");
            
            // Split SQL file into individual queries
            $queries = preg_split('/;\s*$/m', $sql);
            
            // Execute each query
            foreach ($queries as $query) {
                $query = trim($query);
                if (!empty($query)) {
                    $pdo->exec($query);
                }
            }
            
            // Enable foreign key checks
            $pdo->exec("SET FOREIGN_KEY_CHECKS=1");
            
            // Log activity
            logActivity(
                $_SESSION['user_id'],
                'استعادة نسخة احتياطية',
                'backup',
                0,
                'تم استعادة النسخة الاحتياطية: ' . $uploadedFile['name']
            );
            
            flash('success_message', 'تم استعادة النسخة الاحتياطية بنجاح', 'alert alert-success');
            
            // Delete temporary file
            unlink($tempFile);
        } else {
            throw new Exception('فشل في نقل الملف المرفوع');
        }
    } catch (Exception $e) {
        error_log("Restore Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء استعادة النسخة الاحتياطية: ' . $e->getMessage(), 'alert alert-danger');
        
        // Delete temporary file if it exists
        if (isset($tempFile) && file_exists($tempFile)) {
            unlink($tempFile);
        }
    }
}

// Get list of backup files
$backupFiles = [];
if (file_exists($backupDir)) {
    foreach (glob($backupDir . '/*.sql') as $file) {
        $backupFiles[] = [
            'name' => basename($file),
            'size' => filesize($file),
            'date' => filemtime($file),
            'path' => $file
        ];
    }
    
    // Sort by date (newest first)
    usort($backupFiles, function($a, $b) {
        return $b['date'] - $a['date'];
    });
}

// Function to format file size
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $i = 0;
    while ($bytes >= 1024 && $i < count($units) - 1) {
        $bytes /= 1024;
        $i++;
    }
    return round($bytes, 2) . ' ' . $units[$i];
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-database me-2"></i> النسخ الاحتياطي
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى الرئيسية
        </a>
    </div>
</div>

<div class="row">
    <!-- Create Backup Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2"></i> إنشاء نسخة احتياطية
                </h5>
            </div>
            <div class="card-body">
                <p>قم بإنشاء نسخة احتياطية من قاعدة البيانات. يمكنك استخدام هذه النسخة لاستعادة البيانات في حالة حدوث مشكلة أو عند نقل البرنامج إلى حاسوب آخر.</p>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> تحتوي النسخة الاحتياطية على قاعدة البيانات بالكامل.
                </div>
                
                <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                    <button type="submit" name="create_backup" class="btn btn-primary w-100">
                        <i class="fas fa-download me-1"></i> إنشاء نسخة احتياطية
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Restore Backup Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-upload me-2"></i> استعادة نسخة احتياطية
                </h5>
            </div>
            <div class="card-body">
                <p>قم باستعادة قاعدة البيانات من نسخة احتياطية سابقة. سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.</p>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> استعادة النسخة الاحتياطية ستؤدي إلى حذف جميع البيانات الحالية واستبدالها بالبيانات من النسخة الاحتياطية.
                </div>
                
                <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" enctype="multipart/form-data" onsubmit="return confirm('هل أنت متأكد من رغبتك في استعادة هذه النسخة الاحتياطية؟ سيتم حذف جميع البيانات الحالية.');">
                    <div class="mb-3">
                        <label for="backup_file" class="form-label">اختر ملف النسخة الاحتياطية (SQL)</label>
                        <input type="file" class="form-control" id="backup_file" name="backup_file" accept=".sql" required>
                    </div>
                    <button type="submit" name="restore_backup" class="btn btn-warning w-100">
                        <i class="fas fa-upload me-1"></i> استعادة النسخة الاحتياطية
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Backup Files List -->
<div class="card mb-4">
    <div class="card-header bg-secondary text-white">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i> النسخ الاحتياطية المتوفرة
        </h5>
    </div>
    <div class="card-body">
        <?php if (count($backupFiles) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم الملف</th>
                            <th>تاريخ الإنشاء</th>
                            <th>حجم الملف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($backupFiles as $file): ?>
                            <tr>
                                <td><?php echo $file['name']; ?></td>
                                <td><?php echo date('Y-m-d H:i:s', $file['date']); ?></td>
                                <td><?php echo formatFileSize($file['size']); ?></td>
                                <td>
                                    <a href="<?php echo $file['path']; ?>" class="btn btn-sm btn-info" download data-bs-toggle="tooltip" title="تنزيل">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" class="d-inline" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذه النسخة الاحتياطية؟')">
                                        <input type="hidden" name="delete_backup" value="1">
                                        <input type="hidden" name="backup_file" value="<?php echo $file['name']; ?>">
                                        <button type="submit" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد نسخ احتياطية متوفرة.
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
