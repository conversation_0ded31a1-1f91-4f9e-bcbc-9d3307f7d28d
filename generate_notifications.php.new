<?php
/**
 * Generate Notifications Script
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 *
 * This script has been replaced by new_notifications_generator.php
 */

// Set character encoding
header('Content-Type: text/html; charset=utf-8');

// Include configuration and database connection
require_once 'config/config.php';
require_once 'config/database.php';

// Start HTML output
echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>توليد التنبيهات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .btn { display: inline-block; margin: 10px 0; padding: 8px 15px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>توليد التنبيهات</h1>
    <div class='warning'>
        <h2>تنبيه: تم استبدال هذه الصفحة</h2>
        <p>تم استبدال هذه الصفحة بصفحة جديدة لتوليد التنبيهات. يرجى استخدام الصفحة الجديدة بدلاً من ذلك.</p>
        <a href='new_notifications_generator.php' class='btn'>الانتقال إلى صفحة توليد التنبيهات الجديدة</a>
    </div>
</body>
</html>";
?>
