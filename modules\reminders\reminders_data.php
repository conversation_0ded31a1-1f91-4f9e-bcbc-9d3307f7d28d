<?php
/**
 * Reminders Data Module
 * وحدة بيانات التذكيرات
 */

// Initialize reminders array
$reminders = [];

try {
    // Check if reminders table exists
    $remindersTableCheck = $pdo->query("SHOW TABLES LIKE 'reminders'");
    
    if ($remindersTableCheck->rowCount() > 0) {
        // Get reminders for current user
        $stmt = $pdo->prepare("
            SELECT r.*, u.full_name as created_by_name
            FROM reminders r
            LEFT JOIN users u ON r.created_by = u.id
            WHERE r.user_id = :user_id OR r.user_id IS NULL
            ORDER BY r.is_completed ASC, r.reminder_date ASC
            LIMIT 50
        ");
        $stmt->execute([':user_id' => $_SESSION['user_id']]);
        $reminders = $stmt->fetchAll();
    } else {
        // Create dummy reminders for demonstration
        $reminders = [
            [
                'id' => 1,
                'title' => 'مراجعة العلاوات المستحقة',
                'description' => 'مراجعة قائمة الموظفين المستحقين للعلاوة وإعداد التقرير الشهري',
                'reminder_date' => date('Y-m-d', strtotime('+1 day')),
                'reminder_time' => '09:00:00',
                'priority' => 'high',
                'category' => 'allowances',
                'is_completed' => 0,
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'created_by_name' => 'مدير الموارد البشرية'
            ],
            [
                'id' => 2,
                'title' => 'إعداد تقرير الترفيعات',
                'description' => 'إعداد التقرير الربع سنوي للترفيعات وإرساله للإدارة العليا',
                'reminder_date' => date('Y-m-d', strtotime('+3 days')),
                'reminder_time' => '14:00:00',
                'priority' => 'medium',
                'category' => 'promotions',
                'is_completed' => 0,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'created_by_name' => 'النظام'
            ],
            [
                'id' => 3,
                'title' => 'متابعة كتب الشكر',
                'description' => 'متابعة إصدار كتب الشكر المعلقة والتأكد من إرسالها للموظفين',
                'reminder_date' => date('Y-m-d', strtotime('+1 week')),
                'reminder_time' => '10:30:00',
                'priority' => 'low',
                'category' => 'appreciation',
                'is_completed' => 0,
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'created_by_name' => 'مساعد إداري'
            ],
            [
                'id' => 4,
                'title' => 'تحديث بيانات الموظفين',
                'description' => 'تحديث بيانات الموظفين الجدد وإدخال معلومات التعيين',
                'reminder_date' => date('Y-m-d', strtotime('-1 day')),
                'reminder_time' => '11:00:00',
                'priority' => 'high',
                'category' => 'employees',
                'is_completed' => 1,
                'completed_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'created_at' => date('Y-m-d H:i:s', strtotime('-5 days')),
                'created_by_name' => 'مدير النظام'
            ],
            [
                'id' => 5,
                'title' => 'اجتماع مراجعة الأداء',
                'description' => 'اجتماع شهري لمراجعة أداء النظام ومناقشة التحسينات المطلوبة',
                'reminder_date' => date('Y-m-d', strtotime('+2 weeks')),
                'reminder_time' => '15:00:00',
                'priority' => 'medium',
                'category' => 'meetings',
                'is_completed' => 0,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'created_by_name' => 'مدير عام'
            ]
        ];
    }

    // Get reminder statistics
    $totalReminders = count($reminders);
    $pendingReminders = 0;
    $completedReminders = 0;
    $overdueReminders = 0;
    $todayReminders = 0;
    $priorityStats = [
        'high' => 0,
        'medium' => 0,
        'low' => 0
    ];
    $categoryStats = [
        'allowances' => 0,
        'promotions' => 0,
        'appreciation' => 0,
        'employees' => 0,
        'meetings' => 0,
        'reports' => 0,
        'system' => 0
    ];

    foreach ($reminders as $reminder) {
        // Count by status
        if ($reminder['is_completed']) {
            $completedReminders++;
        } else {
            $pendingReminders++;
            
            // Check if overdue
            $reminderDateTime = $reminder['reminder_date'] . ' ' . $reminder['reminder_time'];
            if (strtotime($reminderDateTime) < time()) {
                $overdueReminders++;
            }
        }

        // Count today's reminders
        if ($reminder['reminder_date'] === date('Y-m-d')) {
            $todayReminders++;
        }

        // Count by priority
        $priority = $reminder['priority'] ?? 'medium';
        if (isset($priorityStats[$priority])) {
            $priorityStats[$priority]++;
        }

        // Count by category
        $category = $reminder['category'] ?? 'system';
        if (isset($categoryStats[$category])) {
            $categoryStats[$category]++;
        } else {
            $categoryStats['system']++;
        }
    }

} catch (PDOException $e) {
    error_log("Get Reminders Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع التذكيرات', 'alert alert-danger');
    $reminders = [];
    $totalReminders = 0;
    $pendingReminders = 0;
    $completedReminders = 0;
    $overdueReminders = 0;
    $todayReminders = 0;
    $priorityStats = [];
    $categoryStats = [];
}

// Handle reminder actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = sanitize($_POST['action']);
    
    try {
        switch ($action) {
            case 'complete':
                if (isset($_POST['reminder_id'])) {
                    $reminderId = (int)$_POST['reminder_id'];
                    
                    // Check if reminders table exists
                    $tableCheck = $pdo->query("SHOW TABLES LIKE 'reminders'");
                    if ($tableCheck->rowCount() > 0) {
                        $stmt = $pdo->prepare("
                            UPDATE reminders 
                            SET is_completed = 1, completed_at = NOW() 
                            WHERE id = :id AND (user_id = :user_id OR user_id IS NULL)
                        ");
                        $stmt->execute([
                            ':id' => $reminderId,
                            ':user_id' => $_SESSION['user_id']
                        ]);
                        
                        flash('success_message', 'تم تحديد التذكير كمكتمل', 'alert alert-success');
                    }
                }
                break;
                
            case 'delete':
                if (isset($_POST['reminder_id'])) {
                    $reminderId = (int)$_POST['reminder_id'];
                    
                    // Check if reminders table exists
                    $tableCheck = $pdo->query("SHOW TABLES LIKE 'reminders'");
                    if ($tableCheck->rowCount() > 0) {
                        $stmt = $pdo->prepare("
                            DELETE FROM reminders 
                            WHERE id = :id AND (user_id = :user_id OR user_id IS NULL)
                        ");
                        $stmt->execute([
                            ':id' => $reminderId,
                            ':user_id' => $_SESSION['user_id']
                        ]);
                        
                        flash('success_message', 'تم حذف التذكير بنجاح', 'alert alert-success');
                    }
                }
                break;
                
            case 'create':
                $title = sanitize($_POST['title'] ?? '');
                $description = sanitize($_POST['description'] ?? '');
                $reminderDate = sanitize($_POST['reminder_date'] ?? '');
                $reminderTime = sanitize($_POST['reminder_time'] ?? '');
                $priority = sanitize($_POST['priority'] ?? 'medium');
                $category = sanitize($_POST['category'] ?? 'system');
                $targetUser = isset($_POST['target_user']) ? (int)$_POST['target_user'] : $_SESSION['user_id'];
                
                if (!empty($title) && !empty($reminderDate) && !empty($reminderTime)) {
                    // Check if reminders table exists, create if not
                    $tableCheck = $pdo->query("SHOW TABLES LIKE 'reminders'");
                    if ($tableCheck->rowCount() == 0) {
                        // Create reminders table
                        $createTable = "
                            CREATE TABLE reminders (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                user_id INT NULL,
                                title VARCHAR(255) NOT NULL,
                                description TEXT,
                                reminder_date DATE NOT NULL,
                                reminder_time TIME NOT NULL,
                                priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
                                category VARCHAR(50) DEFAULT 'system',
                                is_completed BOOLEAN DEFAULT FALSE,
                                completed_at TIMESTAMP NULL,
                                created_by INT NULL,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                INDEX idx_user_id (user_id),
                                INDEX idx_reminder_date (reminder_date),
                                INDEX idx_is_completed (is_completed)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                        ";
                        $pdo->exec($createTable);
                    }
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO reminders (user_id, title, description, reminder_date, reminder_time, priority, category, created_by, created_at)
                        VALUES (:user_id, :title, :description, :reminder_date, :reminder_time, :priority, :category, :created_by, NOW())
                    ");
                    $stmt->execute([
                        ':user_id' => $targetUser,
                        ':title' => $title,
                        ':description' => $description,
                        ':reminder_date' => $reminderDate,
                        ':reminder_time' => $reminderTime,
                        ':priority' => $priority,
                        ':category' => $category,
                        ':created_by' => $_SESSION['user_id']
                    ]);
                    
                    flash('success_message', 'تم إنشاء التذكير بنجاح', 'alert alert-success');
                } else {
                    flash('error_message', 'يرجى ملء جميع الحقول المطلوبة', 'alert alert-danger');
                }
                break;
        }
        
        // Redirect to avoid form resubmission
        header('Location: ' . $_SERVER['PHP_SELF'] . '?tab=reminders');
        exit();
        
    } catch (PDOException $e) {
        error_log("Reminder Action Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء تنفيذ العملية', 'alert alert-danger');
    }
}
?>
