/**
 * Color Fix CSS
 * إصلاح مشاكل الألوان والنصوص غير المرئية
 */

/* إصلاح الألوان الأساسية */
body {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

/* إصلاح ألوان النصوص */
h1, h2, h3, h4, h5, h6 {
    color: #212529 !important;
}

p, span, div, li {
    color: #212529 !important;
}

/* إصلاح ألوان البطاقات */
.card {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.card-header {
    color: #ffffff !important;
}

.card-body {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.card-title {
    color: #212529 !important;
}

.card-text {
    color: #212529 !important;
}

/* إصلاح ألوان الجداول */
.table {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.table th {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

.table td {
    color: #212529 !important;
}

/* إصلاح ألوان النماذج */
.form-label {
    color: #212529 !important;
}

.form-control {
    background-color: #ffffff !important;
    color: #212529 !important;
    border: 1px solid #ced4da !important;
}

.input-group-text {
    background-color: #e9ecef !important;
    color: #495057 !important;
    border: 1px solid #ced4da !important;
}

/* إصلاح ألوان الأزرار */
.btn-primary {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: #ffffff !important;
}

.btn-success {
    background-color: #198754 !important;
    border-color: #198754 !important;
    color: #ffffff !important;
}

.btn-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #000000 !important;
}

.btn-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: #ffffff !important;
}

.btn-secondary {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: #ffffff !important;
}

/* إصلاح ألوان التنبيهات */
.alert-success {
    background-color: #d1e7dd !important;
    color: #0f5132 !important;
    border-color: #badbcc !important;
}

.alert-danger {
    background-color: #f8d7da !important;
    color: #842029 !important;
    border-color: #f5c2c7 !important;
}

.alert-warning {
    background-color: #fff3cd !important;
    color: #664d03 !important;
    border-color: #ffecb5 !important;
}

.alert-info {
    background-color: #d1ecf1 !important;
    color: #055160 !important;
    border-color: #b6d4ea !important;
}

/* إصلاح ألوان بطاقات لوحة المعلومات */
.dashboard-card {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.dashboard-card .count {
    color: #212529 !important;
}

.dashboard-card .title {
    color: #6c757d !important;
}

.dashboard-card .icon {
    color: #0d6efd !important;
}

.dashboard-card.success .icon {
    color: #198754 !important;
}

.dashboard-card.warning .icon {
    color: #ffc107 !important;
}

.dashboard-card.danger .icon {
    color: #dc3545 !important;
}

/* إصلاح ألوان شريط التنقل المحسن */
.modern-navbar {
    background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    border-bottom: 3px solid #3b82f6 !important;
    overflow: visible !important;
    position: relative !important;
    z-index: 1030 !important;
}

.navbar .container,
.navbar .container-fluid {
    overflow: visible !important;
    position: relative !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85) !important;
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link.active {
    color: #ffffff !important;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(29, 78, 216, 0.3)) !important;
}

.navbar-brand {
    color: #ffffff !important;
}

.brand-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;
}

.navbar-toggler {
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.85%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

/* إصلاح ألوان القوائم المنسدلة */
.dropdown-menu {
    background-color: rgba(255, 255, 255, 0.98) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
    backdrop-filter: blur(10px) !important;
    z-index: 9999 !important;
    position: absolute !important;
    min-width: 200px !important;
    padding: 0.5rem !important;
    display: none !important;
    top: 100% !important;
    margin-top: 0.5rem !important;
}

.dropdown-menu.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* إصلاح موضع القوائم المنسدلة خارج شريط التنقل */
.navbar .dropdown {
    position: relative !important;
}

.navbar .dropdown-menu {
    position: absolute !important;
    top: calc(100% + 0.5rem) !important;
    transform: none !important;
    margin-top: 0 !important;
}

.dropdown-item {
    color: #374151 !important;
    padding: 0.75rem 1rem !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    margin-bottom: 0.25rem !important;
    text-decoration: none !important;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
    color: white !important;
    transform: translateX(4px) !important;
}

.dropdown-item i {
    margin-left: 0.5rem !important;
    color: #3b82f6 !important;
    transition: color 0.3s ease !important;
}

.dropdown-item:hover i {
    color: white !important;
}

/* إصلاح قائمة الإدارة تحديداً */
#adminDropdown + .dropdown-menu {
    right: 0 !important;
    left: auto !important;
}

/* إصلاح قائمة المستخدم */
#userDropdown + .dropdown-menu {
    right: 0 !important;
    left: auto !important;
}

/* إصلاح قائمة الإشعارات */
#notificationsContainer {
    right: 0 !important;
    left: auto !important;
    width: 320px !important;
    max-height: 400px !important;
    overflow-y: auto !important;
}

/* تأكيد ظهور القوائم عند التمرير */
.navbar .dropdown:hover .dropdown-menu {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* إصلاح مشكلة الـ z-index */
.navbar {
    z-index: 1030 !important;
}

.dropdown-menu {
    z-index: 9999 !important;
}

/* إصلاح ألوان الشارات */
.badge {
    color: #ffffff !important;
}

.badge.bg-success {
    background-color: #198754 !important;
    color: #ffffff !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000000 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
}

.badge.bg-primary {
    background-color: #0d6efd !important;
    color: #ffffff !important;
}

/* إصلاح النصوص المساعدة */
.text-muted {
    color: #6c757d !important;
}

.text-primary {
    color: #0d6efd !important;
}

.text-success {
    color: #198754 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* إصلاح ألوان الروابط */
a {
    color: #0d6efd !important;
}

a:hover {
    color: #0a58ca !important;
}

/* إصلاح ألوان الحدود */
.border {
    border-color: #dee2e6 !important;
}

/* إصلاح خلفيات الصفحات */
.container, .container-fluid {
    background-color: transparent !important;
}

/* إصلاح ألوان التذييل */
footer {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

/* إصلاح ألوان المودال */
.modal-content {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.modal-header {
    background-color: #0d6efd !important;
    color: #ffffff !important;
}

.modal-body {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.modal-footer {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

/* إصلاح ألوان التبويبات */
.nav-tabs .nav-link {
    color: #495057 !important;
}

.nav-tabs .nav-link.active {
    color: #495057 !important;
    background-color: #ffffff !important;
}

/* إصلاح ألوان التصفح */
.pagination .page-link {
    color: #0d6efd !important;
    background-color: #ffffff !important;
    border-color: #dee2e6 !important;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: #ffffff !important;
}
