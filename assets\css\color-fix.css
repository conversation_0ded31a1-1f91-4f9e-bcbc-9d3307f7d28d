/**
 * Color Fix CSS
 * إصلاح مشاكل الألوان والنصوص غير المرئية
 */

/* إصلاح الألوان الأساسية */
body {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

/* إصلاح ألوان النصوص */
h1, h2, h3, h4, h5, h6 {
    color: #212529 !important;
}

p, span, div, li {
    color: #212529 !important;
}

/* إصلاح ألوان البطاقات */
.card {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.card-header {
    color: #ffffff !important;
}

.card-body {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.card-title {
    color: #212529 !important;
}

.card-text {
    color: #212529 !important;
}

/* إصلاح ألوان الجداول */
.table {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.table th {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

.table td {
    color: #212529 !important;
}

/* إصلاح ألوان النماذج */
.form-label {
    color: #212529 !important;
}

.form-control {
    background-color: #ffffff !important;
    color: #212529 !important;
    border: 1px solid #ced4da !important;
}

.input-group-text {
    background-color: #e9ecef !important;
    color: #495057 !important;
    border: 1px solid #ced4da !important;
}

/* إصلاح ألوان الأزرار */
.btn-primary {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: #ffffff !important;
}

.btn-success {
    background-color: #198754 !important;
    border-color: #198754 !important;
    color: #ffffff !important;
}

.btn-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #000000 !important;
}

.btn-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: #ffffff !important;
}

.btn-secondary {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: #ffffff !important;
}

/* إصلاح ألوان التنبيهات */
.alert-success {
    background-color: #d1e7dd !important;
    color: #0f5132 !important;
    border-color: #badbcc !important;
}

.alert-danger {
    background-color: #f8d7da !important;
    color: #842029 !important;
    border-color: #f5c2c7 !important;
}

.alert-warning {
    background-color: #fff3cd !important;
    color: #664d03 !important;
    border-color: #ffecb5 !important;
}

.alert-info {
    background-color: #d1ecf1 !important;
    color: #055160 !important;
    border-color: #b6d4ea !important;
}

/* إصلاح ألوان بطاقات لوحة المعلومات */
.dashboard-card {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.dashboard-card .count {
    color: #212529 !important;
}

.dashboard-card .title {
    color: #6c757d !important;
}

.dashboard-card .icon {
    color: #0d6efd !important;
}

.dashboard-card.success .icon {
    color: #198754 !important;
}

.dashboard-card.warning .icon {
    color: #ffc107 !important;
}

.dashboard-card.danger .icon {
    color: #dc3545 !important;
}

/* إصلاح ألوان شريط التنقل */
.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #ffffff !important;
}

.navbar-brand {
    color: #ffffff !important;
}

/* إصلاح ألوان القوائم المنسدلة */
.dropdown-menu {
    background-color: #ffffff !important;
}

.dropdown-item {
    color: #212529 !important;
}

.dropdown-item:hover {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

/* إصلاح ألوان الشارات */
.badge {
    color: #ffffff !important;
}

.badge.bg-success {
    background-color: #198754 !important;
    color: #ffffff !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000000 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
}

.badge.bg-primary {
    background-color: #0d6efd !important;
    color: #ffffff !important;
}

/* إصلاح النصوص المساعدة */
.text-muted {
    color: #6c757d !important;
}

.text-primary {
    color: #0d6efd !important;
}

.text-success {
    color: #198754 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* إصلاح ألوان الروابط */
a {
    color: #0d6efd !important;
}

a:hover {
    color: #0a58ca !important;
}

/* إصلاح ألوان الحدود */
.border {
    border-color: #dee2e6 !important;
}

/* إصلاح خلفيات الصفحات */
.container, .container-fluid {
    background-color: transparent !important;
}

/* إصلاح ألوان التذييل */
footer {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

/* إصلاح ألوان المودال */
.modal-content {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.modal-header {
    background-color: #0d6efd !important;
    color: #ffffff !important;
}

.modal-body {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.modal-footer {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

/* إصلاح ألوان التبويبات */
.nav-tabs .nav-link {
    color: #495057 !important;
}

.nav-tabs .nav-link.active {
    color: #495057 !important;
    background-color: #ffffff !important;
}

/* إصلاح ألوان التصفح */
.pagination .page-link {
    color: #0d6efd !important;
    background-color: #ffffff !important;
    border-color: #dee2e6 !important;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: #ffffff !important;
}
