<?php
/**
 * Appreciation Letters List Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();

// Get filter parameters
$department = isset($_GET['department']) ? (int)$_GET['department'] : 0;
$issuer = isset($_GET['issuer']) ? sanitize($_GET['issuer']) : 'all';
$year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build query
$query = "
    SELECT a.*, e.employee_number, e.full_name, e.current_grade, d.name as department_name, u.full_name as created_by_name
    FROM appreciation_letters a
    JOIN employees e ON a.employee_id = e.id
    JOIN departments d ON e.department_id = d.id
    JOIN users u ON a.created_by = u.id
    WHERE 1=1
";

$params = [];

if ($department > 0) {
    $query .= " AND e.department_id = :department";
    $params[':department'] = $department;
}

if ($issuer !== 'all') {
    $query .= " AND a.issuer = :issuer";
    $params[':issuer'] = $issuer;
}

if ($year > 0) {
    $query .= " AND YEAR(a.letter_date) = :year";
    $params[':year'] = $year;
}

if (!empty($search)) {
    $query .= " AND (e.full_name LIKE :search OR e.employee_number LIKE :search OR a.letter_number LIKE :search)";
    $params[':search'] = "%$search%";
}

$query .= " ORDER BY a.letter_date DESC, e.full_name ASC";

// Get departments for filter
try {
    $deptStmt = $pdo->query("SELECT id, name FROM departments ORDER BY name ASC");
    $departments = $deptStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Departments Error: " . $e->getMessage());
    $departments = [];
}

// Get years for filter
try {
    $yearStmt = $pdo->query("SELECT DISTINCT YEAR(letter_date) as year FROM appreciation_letters ORDER BY year DESC");
    $years = $yearStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Years Error: " . $e->getMessage());
    $years = [];
}

// Get appreciation letters
try {
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $letters = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Appreciation Letters Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات كتب الشكر', 'alert alert-danger');
    $letters = [];
}

// Get statistics
try {
    // Total letters
    $totalStmt = $pdo->query("SELECT COUNT(*) as count FROM appreciation_letters");
    $totalCount = $totalStmt->fetch()['count'];
    
    // Regular letters
    $regularStmt = $pdo->query("SELECT COUNT(*) as count FROM appreciation_letters WHERE issuer = 'regular'");
    $regularCount = $regularStmt->fetch()['count'];
    
    // Prime minister letters
    $pmStmt = $pdo->query("SELECT COUNT(*) as count FROM appreciation_letters WHERE issuer = 'prime_minister'");
    $pmCount = $pmStmt->fetch()['count'];
    
    // Current year letters
    $currentYearStmt = $pdo->prepare("SELECT COUNT(*) as count FROM appreciation_letters WHERE YEAR(letter_date) = :year");
    $currentYearStmt->execute([':year' => date('Y')]);
    $currentYearCount = $currentYearStmt->fetch()['count'];
    
} catch (PDOException $e) {
    error_log("Get Statistics Error: " . $e->getMessage());
    $totalCount = $regularCount = $pmCount = $currentYearCount = 0;
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-certificate me-2"></i> إدارة كتب الشكر
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <?php if (hasRole(['admin', 'hr'])): ?>
            <a href="add_appreciation.php" class="btn btn-warning">
                <i class="fas fa-plus me-1"></i> إضافة كتاب شكر
            </a>
        <?php endif; ?>
        <button class="btn btn-outline-secondary btn-print">
            <i class="fas fa-print me-1"></i> طباعة
        </button>
        <button class="btn btn-outline-success btn-export" data-table-target="#appreciationTable" data-filename="appreciation_letters">
            <i class="fas fa-file-excel me-1"></i> تصدير
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card">
            <div class="icon text-primary">
                <i class="fas fa-certificate"></i>
            </div>
            <div class="count"><?php echo $totalCount; ?></div>
            <div class="title">إجمالي كتب الشكر</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card">
            <div class="icon text-success">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="count"><?php echo $regularCount; ?></div>
            <div class="title">كتب الشكر العادية</div>
            <a href="?issuer=regular" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card">
            <div class="icon text-warning">
                <i class="fas fa-award"></i>
            </div>
            <div class="count"><?php echo $pmCount; ?></div>
            <div class="title">كتب شكر رئيس الوزراء</div>
            <a href="?issuer=prime_minister" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card">
            <div class="icon text-info">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="count"><?php echo $currentYearCount; ?></div>
            <div class="title">كتب شكر العام الحالي</div>
            <a href="?year=<?php echo date('Y'); ?>" class="stretched-link"></a>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="get" class="row g-3">
            <div class="col-md-3">
                <select class="form-select" name="department">
                    <option value="0">جميع الأقسام</option>
                    <?php foreach ($departments as $dept): ?>
                        <option value="<?php echo $dept['id']; ?>" <?php echo ($department == $dept['id']) ? 'selected' : ''; ?>>
                            <?php echo $dept['name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" name="issuer">
                    <option value="all" <?php echo ($issuer === 'all') ? 'selected' : ''; ?>>جميع الجهات</option>
                    <option value="regular" <?php echo ($issuer === 'regular') ? 'selected' : ''; ?>>كتب شكر عادية</option>
                    <option value="prime_minister" <?php echo ($issuer === 'prime_minister') ? 'selected' : ''; ?>>كتب شكر رئيس الوزراء</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" name="year">
                    <option value="0">جميع السنوات</option>
                    <?php foreach ($years as $y): ?>
                        <option value="<?php echo $y['year']; ?>" <?php echo ($year == $y['year']) ? 'selected' : ''; ?>>
                            <?php echo $y['year']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" name="search" placeholder="بحث" value="<?php echo $search; ?>">
                </div>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">تطبيق</button>
            </div>
        </form>
    </div>
</div>

<!-- Appreciation Letters Table -->
<div class="card">
    <div class="card-body">
        <?php if (count($letters) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover" id="appreciationTable">
                    <thead>
                        <tr>
                            <th>رقم الكتاب</th>
                            <th>تاريخ الكتاب</th>
                            <th>الموظف</th>
                            <th>القسم</th>
                            <th>الجهة المانحة</th>
                            <th>تخفيض المدة</th>
                            <th>تمت بواسطة</th>
                            <th>تاريخ الإضافة</th>
                            <th class="no-print">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($letters as $letter): ?>
                            <tr>
                                <td><?php echo $letter['letter_number']; ?></td>
                                <td><?php echo formatArabicDate($letter['letter_date']); ?></td>
                                <td>
                                    <a href="employee_details.php?id=<?php echo $letter['employee_id']; ?>">
                                        <?php echo $letter['full_name']; ?> (<?php echo $letter['employee_number']; ?>)
                                    </a>
                                </td>
                                <td><?php echo $letter['department_name']; ?></td>
                                <td>
                                    <?php echo $letter['issuer'] == 'prime_minister' ? 'رئيس الوزراء' : 'عادي'; ?>
                                </td>
                                <td><?php echo $letter['months_reduction']; ?> شهر</td>
                                <td><?php echo $letter['created_by_name']; ?></td>
                                <td><?php echo date('d/m/Y', strtotime($letter['created_at'])); ?></td>
                                <td class="no-print">
                                    <?php if ($letter['file_path']): ?>
                                        <a href="<?php echo $letter['file_path']; ?>" class="btn btn-sm btn-info" target="_blank" data-bs-toggle="tooltip" title="عرض الملف">
                                            <i class="fas fa-file"></i>
                                        </a>
                                    <?php endif; ?>
                                    
                                    <?php if (hasRole(['admin', 'hr'])): ?>
                                        <a href="edit_appreciation.php?id=<?php echo $letter['id']; ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="delete_appreciation.php?id=<?php echo $letter['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد بيانات للعرض
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
