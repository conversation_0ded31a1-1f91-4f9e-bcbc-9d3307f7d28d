-- حذف البيانات الموجودة في جدول المراحل
TRUNCATE TABLE `stages`;

-- إدخال بيانات المراحل والرواتب الاسمية
INSERT INTO `stages` (`grade_id`, `stage_number`, `nominal_salary`) VALUES
-- الدرجة 1
(1, 1, 910),
(1, 2, 940),
(1, 3, 970),
(1, 4, 1000),
(1, 5, 1030),
(1, 6, 1060),
(1, 7, 1100),
-- الدرجة 2
(2, 1, 723),
(2, 2, 753),
(2, 3, 783),
(2, 4, 813),
(2, 5, 843),
(2, 6, 873),
(2, 7, 893),
-- الدرجة 3
(3, 1, 600),
(3, 2, 620),
(3, 3, 640),
(3, 4, 660),
(3, 5, 680),
(3, 6, 700),
(3, 7, 710),
-- الدرجة 4
(4, 1, 509),
(4, 2, 519),
(4, 3, 529),
(4, 4, 539),
(4, 5, 559),
(4, 6, 579),
(4, 7, 589),
-- الدرجة 5
(5, 1, 429),
(5, 2, 439),
(5, 3, 449),
(5, 4, 459),
(5, 5, 469),
(5, 6, 479),
(5, 7, 489),
-- الدرجة 6
(6, 1, 362),
(6, 2, 372),
(6, 3, 382),
(6, 4, 392),
(6, 5, 402),
(6, 6, 422),
(6, 7, 432),
-- الدرجة 7
(7, 1, 292),
(7, 2, 302),
(7, 3, 312),
(7, 4, 322),
(7, 5, 332),
(7, 6, 346),
(7, 7, 356),
-- الدرجة 8
(8, 1, 250),
(8, 2, 255),
(8, 3, 260),
(8, 4, 270),
(8, 5, 275),
(8, 6, 285),
(8, 7, 290),
-- الدرجة 9
(9, 1, 192),
(9, 2, 202),
(9, 3, 212),
(9, 4, 222),
(9, 5, 227),
(9, 6, 237),
(9, 7, 242),
-- الدرجة 10
(10, 1, 160),
(10, 2, 165),
(10, 3, 170),
(10, 4, 175),
(10, 5, 180),
(10, 6, 186),
(10, 7, 191);
