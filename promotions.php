<?php
/**
 * Promotions List Page
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login
requireLogin();

// Get filter parameters
$status = isset($_GET['status']) ? sanitize($_GET['status']) : 'all';
$department = isset($_GET['department']) ? (int)$_GET['department'] : 0;
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build query
$query = "
    SELECT e.*, d.name as department_name, el.name as education_level_name, el.max_grade
    FROM employees e
    JOIN departments d ON e.department_id = d.id
    JOIN education_levels el ON e.education_level_id = el.id
    WHERE 1=1
";

$params = [];

if ($status === 'eligible') {
    $query .= " AND e.next_promotion_date <= CURDATE()";
} elseif ($status === 'upcoming') {
    $query .= " AND e.next_promotion_date > CURDATE() AND e.next_promotion_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)";
} elseif ($status === 'maxed') {
    $query .= " AND e.current_grade <= el.max_grade";
}

if ($department > 0) {
    $query .= " AND e.department_id = :department";
    $params[':department'] = $department;
}

if (!empty($search)) {
    $query .= " AND (e.full_name LIKE :search OR e.employee_number LIKE :search)";
    $params[':search'] = "%$search%";
}

$query .= " ORDER BY e.next_promotion_date ASC, e.full_name ASC";

// Get departments for filter
try {
    $deptStmt = $pdo->query("SELECT id, name FROM departments ORDER BY name ASC");
    $departments = $deptStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Departments Error: " . $e->getMessage());
    $departments = [];
}

// Get employees
try {
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $employees = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Employees Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء استرجاع بيانات الموظفين', 'alert alert-danger');
    $employees = [];
}

// Get statistics
try {
    // Eligible for promotion
    $eligibleStmt = $pdo->query("
        SELECT COUNT(*) as count FROM employees e
        JOIN education_levels el ON e.education_level_id = el.id
        WHERE e.current_grade > el.max_grade
        AND e.next_promotion_date <= CURDATE()
    ");
    $eligibleCount = $eligibleStmt->fetch()['count'];

    // Upcoming promotions
    $upcomingStmt = $pdo->query("
        SELECT COUNT(*) as count FROM employees e
        JOIN education_levels el ON e.education_level_id = el.id
        WHERE e.current_grade > el.max_grade
        AND e.next_promotion_date > CURDATE()
        AND e.next_promotion_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)
    ");
    $upcomingCount = $upcomingStmt->fetch()['count'];

    // Maxed out promotions
    $maxedStmt = $pdo->query("
        SELECT COUNT(*) as count FROM employees e
        JOIN education_levels el ON e.education_level_id = el.id
        WHERE e.current_grade <= el.max_grade
    ");
    $maxedCount = $maxedStmt->fetch()['count'];

} catch (PDOException $e) {
    error_log("Get Statistics Error: " . $e->getMessage());
    $eligibleCount = $upcomingCount = $maxedCount = 0;
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-level-up-alt me-2"></i> إدارة الترقيات
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <button class="btn btn-outline-secondary btn-print">
            <i class="fas fa-print me-1"></i> طباعة
        </button>
        <button class="btn btn-outline-success btn-export" data-table-target="#promotionsTable" data-filename="promotions">
            <i class="fas fa-file-excel me-1"></i> تصدير
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card dashboard-card">
            <div class="icon text-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="count"><?php echo $eligibleCount; ?></div>
            <div class="title">مستحقي الترقية حالياً</div>
            <a href="?status=eligible" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card dashboard-card">
            <div class="icon text-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="count"><?php echo $upcomingCount; ?></div>
            <div class="title">مستحقي الترقية خلال 30 يوم</div>
            <a href="?status=upcoming" class="stretched-link"></a>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card dashboard-card">
            <div class="icon text-danger">
                <i class="fas fa-ban"></i>
            </div>
            <div class="count"><?php echo $maxedCount; ?></div>
            <div class="title">وصلوا للحد الأقصى</div>
            <a href="?status=maxed" class="stretched-link"></a>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="get" class="row g-3">
            <div class="col-md-3">
                <select class="form-select" name="status">
                    <option value="all" <?php echo ($status === 'all') ? 'selected' : ''; ?>>جميع الموظفين</option>
                    <option value="eligible" <?php echo ($status === 'eligible') ? 'selected' : ''; ?>>المستحقين حالياً</option>
                    <option value="upcoming" <?php echo ($status === 'upcoming') ? 'selected' : ''; ?>>المستحقين خلال 30 يوم</option>
                    <option value="maxed" <?php echo ($status === 'maxed') ? 'selected' : ''; ?>>وصلوا للحد الأقصى</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="department">
                    <option value="0">جميع الأقسام</option>
                    <?php foreach ($departments as $dept): ?>
                        <option value="<?php echo $dept['id']; ?>" <?php echo ($department == $dept['id']) ? 'selected' : ''; ?>>
                            <?php echo $dept['name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" name="search" placeholder="بحث بالاسم أو الرقم الوظيفي" value="<?php echo $search; ?>">
                </div>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">تطبيق</button>
            </div>
        </form>
    </div>
</div>

<!-- Employees Table -->
<div class="card">
    <div class="card-body">
        <?php if (count($employees) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover" id="promotionsTable">
                    <thead>
                        <tr>
                            <th>الرقم الوظيفي</th>
                            <th>الاسم</th>
                            <th>الدرجة الحالية</th>
                            <th>القسم</th>
                            <th>التحصيل الدراسي</th>
                            <th>الحد الأقصى للدرجة</th>
                            <th>تاريخ آخر ترقية</th>
                            <th>تاريخ الترقية القادم</th>
                            <th>الحالة</th>
                            <th class="no-print">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($employees as $employee): ?>
                            <tr>
                                <td><?php echo $employee['employee_number']; ?></td>
                                <td><?php echo $employee['full_name']; ?></td>
                                <td><?php echo $employee['current_grade']; ?></td>
                                <td><?php echo $employee['department_name']; ?></td>
                                <td><?php echo $employee['education_level_name']; ?></td>
                                <td><?php echo $employee['max_grade']; ?></td>
                                <td><?php echo $employee['last_promotion_date'] ? formatArabicDate($employee['last_promotion_date']) : '-'; ?></td>
                                <td><?php echo formatArabicDate($employee['next_promotion_date']); ?></td>
                                <td>
                                    <?php if ($employee['current_grade'] <= $employee['max_grade']): ?>
                                        <span class="badge bg-danger">وصل للحد الأقصى</span>
                                    <?php elseif (isEligibleForPromotion($employee)): ?>
                                        <span class="badge bg-success">مستحق</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                    <?php endif; ?>
                                </td>
                                <td class="no-print">
                                    <a href="employee_details.php?id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if (hasRole(['admin', 'hr']) && $employee['current_grade'] > $employee['max_grade'] && isEligibleForPromotion($employee)): ?>
                                        <a href="add_promotion.php?employee_id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="إضافة ترقية">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد بيانات للعرض
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
