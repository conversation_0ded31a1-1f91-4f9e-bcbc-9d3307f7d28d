<?php
/**
 * Unified Reports, Analytics & Notifications Page
 * صفحة التقارير والتحليلات والتنبيهات الموحدة
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Include unified reports CSS
echo '<link rel="stylesheet" href="assets/css/unified-reports.css">';

// Require login
requireLogin();

// Get active tab
$activeTab = isset($_GET['tab']) ? sanitize($_GET['tab']) : 'reports';

// Get filter parameters
$reportType = isset($_GET['type']) ? sanitize($_GET['type']) : '';
$department = isset($_GET['department']) ? (int)$_GET['department'] : 0;
$year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');
$month = isset($_GET['month']) ? (int)$_GET['month'] : date('m');
$period = isset($_GET['period']) ? (int)$_GET['period'] : 30;
$grade = isset($_GET['grade']) ? (int)$_GET['grade'] : 0;

// Get departments for filters
try {
    $deptStmt = $pdo->query("SELECT id, name FROM departments ORDER BY name ASC");
    $departments = $deptStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Get Departments Error: " . $e->getMessage());
    $departments = [];
}

// Initialize variables
$reportData = [];
$reportTitle = '';
$totalEmployees = 0;
$employeesByGrade = [];
$employeesByEducation = [];
$allowancesByMonth = [];
$promotionsByMonth = [];
$appreciationByType = [];
$departmentStats = [];
$upcomingAllowances = [];
$upcomingPromotions = [];
$notifications = [];
$reminders = [];

// Load data based on active tab
switch ($activeTab) {
    case 'reports':
        // Load reports data
        include 'modules/reports/reports_data.php';
        break;
    case 'analytics':
        // Load analytics data
        include 'modules/analytics/analytics_data.php';
        break;
    case 'notifications':
        // Load notifications data
        include 'modules/notifications/notifications_data.php';
        break;
    case 'reminders':
        // Load reminders data
        include 'modules/reminders/reminders_data.php';
        break;
}
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb bg-light rounded-modern p-3">
        <li class="breadcrumb-item">
            <a href="dashboard_unified.php" class="text-decoration-none">
                <i class="fas fa-home me-1"></i>لوحة التحكم
            </a>
        </li>
        <li class="breadcrumb-item active" aria-current="page">
            <i class="fas fa-chart-bar me-1"></i>التقارير والتحليلات
        </li>
    </ol>
</nav>

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="display-5 mb-2 text-gradient-primary">
            <i class="fas fa-chart-bar me-2"></i> التقارير والتحليلات الشاملة
        </h1>
        <p class="text-muted">تقارير مفصلة وتحليلات متقدمة ونظام التنبيهات والتذكيرات</p>
    </div>
    <div class="col-md-4 text-md-end">
        <div class="btn-group" role="group">
            <button class="btn btn-outline-secondary btn-print">
                <i class="fas fa-print"></i>
                طباعة
            </button>
            <button class="btn btn-outline-success btn-export">
                <i class="fas fa-file-excel"></i>
                تصدير
            </button>
            <div class="dropdown">
                <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-cog"></i>
                    خيارات
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="refreshData()"><i class="fas fa-sync-alt me-2"></i>تحديث البيانات</a></li>
                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#filtersModal"><i class="fas fa-filter me-2"></i>تصفية متقدمة</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="system_settings.php"><i class="fas fa-sliders-h me-2"></i>إعدادات النظام</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Navigation Tabs -->
<div class="card mb-4 shadow-modern rounded-modern">
    <div class="card-header bg-gradient-primary p-0">
        <ul class="nav nav-tabs nav-tabs-custom" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link <?php echo ($activeTab === 'reports') ? 'active' : ''; ?>"
                        id="reports-tab"
                        data-bs-toggle="tab"
                        data-bs-target="#reports"
                        type="button"
                        role="tab"
                        onclick="changeTab('reports')">
                    <i class="fas fa-file-alt me-2"></i>
                    التقارير
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?php echo ($activeTab === 'analytics') ? 'active' : ''; ?>"
                        id="analytics-tab"
                        data-bs-toggle="tab"
                        data-bs-target="#analytics"
                        type="button"
                        role="tab"
                        onclick="changeTab('analytics')">
                    <i class="fas fa-chart-line me-2"></i>
                    التحليلات المتقدمة
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?php echo ($activeTab === 'notifications') ? 'active' : ''; ?>"
                        id="notifications-tab"
                        data-bs-toggle="tab"
                        data-bs-target="#notifications"
                        type="button"
                        role="tab"
                        onclick="changeTab('notifications')">
                    <i class="fas fa-bell me-2"></i>
                    التنبيهات
                    <?php
                    // Get unread notifications count
                    try {
                        $unreadStmt = $pdo->prepare("SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0");
                        $unreadStmt->execute([$_SESSION['user_id']]);
                        $unreadCount = $unreadStmt->fetch()['count'];
                        if ($unreadCount > 0):
                    ?>
                        <span class="badge bg-danger ms-1"><?php echo $unreadCount; ?></span>
                    <?php endif; } catch (PDOException $e) { /* ignore */ } ?>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?php echo ($activeTab === 'reminders') ? 'active' : ''; ?>"
                        id="reminders-tab"
                        data-bs-toggle="tab"
                        data-bs-target="#reminders"
                        type="button"
                        role="tab"
                        onclick="changeTab('reminders')">
                    <i class="fas fa-clock me-2"></i>
                    التذكيرات
                </button>
            </li>
        </ul>
    </div>

    <div class="card-body p-0">
        <div class="tab-content" id="mainTabsContent">
            <!-- Reports Tab -->
            <div class="tab-pane fade <?php echo ($activeTab === 'reports') ? 'show active' : ''; ?>"
                 id="reports"
                 role="tabpanel"
                 aria-labelledby="reports-tab">
                <?php include 'modules/reports/reports_content.php'; ?>
            </div>

            <!-- Analytics Tab -->
            <div class="tab-pane fade <?php echo ($activeTab === 'analytics') ? 'show active' : ''; ?>"
                 id="analytics"
                 role="tabpanel"
                 aria-labelledby="analytics-tab">
                <?php include 'modules/analytics/analytics_content.php'; ?>
            </div>

            <!-- Notifications Tab -->
            <div class="tab-pane fade <?php echo ($activeTab === 'notifications') ? 'show active' : ''; ?>"
                 id="notifications"
                 role="tabpanel"
                 aria-labelledby="notifications-tab">
                <?php include 'modules/notifications/notifications_content.php'; ?>
            </div>

            <!-- Reminders Tab -->
            <div class="tab-pane fade <?php echo ($activeTab === 'reminders') ? 'show active' : ''; ?>"
                 id="reminders"
                 role="tabpanel"
                 aria-labelledby="reminders-tab">
                <?php include 'modules/reminders/reminders_content.php'; ?>
            </div>
        </div>
    </div>
</div>

<!-- Advanced Filters Modal -->
<div class="modal fade" id="filtersModal" tabindex="-1" aria-labelledby="filtersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-gradient-primary">
                <h5 class="modal-title text-white" id="filtersModalLabel">
                    <i class="fas fa-filter me-2"></i>تصفية متقدمة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="advancedFiltersForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="filterYear" class="form-label">السنة</label>
                            <select class="form-select" id="filterYear" name="year">
                                <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                                    <option value="<?php echo $y; ?>" <?php echo ($year == $y) ? 'selected' : ''; ?>>
                                        <?php echo $y; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="filterMonth" class="form-label">الشهر</label>
                            <select class="form-select" id="filterMonth" name="month">
                                <option value="">جميع الأشهر</option>
                                <?php
                                $months = [
                                    1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
                                    5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
                                    9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
                                ];
                                foreach ($months as $num => $name): ?>
                                    <option value="<?php echo $num; ?>" <?php echo ($month == $num) ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="filterDepartment" class="form-label">القسم</label>
                            <select class="form-select" id="filterDepartment" name="department">
                                <option value="0">جميع الأقسام</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['id']; ?>" <?php echo ($department == $dept['id']) ? 'selected' : ''; ?>>
                                        <?php echo $dept['name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="filterPeriod" class="form-label">الفترة (بالأيام)</label>
                            <input type="number" class="form-control" id="filterPeriod" name="period" min="1" max="365" value="<?php echo $period; ?>">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="applyFilters()">تطبيق التصفية</button>
            </div>
        </div>
    </div>
</div>

<script>
// Tab change functionality
function changeTab(tab) {
    const url = new URL(window.location);
    url.searchParams.set('tab', tab);
    window.history.pushState({}, '', url);
}

// Apply filters
function applyFilters() {
    const form = document.getElementById('advancedFiltersForm');
    const formData = new FormData(form);
    const url = new URL(window.location);

    for (let [key, value] of formData.entries()) {
        if (value) {
            url.searchParams.set(key, value);
        } else {
            url.searchParams.delete(key);
        }
    }

    window.location.href = url.toString();
}

// Refresh data
function refreshData() {
    location.reload();
}

// Auto-refresh every 5 minutes for notifications
setInterval(() => {
    if (document.getElementById('notifications-tab').classList.contains('active')) {
        location.reload();
    }
}, 300000);
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
