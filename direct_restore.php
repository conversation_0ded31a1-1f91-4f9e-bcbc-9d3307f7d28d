<?php
/**
 * Direct Restore Page - استعادة قاعدة البيانات مباشرة
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and admin role
requireRole(['admin']);

// Process restore request
if (isset($_POST['restore_backup']) && isset($_FILES['backup_file'])) {
    try {
        $uploadedFile = $_FILES['backup_file'];
        
        // Check for upload errors
        if ($uploadedFile['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('حدث خطأ أثناء رفع الملف. رمز الخطأ: ' . $uploadedFile['error']);
        }
        
        // Check file type
        $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));
        if ($fileExtension !== 'sql') {
            throw new Exception('نوع الملف غير صالح. يجب أن يكون الملف بتنسيق SQL.');
        }
        
        // Read the SQL file
        $sql = file_get_contents($uploadedFile['tmp_name']);
        
        // Split SQL file into individual queries
        $queries = preg_split('/;\s*$/m', $sql);
        
        // Get database connection
        $pdo = $GLOBALS['pdo']; // Use existing PDO connection
        
        // Begin transaction
        $pdo->beginTransaction();
        
        // Execute each query
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                $pdo->exec($query);
            }
        }
        
        // Commit transaction
        $pdo->commit();
        
        // Log activity
        logActivity(
            $_SESSION['user_id'],
            'استعادة نسخة احتياطية مباشرة',
            'backup',
            0,
            'تم استعادة النسخة الاحتياطية المباشرة: ' . $uploadedFile['name']
        );
        
        flash('success_message', 'تم استعادة النسخة الاحتياطية بنجاح', 'alert alert-success');
    } catch (Exception $e) {
        // Rollback transaction on error
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        
        error_log("Direct Restore Error: " . $e->getMessage());
        flash('error_message', 'حدث خطأ أثناء استعادة النسخة الاحتياطية: ' . $e->getMessage(), 'alert alert-danger');
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-upload me-2"></i> استعادة قاعدة البيانات مباشرة
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى الرئيسية
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mx-auto">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-upload me-2"></i> استعادة نسخة احتياطية
                </h5>
            </div>
            <div class="card-body">
                <p>قم باستعادة قاعدة البيانات من نسخة احتياطية سابقة. سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.</p>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> استعادة النسخة الاحتياطية ستؤدي إلى حذف جميع البيانات الحالية واستبدالها بالبيانات من النسخة الاحتياطية.
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> لإنشاء نسخة احتياطية، انقر على الزر أدناه ثم احفظ الملف الذي سيتم تنزيله.
                </div>
                
                <div class="mb-4 text-center">
                    <a href="direct_backup.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-download me-1"></i> إنشاء نسخة احتياطية
                    </a>
                </div>
                
                <hr>
                
                <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" enctype="multipart/form-data" id="restoreForm">
                    <div class="mb-3">
                        <label for="backup_file" class="form-label">اختر ملف النسخة الاحتياطية (SQL)</label>
                        <input type="file" class="form-control" id="backup_file" name="backup_file" accept=".sql" required>
                    </div>
                    <button type="submit" name="restore_backup" class="btn btn-warning w-100" id="restoreBackupBtn">
                        <i class="fas fa-upload me-1"></i> استعادة النسخة الاحتياطية
                    </button>
                </form>
                
                <!-- Progress bar (hidden by default) -->
                <div class="progress mt-3" id="restoreProgress" style="display: none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" role="progressbar" style="width: 0%"></div>
                </div>
                <small class="text-muted mt-2" id="restoreStatus" style="display: none;">جاري استعادة النسخة الاحتياطية...</small>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>

<script>
// Restore progress simulation
document.addEventListener('DOMContentLoaded', function() {
    // Restore form elements
    const restoreForm = document.getElementById('restoreForm');
    const restoreBackupBtn = document.getElementById('restoreBackupBtn');
    const restoreProgress = document.getElementById('restoreProgress');
    const restoreProgressBar = restoreProgress.querySelector('.progress-bar');
    const restoreStatus = document.getElementById('restoreStatus');
    
    // Restore form submission
    if (restoreForm) {
        restoreForm.addEventListener('submit', function(e) {
            // Show confirmation dialog
            if (!confirm('هل أنت متأكد من رغبتك في استعادة هذه النسخة الاحتياطية؟ سيتم حذف جميع البيانات الحالية.')) {
                e.preventDefault();
                return false;
            }
            
            // Show progress bar and disable button
            restoreProgress.style.display = 'flex';
            restoreStatus.style.display = 'block';
            restoreBackupBtn.disabled = true;
            restoreBackupBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري استعادة النسخة الاحتياطية...';
            
            // Simulate progress
            let progress = 0;
            const interval = setInterval(function() {
                progress += Math.random() * 10;
                if (progress > 100) progress = 100;
                
                restoreProgressBar.style.width = progress + '%';
                restoreProgressBar.setAttribute('aria-valuenow', progress);
                
                if (progress < 30) {
                    restoreStatus.textContent = 'جاري قراءة ملف النسخة الاحتياطية...';
                } else if (progress < 60) {
                    restoreStatus.textContent = 'جاري إعادة إنشاء هيكل الجداول...';
                } else if (progress < 90) {
                    restoreStatus.textContent = 'جاري استيراد البيانات...';
                } else {
                    restoreStatus.textContent = 'جاري إكمال عملية الاستعادة...';
                }
                
                if (progress === 100) {
                    clearInterval(interval);
                    // Form will submit normally and page will refresh
                }
            }, 500);
        });
    }
});
</script>
