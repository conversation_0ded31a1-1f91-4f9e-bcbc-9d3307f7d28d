<!-- Reminders Content -->
<div class="p-4">
    <!-- Reminders Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="reminder-stat-card pending">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo isset($pendingReminders) ? $pendingReminders : 0; ?></div>
                    <div class="stat-label">تذكيرات معلقة</div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="reminder-stat-card overdue">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo isset($overdueReminders) ? $overdueReminders : 0; ?></div>
                    <div class="stat-label">تذكيرات متأخرة</div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="reminder-stat-card today">
                <div class="stat-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo isset($todayReminders) ? $todayReminders : 0; ?></div>
                    <div class="stat-label">تذكيرات اليوم</div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="reminder-stat-card completed">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo isset($completedReminders) ? $completedReminders : 0; ?></div>
                    <div class="stat-label">تذكيرات مكتملة</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reminder Actions -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="btn-group" role="group">
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createReminderModal">
                    <i class="fas fa-plus me-1"></i>
                    إنشاء تذكير جديد
                </button>

                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-filter me-1"></i>
                        تصفية
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="filterReminders('all')">جميع التذكيرات</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterReminders('pending')">معلقة فقط</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterReminders('overdue')">متأخرة فقط</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterReminders('today')">اليوم فقط</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterReminders('completed')">مكتملة فقط</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="filterReminders('high')">أولوية عالية</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterReminders('medium')">أولوية متوسطة</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterReminders('low')">أولوية منخفضة</a></li>
                    </ul>
                </div>

                <div class="dropdown">
                    <button class="btn btn-outline-info dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-tags me-1"></i>
                        الفئات
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="filterReminders('allowances')">العلاوات</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterReminders('promotions')">الترفيعات</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterReminders('appreciation')">كتب الشكر</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterReminders('employees')">الموظفين</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterReminders('meetings')">الاجتماعات</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filterReminders('reports')">التقارير</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-md-4 text-md-end">
            <div class="input-group">
                <input type="text" class="form-control" id="reminderSearch" placeholder="البحث في التذكيرات...">
                <button class="btn btn-outline-secondary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Reminders List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-modern rounded-modern">
                <div class="card-header bg-gradient-primary">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-clock me-2"></i>قائمة التذكيرات
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (count($reminders) > 0): ?>
                        <div class="reminders-list">
                            <?php foreach ($reminders as $reminder): ?>
                                <?php
                                $reminderDateTime = $reminder['reminder_date'] . ' ' . $reminder['reminder_time'];
                                $isOverdue = !$reminder['is_completed'] && strtotime($reminderDateTime) < time();
                                $isToday = $reminder['reminder_date'] === date('Y-m-d');
                                ?>

                                <div class="reminder-item <?php echo $reminder['is_completed'] ? 'completed' : ''; ?> <?php echo $isOverdue ? 'overdue' : ''; ?>"
                                     data-priority="<?php echo $reminder['priority']; ?>"
                                     data-category="<?php echo $reminder['category']; ?>"
                                     data-date="<?php echo $reminder['reminder_date']; ?>"
                                     data-status="<?php echo $reminder['is_completed'] ? 'completed' : 'pending'; ?>">

                                    <div class="reminder-content">
                                        <div class="reminder-header">
                                            <div class="reminder-priority">
                                                <?php
                                                $priorityClass = match($reminder['priority']) {
                                                    'high' => 'text-danger',
                                                    'medium' => 'text-warning',
                                                    'low' => 'text-success',
                                                    default => 'text-secondary'
                                                };
                                                $priorityIcon = match($reminder['priority']) {
                                                    'high' => 'fas fa-exclamation-circle',
                                                    'medium' => 'fas fa-minus-circle',
                                                    'low' => 'fas fa-info-circle',
                                                    default => 'fas fa-circle'
                                                };
                                                ?>
                                                <i class="<?php echo $priorityIcon . ' ' . $priorityClass; ?>"></i>
                                            </div>

                                            <div class="reminder-meta">
                                                <h6 class="reminder-title"><?php echo $reminder['title']; ?></h6>
                                                <div class="reminder-info">
                                                    <span class="reminder-datetime">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        <?php echo formatArabicDate($reminder['reminder_date']); ?>
                                                        <i class="fas fa-clock me-1 ms-2"></i>
                                                        <?php echo date('H:i', strtotime($reminder['reminder_time'])); ?>
                                                    </span>
                                                    <span class="reminder-creator">
                                                        <i class="fas fa-user me-1"></i>
                                                        <?php echo $reminder['created_by_name'] ?? 'النظام'; ?>
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="reminder-actions">
                                                <?php if (!$reminder['is_completed']): ?>
                                                    <form method="post" class="d-inline">
                                                        <input type="hidden" name="action" value="complete">
                                                        <input type="hidden" name="reminder_id" value="<?php echo $reminder['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-success" title="تحديد كمكتمل">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>

                                                <form method="post" class="d-inline">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="reminder_id" value="<?php echo $reminder['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-outline-danger"
                                                            title="حذف"
                                                            onclick="return confirm('هل أنت متأكد من حذف هذا التذكير؟')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>

                                        <?php if (!empty($reminder['description'])): ?>
                                            <div class="reminder-description">
                                                <?php echo $reminder['description']; ?>
                                            </div>
                                        <?php endif; ?>

                                        <div class="reminder-footer">
                                            <span class="reminder-category-badge badge bg-<?php
                                                echo match($reminder['category']) {
                                                    'allowances' => 'success',
                                                    'promotions' => 'info',
                                                    'appreciation' => 'warning',
                                                    'employees' => 'primary',
                                                    'meetings' => 'secondary',
                                                    'reports' => 'dark',
                                                    default => 'light text-dark'
                                                };
                                            ?>">
                                                <?php
                                                echo match($reminder['category']) {
                                                    'allowances' => 'علاوات',
                                                    'promotions' => 'ترفيعات',
                                                    'appreciation' => 'كتب شكر',
                                                    'employees' => 'موظفين',
                                                    'meetings' => 'اجتماعات',
                                                    'reports' => 'تقارير',
                                                    default => 'نظام'
                                                };
                                                ?>
                                            </span>

                                            <span class="reminder-priority-badge badge bg-<?php
                                                echo match($reminder['priority']) {
                                                    'high' => 'danger',
                                                    'medium' => 'warning',
                                                    'low' => 'success',
                                                    default => 'secondary'
                                                };
                                            ?>">
                                                <?php
                                                echo match($reminder['priority']) {
                                                    'high' => 'عالية',
                                                    'medium' => 'متوسطة',
                                                    'low' => 'منخفضة',
                                                    default => 'عادية'
                                                };
                                                ?>
                                            </span>

                                            <?php if ($reminder['is_completed']): ?>
                                                <span class="badge bg-success">مكتمل</span>
                                            <?php elseif ($isOverdue): ?>
                                                <span class="badge bg-danger">متأخر</span>
                                            <?php elseif ($isToday): ?>
                                                <span class="badge bg-warning">اليوم</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-clock fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد تذكيرات</h4>
                            <p class="text-muted">لم يتم العثور على أي تذكيرات حتى الآن</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createReminderModal">
                                <i class="fas fa-plus me-1"></i>
                                إنشاء تذكير جديد
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Reminder Modal -->
<div class="modal fade" id="createReminderModal" tabindex="-1" aria-labelledby="createReminderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-gradient-primary">
                <h5 class="modal-title text-white" id="createReminderModalLabel">
                    <i class="fas fa-plus me-2"></i>إنشاء تذكير جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create">

                    <div class="row mb-3">
                        <div class="col-md-8">
                            <label for="reminderTitle" class="form-label">عنوان التذكير</label>
                            <input type="text" class="form-control" id="reminderTitle" name="title" required>
                        </div>
                        <div class="col-md-4">
                            <label for="reminderPriority" class="form-label">الأولوية</label>
                            <select class="form-select" id="reminderPriority" name="priority" required>
                                <option value="low">منخفضة</option>
                                <option value="medium" selected>متوسطة</option>
                                <option value="high">عالية</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="reminderDescription" class="form-label">وصف التذكير</label>
                        <textarea class="form-control" id="reminderDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="reminderDate" class="form-label">تاريخ التذكير</label>
                            <input type="date" class="form-control" id="reminderDate" name="reminder_date" required>
                        </div>
                        <div class="col-md-6">
                            <label for="reminderTime" class="form-label">وقت التذكير</label>
                            <input type="time" class="form-control" id="reminderTime" name="reminder_time" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="reminderCategory" class="form-label">الفئة</label>
                            <select class="form-select" id="reminderCategory" name="category" required>
                                <option value="system">نظام</option>
                                <option value="allowances">علاوات</option>
                                <option value="promotions">ترفيعات</option>
                                <option value="appreciation">كتب شكر</option>
                                <option value="employees">موظفين</option>
                                <option value="meetings">اجتماعات</option>
                                <option value="reports">تقارير</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="targetUser" class="form-label">المستخدم المستهدف</label>
                            <select class="form-select" id="targetUser" name="target_user">
                                <option value="<?php echo $_SESSION['user_id']; ?>">أنا</option>
                                <?php if (hasRole(['admin', 'hr_manager'])): ?>
                                    <?php
                                    try {
                                        $usersStmt = $pdo->query("SELECT id, full_name, username FROM users WHERE is_active = 1 ORDER BY full_name ASC");
                                        $users = $usersStmt->fetchAll();
                                        foreach ($users as $user):
                                            if ($user['id'] != $_SESSION['user_id']):
                                    ?>
                                        <option value="<?php echo $user['id']; ?>">
                                            <?php echo $user['full_name'] . ' (' . $user['username'] . ')'; ?>
                                        </option>
                                    <?php endif; endforeach; } catch (Exception $e) { /* ignore */ } ?>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إنشاء التذكير</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Set default date and time
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('reminderDate');
    const timeInput = document.getElementById('reminderTime');

    // Set default date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    dateInput.value = tomorrow.toISOString().split('T')[0];

    // Set default time to 9:00 AM
    timeInput.value = '09:00';
});

// Reminder filtering
function filterReminders(filter) {
    const reminders = document.querySelectorAll('.reminder-item');

    reminders.forEach(reminder => {
        let show = true;

        switch (filter) {
            case 'pending':
                show = reminder.dataset.status === 'pending';
                break;
            case 'completed':
                show = reminder.dataset.status === 'completed';
                break;
            case 'overdue':
                show = reminder.classList.contains('overdue');
                break;
            case 'today':
                const today = new Date().toISOString().split('T')[0];
                show = reminder.dataset.date === today;
                break;
            case 'high':
            case 'medium':
            case 'low':
                show = reminder.dataset.priority === filter;
                break;
            case 'all':
                show = true;
                break;
            default:
                show = reminder.dataset.category === filter;
        }

        reminder.style.display = show ? 'block' : 'none';
    });
}

// Search functionality
document.getElementById('reminderSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const reminders = document.querySelectorAll('.reminder-item');

    reminders.forEach(reminder => {
        const title = reminder.querySelector('.reminder-title').textContent.toLowerCase();
        const description = reminder.querySelector('.reminder-description')?.textContent.toLowerCase() || '';
        const creator = reminder.querySelector('.reminder-creator').textContent.toLowerCase();

        const matches = title.includes(searchTerm) || description.includes(searchTerm) || creator.includes(searchTerm);
        reminder.style.display = matches ? 'block' : 'none';
    });
});
</script>
