<?php
/**
 * Check Table Structure
 * التحقق من هيكل الجداول
 */

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once 'config/database.php';

echo "<h1>التحقق من هيكل الجداول</h1>";

try {
    // Check users table structure
    $tableName = 'users';
    echo "<h2>هيكل جدول $tableName</h2>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color:green;'>جدول $tableName موجود.</p>";
        
        // Get table structure
        $stmt = $pdo->query("DESCRIBE $tableName");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>الحقل</th><th>النوع</th><th>Null</th><th>المفتاح</th><th>القيمة الافتراضية</th><th>إضافي</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Check if there are any users
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $tableName");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        echo "<p>عدد المستخدمين: $count</p>";
        
        if ($count > 0) {
            // Show users
            $stmt = $pdo->query("SELECT id, username, full_name, email, role, status FROM $tableName");
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>المستخدمون الحاليون:</h3>";
            echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>البريد الإلكتروني</th><th>الدور</th><th>الحالة</th></tr>";
            
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>" . $user['id'] . "</td>";
                echo "<td>" . $user['username'] . "</td>";
                echo "<td>" . $user['full_name'] . "</td>";
                echo "<td>" . $user['email'] . "</td>";
                echo "<td>" . $user['role'] . "</td>";
                echo "<td>" . $user['status'] . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        }
    } else {
        echo "<p style='color:red;'>جدول $tableName غير موجود!</p>";
    }
    
    // Create SQL for users table if it doesn't exist
    echo "<h2>إنشاء جدول المستخدمين</h2>";
    echo "<form method='post'>";
    echo "<input type='submit' name='create_users_table' value='إنشاء جدول المستخدمين' style='padding: 5px 10px;'>";
    echo "</form>";
    
    if (isset($_POST['create_users_table'])) {
        $createTableSQL = "
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL,
                role ENUM('admin', 'hr', 'manager', 'viewer') NOT NULL DEFAULT 'viewer',
                status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
                last_login DATETIME,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        try {
            $pdo->exec($createTableSQL);
            echo "<p style='color:green;'>تم إنشاء جدول المستخدمين بنجاح!</p>";
            
            // Create admin user
            $username = 'admin';
            $password = 'admin123';
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            $stmt = $pdo->prepare("
                INSERT INTO users (username, password, full_name, email, role)
                VALUES (?, ?, ?, ?, 'admin')
            ");
            
            $stmt->execute([$username, $hashedPassword, 'مدير النظام', '<EMAIL>']);
            
            echo "<p style='color:green;'>تم إنشاء مستخدم المدير بنجاح!</p>";
            echo "<p>اسم المستخدم: $username</p>";
            echo "<p>كلمة المرور: $password</p>";
            
            // Refresh page
            echo "<meta http-equiv='refresh' content='2'>";
        } catch (PDOException $e) {
            echo "<p style='color:red;'>خطأ في إنشاء جدول المستخدمين: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color:red;'>خطأ: " . $e->getMessage() . "</p>";
}

// Add link to go back to the main page
echo "<p><a href='index.php' style='display: inline-block; margin-top: 20px; padding: 5px 10px; background-color: #6c757d; color: white; text-decoration: none;'>العودة إلى الصفحة الرئيسية</a></p>";
?>
